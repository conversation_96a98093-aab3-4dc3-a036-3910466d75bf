/**
 * CloudForge Platform - Dashboard Page
 * Enterprise-grade cloud services platform
 */

import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  useTheme,
} from '@mui/material';
import {
  TrendingUp,
  People,
  AttachMoney,
  Notifications,
  CloudQueue,
  Security,
} from '@mui/icons-material';

import { StatsCard } from '../components/Dashboard/StatsCard';
import { ChartCard } from '../components/Dashboard/ChartCard';
import { RecentActivity } from '../components/Dashboard/RecentActivity';
import { SystemHealth } from '../components/Dashboard/SystemHealth';
import { QuickActions } from '../components/Dashboard/QuickActions';

const DashboardPage: React.FC = () => {
  const theme = useTheme();

  // Mock data - in real app, this would come from API
  const stats = [
    {
      title: 'Total Users',
      value: '12,847',
      change: '+12.5%',
      trend: 'up' as const,
      icon: People,
      color: theme.palette.primary.main,
    },
    {
      title: 'Monthly Revenue',
      value: '$89,432',
      change: '+8.2%',
      trend: 'up' as const,
      icon: AttachMoney,
      color: theme.palette.success.main,
    },
    {
      title: 'Active Subscriptions',
      value: '3,247',
      change: '+5.1%',
      trend: 'up' as const,
      icon: TrendingUp,
      color: theme.palette.info.main,
    },
    {
      title: 'Cloud Resources',
      value: '1,892',
      change: '-2.3%',
      trend: 'down' as const,
      icon: CloudQueue,
      color: theme.palette.warning.main,
    },
    {
      title: 'Security Alerts',
      value: '23',
      change: '+15.7%',
      trend: 'up' as const,
      icon: Security,
      color: theme.palette.error.main,
    },
    {
      title: 'Notifications Sent',
      value: '45,123',
      change: '+22.1%',
      trend: 'up' as const,
      icon: Notifications,
      color: theme.palette.secondary.main,
    },
  ];

  const revenueData = [
    { month: 'Jan', revenue: 65000, users: 1200 },
    { month: 'Feb', revenue: 72000, users: 1350 },
    { month: 'Mar', revenue: 68000, users: 1280 },
    { month: 'Apr', revenue: 78000, users: 1420 },
    { month: 'May', revenue: 85000, users: 1580 },
    { month: 'Jun', revenue: 89432, users: 1647 },
  ];

  const userGrowthData = [
    { month: 'Jan', active: 8500, new: 450, churned: 120 },
    { month: 'Feb', active: 9200, new: 520, churned: 140 },
    { month: 'Mar', active: 9800, new: 480, churned: 110 },
    { month: 'Apr', active: 10500, new: 580, churned: 130 },
    { month: 'May', active: 11800, new: 620, churned: 95 },
    { month: 'Jun', active: 12847, new: 687, churned: 108 },
  ];

  return (
    <Box>
      {/* Page Header */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Welcome back! Here's what's happening with your platform.
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
            <StatsCard {...stat} />
          </Grid>
        ))}
      </Grid>

      {/* Charts and Analytics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} lg={8}>
          <ChartCard
            title="Revenue & User Growth"
            subtitle="Monthly revenue and user acquisition trends"
            data={revenueData}
            type="line"
            height={400}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          <SystemHealth />
        </Grid>
      </Grid>

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} lg={6}>
          <ChartCard
            title="User Growth Analysis"
            subtitle="Active users, new signups, and churn rate"
            data={userGrowthData}
            type="bar"
            height={350}
          />
        </Grid>
        <Grid item xs={12} lg={6}>
          <RecentActivity />
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <QuickActions />
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
