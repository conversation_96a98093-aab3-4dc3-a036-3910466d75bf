# CloudForge Platform - Prometheus Configuration
# Enterprise-grade cloud services platform

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'cloudforge-platform'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "rules/*.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Auth Service
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:3001']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # User Service
  - job_name: 'user-service'
    static_configs:
      - targets: ['user-service:3002']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Billing Service
  - job_name: 'billing-service'
    static_configs:
      - targets: ['billing-service:3003']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Notification Service
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service:3004']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Monitoring Service
  - job_name: 'monitoring-service'
    static_configs:
      - targets: ['monitoring-service:3005']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (System metrics)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # cAdvisor (Container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx metrics (if using nginx)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Application-specific metrics
  - job_name: 'cloudforge-business-metrics'
    static_configs:
      - targets: ['api-gateway:3000']
    scrape_interval: 60s
    metrics_path: /metrics/business
    scrape_timeout: 30s

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Remote read configuration
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    path: /prometheus
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Web configuration
web:
  console.templates: /etc/prometheus/consoles
  console.libraries: /etc/prometheus/console_libraries
  enable-lifecycle: true
  enable-admin-api: true
  max-connections: 512
  read-timeout: 30s
  route-prefix: /

# Feature flags
feature_flags:
  - name: "promql-at-modifier"
    enabled: true
  - name: "promql-negative-offset"
    enabled: true
