/**
 * CloudForge Platform - Invoice Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { User } from './user.entity';
import { Subscription } from './subscription.entity';
import { Payment } from './payment.entity';

export enum InvoiceStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

@Entity('invoices')
@Index(['userId'])
@Index(['subscriptionId'])
@Index(['status'])
@Index(['dueDate'])
@Index(['invoiceNumber'], { unique: true })
export class Invoice extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: 'Unique invoice number',
  })
  invoiceNumber: string;

  @Column({
    type: 'uuid',
    comment: 'User who owns this invoice',
  })
  userId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Related subscription',
  })
  subscriptionId?: string;

  @Column({
    type: 'enum',
    enum: InvoiceStatus,
    default: InvoiceStatus.PENDING,
    comment: 'Invoice status',
  })
  status: InvoiceStatus;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: 'Subtotal amount',
  })
  subtotal: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: 'Tax amount',
  })
  taxAmount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: 'Discount amount',
  })
  discountAmount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: 'Total amount',
  })
  total: number;

  @Column({
    type: 'varchar',
    length: 3,
    default: 'USD',
    comment: 'Currency code',
  })
  currency: string;

  @Column({
    type: 'timestamp with time zone',
    comment: 'Invoice due date',
  })
  dueDate: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Date when invoice was paid',
  })
  paidAt?: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'External invoice ID',
  })
  externalId?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Invoice line items',
  })
  lineItems?: Record<string, any>[];

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Invoice metadata',
  })
  invoiceMetadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Subscription)
  @JoinColumn({ name: 'subscriptionId' })
  subscription?: Subscription;

  @OneToMany(() => Payment, payment => payment.invoice)
  payments: Payment[];

  // Methods
  isPaid(): boolean {
    return this.status === InvoiceStatus.PAID;
  }

  isOverdue(): boolean {
    return this.status === InvoiceStatus.OVERDUE || 
           (this.status === InvoiceStatus.PENDING && this.dueDate < new Date());
  }

  markAsPaid(): void {
    this.status = InvoiceStatus.PAID;
    this.paidAt = new Date();
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    obj.isPaid = this.isPaid();
    obj.isOverdue = this.isOverdue();
    return obj;
  }
}
