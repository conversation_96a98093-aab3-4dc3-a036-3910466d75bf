import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  Param,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MetricsService } from './metrics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/enums/user-role.enum';

@ApiTags('metrics')
@Controller('metrics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  @Get('system')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MONITORING_MANAGER)
  @ApiOperation({ summary: 'Get system metrics' })
  @ApiResponse({ status: 200, description: 'System metrics retrieved successfully' })
  async getSystemMetrics(
    @Query('timeRange') timeRange: string = '1h',
    @Query('interval') interval: string = '5m',
  ) {
    return this.metricsService.getSystemMetrics({ timeRange, interval });
  }

  @Get('business')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.BUSINESS_ANALYST)
  @ApiOperation({ summary: 'Get business metrics' })
  @ApiResponse({ status: 200, description: 'Business metrics retrieved successfully' })
  async getBusinessMetrics(
    @Query('timeRange') timeRange: string = '24h',
    @Query('interval') interval: string = '1h',
  ) {
    return this.metricsService.getBusinessMetrics({ timeRange, interval });
  }

  @Get('performance')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MONITORING_MANAGER)
  @ApiOperation({ summary: 'Get performance metrics' })
  @ApiResponse({ status: 200, description: 'Performance metrics retrieved successfully' })
  async getPerformanceMetrics(
    @Query('service') service?: string,
    @Query('timeRange') timeRange: string = '1h',
  ) {
    return this.metricsService.getPerformanceMetrics({ service, timeRange });
  }

  @Get('real-time')
  @ApiOperation({ summary: 'Get real-time metrics' })
  @ApiResponse({ status: 200, description: 'Real-time metrics retrieved successfully' })
  async getRealTimeMetrics() {
    return this.metricsService.getRealTimeMetrics();
  }

  @Get('dashboard/:dashboardId')
  @ApiOperation({ summary: 'Get dashboard metrics' })
  @ApiResponse({ status: 200, description: 'Dashboard metrics retrieved successfully' })
  async getDashboardMetrics(
    @Param('dashboardId') dashboardId: string,
    @Query('timeRange') timeRange: string = '1h',
  ) {
    return this.metricsService.getDashboardMetrics(dashboardId, { timeRange });
  }

  @Post('custom')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MONITORING_MANAGER)
  @ApiOperation({ summary: 'Record custom metric' })
  @ApiResponse({ status: 201, description: 'Custom metric recorded successfully' })
  async recordCustomMetric(@Body() metricData: any) {
    return this.metricsService.recordCustomMetric(metricData);
  }

  @Get('alerts/active')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MONITORING_MANAGER)
  @ApiOperation({ summary: 'Get active alerts' })
  @ApiResponse({ status: 200, description: 'Active alerts retrieved successfully' })
  async getActiveAlerts() {
    return this.metricsService.getActiveAlerts();
  }

  @Get('health/services')
  @ApiOperation({ summary: 'Get service health status' })
  @ApiResponse({ status: 200, description: 'Service health status retrieved successfully' })
  async getServiceHealth() {
    return this.metricsService.getServiceHealth();
  }

  @Get('usage/api')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.API_MANAGER)
  @ApiOperation({ summary: 'Get API usage metrics' })
  @ApiResponse({ status: 200, description: 'API usage metrics retrieved successfully' })
  async getApiUsage(
    @Query('timeRange') timeRange: string = '24h',
    @Query('endpoint') endpoint?: string,
  ) {
    return this.metricsService.getApiUsage({ timeRange, endpoint });
  }

  @Get('users/activity')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.USER_MANAGER)
  @ApiOperation({ summary: 'Get user activity metrics' })
  @ApiResponse({ status: 200, description: 'User activity metrics retrieved successfully' })
  async getUserActivity(
    @Query('timeRange') timeRange: string = '24h',
    @Query('userId') userId?: string,
  ) {
    return this.metricsService.getUserActivity({ timeRange, userId });
  }

  @Get('billing/revenue')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.BILLING_MANAGER)
  @ApiOperation({ summary: 'Get revenue metrics' })
  @ApiResponse({ status: 200, description: 'Revenue metrics retrieved successfully' })
  async getRevenueMetrics(
    @Query('timeRange') timeRange: string = '30d',
    @Query('granularity') granularity: string = 'day',
  ) {
    return this.metricsService.getRevenueMetrics({ timeRange, granularity });
  }

  @Get('export')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MONITORING_MANAGER)
  @ApiOperation({ summary: 'Export metrics data' })
  @ApiResponse({ status: 200, description: 'Metrics data exported successfully' })
  async exportMetrics(
    @Query('format') format: string = 'json',
    @Query('timeRange') timeRange: string = '24h',
    @Query('metrics') metrics?: string,
  ) {
    return this.metricsService.exportMetrics({ format, timeRange, metrics });
  }
}
