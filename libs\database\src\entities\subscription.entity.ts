/**
 * CloudForge Platform - Subscription Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { User } from './user.entity';
import { BillingPlan } from './billing-plan.entity';
import { Invoice } from './invoice.entity';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CANCELLED = 'cancelled',
  PAST_DUE = 'past_due',
  TRIALING = 'trialing',
  PAUSED = 'paused',
}

@Entity('subscriptions')
@Index(['userId'])
@Index(['planId'])
@Index(['status'])
@Index(['currentPeriodStart'])
@Index(['currentPeriodEnd'])
@Index(['trialEnd'])
export class Subscription extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'User who owns this subscription',
  })
  userId: string;

  @Column({
    type: 'uuid',
    comment: 'Billing plan for this subscription',
  })
  planId: string;

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.ACTIVE,
    comment: 'Current subscription status',
  })
  status: SubscriptionStatus;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'External subscription ID (e.g., Stripe subscription ID)',
  })
  externalId?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'External customer ID (e.g., Stripe customer ID)',
  })
  externalCustomerId?: string;

  @Column({
    type: 'timestamp with time zone',
    comment: 'Current billing period start date',
  })
  currentPeriodStart: Date;

  @Column({
    type: 'timestamp with time zone',
    comment: 'Current billing period end date',
  })
  currentPeriodEnd: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Trial period end date',
  })
  trialEnd?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Date when subscription was cancelled',
  })
  cancelledAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Date when subscription will end (for cancelled subscriptions)',
  })
  endsAt?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether to cancel at the end of the current period',
  })
  cancelAtPeriodEnd: boolean;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'Reason for cancellation',
  })
  cancellationReason?: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Monthly recurring revenue amount',
  })
  mrrAmount?: number;

  @Column({
    type: 'varchar',
    length: 3,
    default: 'USD',
    comment: 'Currency code',
  })
  currency: string;

  @Column({
    type: 'int',
    default: 1,
    comment: 'Quantity of the subscription (for per-seat pricing)',
  })
  quantity: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
    comment: 'Discount percentage applied',
  })
  discountPercentage?: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Fixed discount amount',
  })
  discountAmount?: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Coupon or promo code applied',
  })
  couponCode?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Usage limits and current usage',
  })
  usage?: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional subscription metadata',
  })
  subscriptionMetadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User, user => user.subscriptions)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => BillingPlan, plan => plan.subscriptions)
  @JoinColumn({ name: 'planId' })
  plan: BillingPlan;

  @OneToMany(() => Invoice, invoice => invoice.subscription)
  invoices: Invoice[];

  // Hooks
  @BeforeInsert()
  @BeforeUpdate()
  calculateMrr(): void {
    if (this.plan && this.plan.price && this.quantity) {
      let baseAmount = this.plan.price * this.quantity;
      
      // Apply discounts
      if (this.discountPercentage) {
        baseAmount = baseAmount * (1 - this.discountPercentage / 100);
      }
      
      if (this.discountAmount) {
        baseAmount = Math.max(0, baseAmount - this.discountAmount);
      }
      
      // Convert to monthly amount based on interval
      switch (this.plan.interval) {
        case 'year':
          this.mrrAmount = baseAmount / 12;
          break;
        case 'month':
        default:
          this.mrrAmount = baseAmount;
          break;
      }
    }
  }

  // Methods
  isActive(): boolean {
    return this.status === SubscriptionStatus.ACTIVE;
  }

  isTrialing(): boolean {
    return this.status === SubscriptionStatus.TRIALING || 
           (this.trialEnd && this.trialEnd > new Date());
  }

  isCancelled(): boolean {
    return this.status === SubscriptionStatus.CANCELLED;
  }

  isPastDue(): boolean {
    return this.status === SubscriptionStatus.PAST_DUE;
  }

  isInCurrentPeriod(): boolean {
    const now = new Date();
    return now >= this.currentPeriodStart && now <= this.currentPeriodEnd;
  }

  getDaysUntilRenewal(): number {
    const now = new Date();
    const diffTime = this.currentPeriodEnd.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getDaysInTrial(): number {
    if (!this.trialEnd) return 0;
    
    const now = new Date();
    const diffTime = this.trialEnd.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }

  cancel(reason?: string, immediately: boolean = false): void {
    this.status = SubscriptionStatus.CANCELLED;
    this.cancelledAt = new Date();
    this.cancellationReason = reason;
    
    if (immediately) {
      this.endsAt = new Date();
    } else {
      this.cancelAtPeriodEnd = true;
      this.endsAt = this.currentPeriodEnd;
    }
  }

  reactivate(): void {
    if (this.isCancelled()) {
      this.status = SubscriptionStatus.ACTIVE;
      this.cancelledAt = null;
      this.endsAt = null;
      this.cancelAtPeriodEnd = false;
      this.cancellationReason = null;
    }
  }

  pause(): void {
    if (this.isActive()) {
      this.status = SubscriptionStatus.PAUSED;
    }
  }

  resume(): void {
    if (this.status === SubscriptionStatus.PAUSED) {
      this.status = SubscriptionStatus.ACTIVE;
    }
  }

  updateUsage(resource: string, amount: number): void {
    if (!this.usage) {
      this.usage = {};
    }
    this.usage[resource] = amount;
  }

  getUsage(resource: string): number {
    return this.usage?.[resource] || 0;
  }

  hasExceededLimit(resource: string): boolean {
    if (!this.plan?.limits) return false;
    
    const limit = this.plan.limits[resource];
    const usage = this.getUsage(resource);
    
    return limit !== -1 && usage > limit; // -1 means unlimited
  }

  getUsagePercentage(resource: string): number {
    if (!this.plan?.limits) return 0;
    
    const limit = this.plan.limits[resource];
    if (limit === -1) return 0; // Unlimited
    
    const usage = this.getUsage(resource);
    return Math.min(100, (usage / limit) * 100);
  }

  applyDiscount(percentage?: number, amount?: number, couponCode?: string): void {
    this.discountPercentage = percentage;
    this.discountAmount = amount;
    this.couponCode = couponCode;
    this.calculateMrr();
  }

  removeDiscount(): void {
    this.discountPercentage = null;
    this.discountAmount = null;
    this.couponCode = null;
    this.calculateMrr();
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    
    // Add computed fields
    obj.isActive = this.isActive();
    obj.isTrialing = this.isTrialing();
    obj.isCancelled = this.isCancelled();
    obj.isPastDue = this.isPastDue();
    obj.daysUntilRenewal = this.getDaysUntilRenewal();
    obj.daysInTrial = this.getDaysInTrial();
    
    return obj;
  }
}
