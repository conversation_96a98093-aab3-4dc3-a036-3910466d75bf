/**
 * CloudForge Platform - Health Service
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as os from 'os';
import * as fs from 'fs';
import { promisify } from 'util';

const stat = promisify(fs.stat);

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();

  constructor(private configService: ConfigService) {}

  /**
   * Get readiness status - indicates if the application is ready to serve traffic
   */
  async getReadinessStatus() {
    try {
      // Check if all critical dependencies are available
      const dependencies = await this.checkDependencies();
      
      const isReady = Object.values(dependencies).every(status => status === 'healthy');
      
      return {
        status: isReady ? 'ready' : 'not_ready',
        timestamp: new Date().toISOString(),
        dependencies,
      };
    } catch (error) {
      this.logger.error('Readiness check failed', error);
      return {
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * Get liveness status - indicates if the application is alive
   */
  async getLivenessStatus() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: this.getUptime(),
      pid: process.pid,
    };
  }

  /**
   * Get detailed health information
   */
  async getDetailedHealth() {
    try {
      const [dependencies, systemInfo] = await Promise.all([
        this.checkDependencies(),
        this.getSystemInfo(),
      ]);

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: this.getUptime(),
        version: this.configService.get<string>('app.version', '1.0.0'),
        environment: this.configService.get<string>('app.environment', 'development'),
        service: 'api-gateway',
        dependencies,
        system: systemInfo,
        configuration: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
        },
      };
    } catch (error) {
      this.logger.error('Detailed health check failed', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * Check the status of critical dependencies
   */
  private async checkDependencies() {
    const dependencies = {
      database: 'unknown',
      redis: 'unknown',
      external: 'unknown',
    };

    try {
      // Database check would go here
      // For now, we'll assume it's healthy if the service is running
      dependencies.database = 'healthy';
    } catch (error) {
      this.logger.warn('Database health check failed', error);
      dependencies.database = 'unhealthy';
    }

    try {
      // Redis check would go here
      // For now, we'll assume it's healthy if the service is running
      dependencies.redis = 'healthy';
    } catch (error) {
      this.logger.warn('Redis health check failed', error);
      dependencies.redis = 'unhealthy';
    }

    try {
      // External services check would go here
      dependencies.external = 'healthy';
    } catch (error) {
      this.logger.warn('External services health check failed', error);
      dependencies.external = 'unhealthy';
    }

    return dependencies;
  }

  /**
   * Get system information
   */
  private async getSystemInfo() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    let diskInfo = {};
    try {
      const stats = await stat('/');
      diskInfo = {
        available: true,
        path: '/',
      };
    } catch (error) {
      diskInfo = {
        available: false,
        error: error.message,
      };
    }

    return {
      memory: {
        rss: this.formatBytes(memoryUsage.rss),
        heapTotal: this.formatBytes(memoryUsage.heapTotal),
        heapUsed: this.formatBytes(memoryUsage.heapUsed),
        external: this.formatBytes(memoryUsage.external),
        arrayBuffers: this.formatBytes(memoryUsage.arrayBuffers),
        systemTotal: this.formatBytes(os.totalmem()),
        systemFree: this.formatBytes(os.freemem()),
        systemUsage: `${(((os.totalmem() - os.freemem()) / os.totalmem()) * 100).toFixed(2)}%`,
      },
      cpu: {
        usage: cpuUsage,
        loadAverage: os.loadavg(),
        cores: os.cpus().length,
        model: os.cpus()[0]?.model || 'unknown',
      },
      disk: diskInfo,
      network: {
        hostname: os.hostname(),
        interfaces: Object.keys(os.networkInterfaces()),
      },
    };
  }

  /**
   * Get application uptime in seconds
   */
  private getUptime(): number {
    return Math.floor((Date.now() - this.startTime) / 1000);
  }

  /**
   * Format bytes to human readable format
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
