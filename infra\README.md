# CloudForge Platform - Infrastructure Scripts

**Production-Ready Infrastructure Automation**  
**Enterprise Deployment Scripts for €60M Platform**

---

## 🏗️ Infrastructure Overview

This directory contains **production-ready infrastructure scripts** for deploying CloudForge Platform in enterprise environments, supporting the **€60 million investment** with automated, scalable, and secure deployment capabilities.

### Infrastructure Components
- **Terraform**: Infrastructure as Code for cloud providers
- **Kubernetes**: Container orchestration and deployment
- **Helm**: Package management and application deployment
- **Docker**: Container images and local development
- **Monitoring**: Observability and monitoring stack

---

## 📁 Directory Structure

```
infra/
├── README.md                          # This file - infrastructure overview
├── terraform/                         # Infrastructure as Code
│   ├── aws/                           # AWS-specific configurations
│   ├── azure/                         # Azure-specific configurations
│   ├── gcp/                           # Google Cloud-specific configurations
│   └── modules/                       # Reusable Terraform modules
├── kubernetes/                        # Kubernetes manifests
│   ├── base/                          # Base Kubernetes configurations
│   ├── overlays/                      # Environment-specific overlays
│   └── operators/                     # Custom operators and CRDs
├── helm/                              # Helm charts
│   ├── cloudforge-platform/          # Main platform Helm chart
│   ├── monitoring/                    # Monitoring stack Helm charts
│   └── security/                      # Security tools Helm charts
├── docker/                            # Docker configurations
│   ├── Dockerfile.production          # Production Docker images
│   ├── docker-compose.production.yml  # Production Docker Compose
│   └── docker-compose.monitoring.yml  # Monitoring stack
├── scripts/                           # Automation scripts
│   ├── deploy.sh                      # Main deployment script
│   ├── setup-cluster.sh               # Kubernetes cluster setup
│   ├── install-monitoring.sh          # Monitoring stack installation
│   └── backup.sh                      # Backup and disaster recovery
└── monitoring/                        # Monitoring configurations
    ├── prometheus/                    # Prometheus configurations
    ├── grafana/                       # Grafana dashboards
    └── alertmanager/                  # Alert manager rules
```

---

## 🚀 Quick Start Deployment

### 1. Prerequisites

#### Required Tools
```bash
# Install required tools
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
sudo apt-get update && sudo apt-get install terraform
```

#### Environment Setup
```bash
# Set environment variables
export CLOUD_PROVIDER="aws"  # aws, azure, or gcp
export ENVIRONMENT="production"
export CLUSTER_NAME="cloudforge-production"
export REGION="us-east-1"
```

### 2. Infrastructure Deployment

#### Step 1: Deploy Infrastructure
```bash
# Navigate to infrastructure directory
cd infra/terraform/${CLOUD_PROVIDER}

# Initialize Terraform
terraform init

# Plan infrastructure deployment
terraform plan -var="environment=${ENVIRONMENT}" -var="cluster_name=${CLUSTER_NAME}"

# Deploy infrastructure
terraform apply -var="environment=${ENVIRONMENT}" -var="cluster_name=${CLUSTER_NAME}"
```

#### Step 2: Configure Kubernetes
```bash
# Setup Kubernetes cluster
./scripts/setup-cluster.sh

# Verify cluster connectivity
kubectl cluster-info
kubectl get nodes
```

#### Step 3: Deploy Platform
```bash
# Deploy CloudForge Platform
helm install cloudforge-platform ./helm/cloudforge-platform \
  --namespace cloudforge-production \
  --create-namespace \
  --values ./helm/cloudforge-platform/values-production.yaml
```

#### Step 4: Install Monitoring
```bash
# Install monitoring stack
./scripts/install-monitoring.sh

# Verify monitoring deployment
kubectl get pods -n monitoring
```

---

## ☁️ Cloud Provider Configurations

### 3. AWS Deployment

#### AWS Infrastructure
```hcl
# terraform/aws/main.tf
provider "aws" {
  region = var.aws_region
}

module "vpc" {
  source = "../modules/vpc"
  
  name               = "${var.cluster_name}-vpc"
  cidr               = "10.0.0.0/16"
  availability_zones = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  
  public_subnets  = ["********/24", "********/24", "********/24"]
  private_subnets = ["*********/24", "*********/24", "*********/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = false
  
  tags = {
    Environment = var.environment
    Project     = "CloudForge"
  }
}

module "eks" {
  source = "../modules/eks"
  
  cluster_name    = var.cluster_name
  cluster_version = "1.28"
  
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets
  
  node_groups = {
    main = {
      desired_capacity = 6
      max_capacity     = 50
      min_capacity     = 3
      
      instance_types = ["c5.2xlarge"]
      
      k8s_labels = {
        Environment = var.environment
        NodeGroup   = "main"
      }
    }
  }
  
  tags = {
    Environment = var.environment
    Project     = "CloudForge"
  }
}
```

#### AWS Variables
```hcl
# terraform/aws/variables.tf
variable "aws_region" {
  description = "AWS region for deployment"
  type        = string
  default     = "us-east-1"
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = "cloudforge-production"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "node_instance_type" {
  description = "EC2 instance type for worker nodes"
  type        = string
  default     = "c5.2xlarge"
}
```

### 4. Azure Deployment

#### Azure Infrastructure
```hcl
# terraform/azure/main.tf
provider "azurerm" {
  features {}
}

resource "azurerm_resource_group" "main" {
  name     = "${var.cluster_name}-rg"
  location = var.azure_location
  
  tags = {
    Environment = var.environment
    Project     = "CloudForge"
  }
}

resource "azurerm_kubernetes_cluster" "main" {
  name                = var.cluster_name
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  dns_prefix          = "${var.cluster_name}-dns"
  
  default_node_pool {
    name       = "default"
    node_count = 6
    vm_size    = "Standard_D4s_v3"
    
    enable_auto_scaling = true
    min_count          = 3
    max_count          = 50
  }
  
  identity {
    type = "SystemAssigned"
  }
  
  network_profile {
    network_plugin = "azure"
  }
  
  tags = {
    Environment = var.environment
    Project     = "CloudForge"
  }
}
```

### 5. Google Cloud Deployment

#### GCP Infrastructure
```hcl
# terraform/gcp/main.tf
provider "google" {
  project = var.gcp_project
  region  = var.gcp_region
}

resource "google_container_cluster" "primary" {
  name     = var.cluster_name
  location = var.gcp_region
  
  remove_default_node_pool = true
  initial_node_count       = 1
  
  network    = google_compute_network.vpc.name
  subnetwork = google_compute_subnetwork.subnet.name
  
  ip_allocation_policy {
    cluster_ipv4_cidr_block  = "*********/21"
    services_ipv4_cidr_block = "*********/21"
  }
  
  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }
}

resource "google_container_node_pool" "primary_nodes" {
  name       = "${google_container_cluster.primary.name}-node-pool"
  location   = var.gcp_region
  cluster    = google_container_cluster.primary.name
  node_count = 6
  
  autoscaling {
    min_node_count = 3
    max_node_count = 50
  }
  
  node_config {
    preemptible  = false
    machine_type = "e2-standard-4"
    
    oauth_scopes = [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring",
    ]
  }
}
```

---

## 🐳 Container Deployment

### 6. Docker Configuration

#### Production Dockerfile
```dockerfile
# docker/Dockerfile.production
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runtime

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app

COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

USER nextjs

EXPOSE 3000

ENV NODE_ENV=production
ENV PORT=3000

CMD ["node", "dist/main.js"]
```

#### Production Docker Compose
```yaml
# docker/docker-compose.production.yml
version: '3.8'

services:
  api-gateway:
    image: cloudforge/api-gateway:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    
  auth-service:
    image: cloudforge/auth-service:latest
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    
  user-service:
    image: cloudforge/user-service:latest
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=cloudforge
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

---

## ⚙️ Kubernetes Deployment

### 7. Kubernetes Manifests

#### Namespace Configuration
```yaml
# kubernetes/base/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: cloudforge-production
  labels:
    name: cloudforge-production
    environment: production
    project: cloudforge
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: cloudforge-quota
  namespace: cloudforge-production
spec:
  hard:
    requests.cpu: "100"
    requests.memory: "200Gi"
    limits.cpu: "200"
    limits.memory: "400Gi"
    persistentvolumeclaims: "50"
```

#### API Gateway Deployment
```yaml
# kubernetes/base/api-gateway.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: cloudforge-production
spec:
  replicas: 6
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: cloudforge/api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
  namespace: cloudforge-production
spec:
  selector:
    app: api-gateway
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
```

---

## 📊 Monitoring Stack

### 8. Monitoring Configuration

#### Prometheus Configuration
```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  - job_name: 'cloudforge-services'
    kubernetes_sd_configs:
    - role: pod
      namespaces:
        names:
        - cloudforge-production
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
```

#### Grafana Dashboards
```json
{
  "dashboard": {
    "title": "CloudForge Platform Overview",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ]
      }
    ]
  }
}
```

---

## 🔧 Automation Scripts

### 9. Deployment Scripts

#### Main Deployment Script
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-production}
CLOUD_PROVIDER=${2:-aws}

echo "🚀 Deploying CloudForge Platform to ${ENVIRONMENT} on ${CLOUD_PROVIDER}"

# Step 1: Deploy infrastructure
echo "📦 Deploying infrastructure..."
cd terraform/${CLOUD_PROVIDER}
terraform init
terraform apply -auto-approve -var="environment=${ENVIRONMENT}"

# Step 2: Configure kubectl
echo "⚙️ Configuring kubectl..."
if [ "$CLOUD_PROVIDER" = "aws" ]; then
    aws eks update-kubeconfig --region ${AWS_REGION} --name ${CLUSTER_NAME}
elif [ "$CLOUD_PROVIDER" = "azure" ]; then
    az aks get-credentials --resource-group ${RESOURCE_GROUP} --name ${CLUSTER_NAME}
elif [ "$CLOUD_PROVIDER" = "gcp" ]; then
    gcloud container clusters get-credentials ${CLUSTER_NAME} --region ${GCP_REGION}
fi

# Step 3: Deploy platform
echo "🏗️ Deploying CloudForge Platform..."
helm upgrade --install cloudforge-platform ./helm/cloudforge-platform \
    --namespace cloudforge-${ENVIRONMENT} \
    --create-namespace \
    --values ./helm/cloudforge-platform/values-${ENVIRONMENT}.yaml \
    --wait

# Step 4: Install monitoring
echo "📊 Installing monitoring stack..."
./scripts/install-monitoring.sh

echo "✅ Deployment completed successfully!"
echo "🌐 Access your platform at: https://cloudforge.${DOMAIN}"
```

---

## 🏆 Infrastructure Excellence

**CloudForge Platform infrastructure provides enterprise-grade deployment capabilities supporting your €60 million investment with:**

### Infrastructure Benefits
- **Multi-Cloud Support**: Deploy on AWS, Azure, or Google Cloud
- **Production-Ready**: Battle-tested infrastructure configurations
- **Automated Deployment**: One-command deployment and scaling
- **Monitoring Included**: Comprehensive observability stack
- **Security Hardened**: Enterprise security best practices

### Ready for Enterprise Scale
- ✅ **High Availability**: Multi-zone deployment with failover
- ✅ **Auto-Scaling**: Dynamic scaling based on demand
- ✅ **Disaster Recovery**: Automated backup and recovery
- ✅ **Compliance Ready**: Meets enterprise compliance requirements
- ✅ **Cost Optimized**: Efficient resource utilization

**CloudForge Platform Infrastructure: Enterprise-Grade Foundation for Your €60 Million Investment**

---

*This infrastructure package provides everything needed to deploy and operate CloudForge Platform at enterprise scale with the highest standards of reliability, security, and performance.*
