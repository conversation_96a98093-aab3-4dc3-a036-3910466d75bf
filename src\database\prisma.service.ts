import { Injectable, OnModuleInit, OnM<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get('DATABASE_URL'),
        },
      },
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
      errorFormat: 'pretty',
    });

    // Log database queries in development
    if (configService.get('NODE_ENV') === 'development') {
      this.$on('query', (e) => {
        this.logger.debug(`Query: ${e.query}`);
        this.logger.debug(`Params: ${e.params}`);
        this.logger.debug(`Duration: ${e.duration}ms`);
      });
    }

    this.$on('error', (e) => {
      this.logger.error('Database error:', e);
    });

    this.$on('info', (e) => {
      this.logger.log(`Database info: ${e.message}`);
    });

    this.$on('warn', (e) => {
      this.logger.warn(`Database warning: ${e.message}`);
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      this.logger.log('✅ Database connected successfully');
      
      // Test the connection
      await this.$queryRaw`SELECT 1`;
      this.logger.log('✅ Database health check passed');
    } catch (error) {
      this.logger.error('❌ Failed to connect to database:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.$disconnect();
      this.logger.log('✅ Database disconnected successfully');
    } catch (error) {
      this.logger.error('❌ Error disconnecting from database:', error);
    }
  }

  async enableShutdownHooks(app: any) {
    this.$on('beforeExit', async () => {
      await app.close();
    });
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return false;
    }
  }

  // Transaction helper
  async transaction<T>(fn: (prisma: PrismaClient) => Promise<T>): Promise<T> {
    return this.$transaction(fn);
  }

  // Soft delete helper
  async softDelete(model: string, id: string) {
    return this[model].update({
      where: { id },
      data: { isActive: false, updatedAt: new Date() },
    });
  }

  // Bulk operations helper
  async bulkCreate(model: string, data: any[]) {
    return this[model].createMany({
      data,
      skipDuplicates: true,
    });
  }

  // Search helper with pagination
  async searchWithPagination(
    model: string,
    searchTerm: string,
    searchFields: string[],
    page: number = 1,
    limit: number = 10,
    orderBy: any = { createdAt: 'desc' },
  ) {
    const skip = (page - 1) * limit;
    
    const where = searchTerm
      ? {
          OR: searchFields.map((field) => ({
            [field]: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          })),
        }
      : {};

    const [data, total] = await Promise.all([
      this[model].findMany({
        where,
        skip,
        take: limit,
        orderBy,
      }),
      this[model].count({ where }),
    ]);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  // Analytics helper
  async getAnalytics(model: string, dateField: string = 'createdAt', days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return this[model].groupBy({
      by: [dateField],
      where: {
        [dateField]: {
          gte: startDate,
        },
      },
      _count: true,
      orderBy: {
        [dateField]: 'asc',
      },
    });
  }

  // Performance monitoring
  async getSlowQueries(threshold: number = 1000) {
    // This would typically integrate with a query monitoring service
    // For now, we'll return a placeholder
    return {
      threshold,
      queries: [],
      message: 'Query monitoring active',
    };
  }

  // Database statistics
  async getDatabaseStats() {
    try {
      const stats = await this.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation
        FROM pg_stats 
        WHERE schemaname = 'public'
        LIMIT 10
      `;

      return {
        stats,
        timestamp: new Date(),
        status: 'healthy',
      };
    } catch (error) {
      this.logger.error('Failed to get database stats:', error);
      return {
        stats: [],
        timestamp: new Date(),
        status: 'error',
        error: error.message,
      };
    }
  }
}
