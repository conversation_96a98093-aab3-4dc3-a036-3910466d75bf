import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '@prisma/client';
import { UpdateUserDto, UpdatePasswordDto, CreateUserDto } from './dto/users.dto';

@ApiTags('users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get all users',
    description: 'Retrieve paginated list of all users (Admin only)',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    schema: {
      example: {
        success: true,
        data: {
          data: [
            {
              id: 'user_123',
              email: '<EMAIL>',
              username: 'user123',
              firstName: 'John',
              lastName: 'Doe',
              role: 'USER',
              isActive: true,
              createdAt: '2024-01-01T00:00:00Z',
            },
          ],
          pagination: {
            page: 1,
            limit: 10,
            total: 100,
            pages: 10,
            hasNext: true,
            hasPrev: false,
          },
        },
        message: 'Users retrieved successfully',
      },
    },
  })
  async findAll(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('search') search?: string,
  ) {
    const result = await this.usersService.findAll(
      parseInt(page),
      parseInt(limit),
      search,
    );

    return {
      success: true,
      data: result,
      message: 'Users retrieved successfully',
      platform: 'CloudForge Platform - €0.001/user/month',
    };
  }

  @Get('me')
  @ApiOperation({
    summary: 'Get current user profile',
    description: 'Get detailed profile information for the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'User profile retrieved successfully',
  })
  async getProfile(@Request() req) {
    const user = await this.usersService.findById(req.user.id);
    const stats = await this.usersService.getUserStats(req.user.id);

    return {
      success: true,
      data: {
        profile: user,
        stats: stats.aiUsage,
        counts: stats.counts,
        costEfficiency: stats.costEfficiency,
      },
      message: 'Profile retrieved successfully',
      membership: 'CloudForge Transcendent User',
    };
  }

  @Get('me/api-keys')
  @ApiOperation({
    summary: 'Get user API keys',
    description: 'Retrieve all API keys for the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'API keys retrieved successfully',
  })
  async getApiKeys(@Request() req) {
    const apiKeys = await this.usersService.getUserApiKeys(req.user.id);

    return {
      success: true,
      data: apiKeys,
      message: 'API keys retrieved successfully',
      security: 'Quantum-enhanced API key management',
    };
  }

  @Delete('me/api-keys/:keyId')
  @ApiOperation({
    summary: 'Revoke API key',
    description: 'Revoke a specific API key',
  })
  @ApiResponse({
    status: 200,
    description: 'API key revoked successfully',
  })
  async revokeApiKey(@Request() req, @Param('keyId') keyId: string) {
    await this.usersService.revokeApiKey(req.user.id, keyId);

    return {
      success: true,
      message: 'API key revoked successfully',
    };
  }

  @Get(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve specific user by ID (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'User retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async findById(@Param('id') id: string) {
    const user = await this.usersService.findById(id);
    const stats = await this.usersService.getUserStats(id);

    return {
      success: true,
      data: {
        user,
        stats: stats.aiUsage,
        counts: stats.counts,
      },
      message: 'User retrieved successfully',
    };
  }

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Create new user',
    description: 'Create a new user account (Admin only)',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'User already exists or validation error',
  })
  async create(@Body() createUserDto: CreateUserDto) {
    const user = await this.usersService.create(createUserDto);

    return {
      success: true,
      data: user,
      message: 'User created successfully',
      welcome: 'Welcome to CloudForge Platform',
      benefits: {
        costPerUser: '€0.001/month',
        aiCapabilities: 'Consciousness-level AI included',
        support: '24/7 self-supporting system',
      },
    };
  }

  @Put('me')
  @ApiOperation({
    summary: 'Update current user profile',
    description: 'Update profile information for the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
  })
  async updateProfile(@Request() req, @Body() updateUserDto: UpdateUserDto) {
    const user = await this.usersService.update(req.user.id, updateUserDto);

    return {
      success: true,
      data: user,
      message: 'Profile updated successfully',
    };
  }

  @Put('me/password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update password',
    description: 'Update password for the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'Password updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Current password is incorrect',
  })
  async updatePassword(@Request() req, @Body() updatePasswordDto: UpdatePasswordDto) {
    await this.usersService.updatePassword(
      req.user.id,
      updatePasswordDto.currentPassword,
      updatePasswordDto.newPassword,
    );

    return {
      success: true,
      message: 'Password updated successfully',
      security: 'All sessions have been invalidated for security',
    };
  }

  @Put(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Update user',
    description: 'Update user information (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    const user = await this.usersService.update(id, updateUserDto);

    return {
      success: true,
      data: user,
      message: 'User updated successfully',
    };
  }

  @Delete(':id/deactivate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Deactivate user',
    description: 'Deactivate user account (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'User deactivated successfully',
  })
  async deactivate(@Param('id') id: string) {
    await this.usersService.deactivate(id);

    return {
      success: true,
      message: 'User deactivated successfully',
    };
  }

  @Put(':id/reactivate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Reactivate user',
    description: 'Reactivate user account (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'User reactivated successfully',
  })
  async reactivate(@Param('id') id: string) {
    await this.usersService.reactivate(id);

    return {
      success: true,
      message: 'User reactivated successfully',
    };
  }
}
