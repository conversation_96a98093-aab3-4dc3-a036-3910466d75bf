import { registerAs } from '@nestjs/config';

export default registerAs('ai', () => ({
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    defaultModel: process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
    maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS, 10) || 2000,
    temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.7,
    timeout: parseInt(process.env.OPENAI_TIMEOUT, 10) || 30000,
  },
  anthropic: {
    apiKey: process.env.ANTHROPIC_API_KEY,
    baseURL: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com',
    defaultModel: process.env.ANTHROPIC_DEFAULT_MODEL || 'claude-3-sonnet-20240229',
    maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS, 10) || 2000,
    temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE) || 0.7,
    timeout: parseInt(process.env.ANTHROPIC_TIMEOUT, 10) || 30000,
  },
  costTracking: {
    enabled: process.env.AI_COST_TRACKING === 'true',
    currency: process.env.AI_COST_CURRENCY || 'EUR',
    alertThreshold: parseFloat(process.env.AI_COST_ALERT_THRESHOLD) || 100,
  },
  rateLimit: {
    requestsPerMinute: parseInt(process.env.AI_RATE_LIMIT_RPM, 10) || 60,
    tokensPerMinute: parseInt(process.env.AI_RATE_LIMIT_TPM, 10) || 100000,
  },
}));
