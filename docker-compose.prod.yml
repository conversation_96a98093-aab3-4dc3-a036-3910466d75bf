# CloudForge Platform - Production Docker Compose
# Created by <PERSON><PERSON>
# Transcendent Excellence at €0.001/user/month

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cloudforge-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: cloudforge
      POSTGRES_USER: cloudforge
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cloudforge_secure_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - cloudforge-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cloudforge -d cloudforge"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cloudforge-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-cloudforge_redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - cloudforge-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'

  # CloudForge Platform Application
  cloudforge-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: cloudforge-app
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 3000
      HOST: 0.0.0.0
      
      # Database
      DATABASE_URL: postgresql://cloudforge:${POSTGRES_PASSWORD:-cloudforge_secure_password}@postgres:5432/cloudforge?schema=public
      
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-cloudforge_redis_password}
      
      # JWT
      JWT_SECRET: ${JWT_SECRET:-cloudforge_jwt_secret_key_transcendent_security}
      JWT_EXPIRES_IN: 24h
      JWT_REFRESH_EXPIRES_IN: 7d
      
      # AI APIs
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      
      # Application
      API_BASE_URL: ${API_BASE_URL:-http://localhost:3000}
      CORS_ORIGIN: ${CORS_ORIGIN:-*}
      
      # Rate Limiting
      THROTTLE_TTL: 60
      THROTTLE_LIMIT: 1000
      RATE_LIMIT_MAX: 10000
      
      # Cache
      CACHE_TTL: 300
      CACHE_MAX_ITEMS: 10000
      
      # Monitoring
      LOG_LEVEL: info
      METRICS_ENABLED: true
      
    ports:
      - "3000:3000"
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    networks:
      - cloudforge-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: cloudforge-nginx
    restart: unless-stopped
    depends_on:
      - cloudforge-app
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - cloudforge-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: cloudforge-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - cloudforge-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: cloudforge-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-cloudforge_grafana_admin}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - cloudforge-network
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  cloudforge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
