# Developer Guide

## Contributing

- Fork and clone the repository.
- Use feature branches and submit pull requests.
- All code must pass lint, tests, and code review.

## Microservices

- Each service is in `backend/`.
- Use RESTful APIs, JWT for auth, and log all actions.

## Coding Standards

- Node.js: StandardJS
- React: Airbnb style guide

## Testing

- Unit tests in `tests/unit/`
- Integration tests in `tests/integration/`
- Load tests in `tests/load/`
