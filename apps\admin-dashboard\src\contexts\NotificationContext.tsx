/**
 * CloudForge Platform - Notification Context
 * Enterprise-grade notification system with multiple channels
 * Created by <PERSON><PERSON>
 */

import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';
import {
  <PERSON>nackbar,
  Alert,
  AlertT<PERSON>le,
  Slide,
  SlideProps,
  IconButton,
  Box,
  Typography,
  Stack
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';

// Types
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  timestamp: number;
}

interface NotificationState {
  notifications: Notification[];
  queue: Notification[];
  maxVisible: number;
}

// Action types
type NotificationAction =
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'CLEAR_ALL' }
  | { type: 'PROCESS_QUEUE' };

// Initial state
const initialState: NotificationState = {
  notifications: [],
  queue: [],
  maxVisible: 3,
};

// Reducer
const notificationReducer = (state: NotificationState, action: NotificationAction): NotificationState => {
  switch (action.type) {
    case 'ADD_NOTIFICATION': {
      const newNotification = action.payload;
      
      if (state.notifications.length >= state.maxVisible) {
        // Add to queue if max visible reached
        return {
          ...state,
          queue: [...state.queue, newNotification],
        };
      }
      
      return {
        ...state,
        notifications: [...state.notifications, newNotification],
      };
    }
    
    case 'REMOVE_NOTIFICATION': {
      const filteredNotifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
      
      // Process queue if there's space
      let newQueue = state.queue;
      let newNotifications = filteredNotifications;
      
      if (filteredNotifications.length < state.maxVisible && state.queue.length > 0) {
        const nextNotification = state.queue[0];
        newNotifications = [...filteredNotifications, nextNotification];
        newQueue = state.queue.slice(1);
      }
      
      return {
        ...state,
        notifications: newNotifications,
        queue: newQueue,
      };
    }
    
    case 'CLEAR_ALL':
      return {
        ...state,
        notifications: [],
        queue: [],
      };
    
    case 'PROCESS_QUEUE': {
      if (state.queue.length === 0 || state.notifications.length >= state.maxVisible) {
        return state;
      }
      
      const nextNotification = state.queue[0];
      return {
        ...state,
        notifications: [...state.notifications, nextNotification],
        queue: state.queue.slice(1),
      };
    }
    
    default:
      return state;
  }
};

// Context
interface NotificationContextType {
  notifications: Notification[];
  showNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => string;
  showSuccess: (message: string, title?: string, options?: Partial<Notification>) => string;
  showError: (message: string, title?: string, options?: Partial<Notification>) => string;
  showWarning: (message: string, title?: string, options?: Partial<Notification>) => string;
  showInfo: (message: string, title?: string, options?: Partial<Notification>) => string;
  removeNotification: (id: string) => void;
  clearAll: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Slide transition component
function SlideTransition(props: SlideProps) {
  return <Slide {...props} direction="left" />;
}

// Provider component
interface NotificationProviderProps {
  children: ReactNode;
  maxVisible?: number;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ 
  children, 
  maxVisible = 3 
}) => {
  const [state, dispatch] = useReducer(notificationReducer, {
    ...initialState,
    maxVisible,
  });

  // Generate unique ID
  const generateId = useCallback(() => {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Show notification
  const showNotification = useCallback((
    notification: Omit<Notification, 'id' | 'timestamp'>
  ): string => {
    const id = generateId();
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: Date.now(),
      duration: notification.duration ?? (notification.type === 'error' ? 6000 : 4000),
    };

    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification });

    // Auto-remove notification after duration (unless persistent)
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
      }, newNotification.duration);
    }

    return id;
  }, [generateId]);

  // Convenience methods
  const showSuccess = useCallback((
    message: string, 
    title?: string, 
    options?: Partial<Notification>
  ): string => {
    return showNotification({
      type: 'success',
      message,
      title,
      ...options,
    });
  }, [showNotification]);

  const showError = useCallback((
    message: string, 
    title?: string, 
    options?: Partial<Notification>
  ): string => {
    return showNotification({
      type: 'error',
      message,
      title,
      ...options,
    });
  }, [showNotification]);

  const showWarning = useCallback((
    message: string, 
    title?: string, 
    options?: Partial<Notification>
  ): string => {
    return showNotification({
      type: 'warning',
      message,
      title,
      ...options,
    });
  }, [showNotification]);

  const showInfo = useCallback((
    message: string, 
    title?: string, 
    options?: Partial<Notification>
  ): string => {
    return showNotification({
      type: 'info',
      message,
      title,
      ...options,
    });
  }, [showNotification]);

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  }, []);

  // Clear all notifications
  const clearAll = useCallback(() => {
    dispatch({ type: 'CLEAR_ALL' });
  }, []);

  // Get icon for notification type
  const getIcon = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return <SuccessIcon />;
      case 'error':
        return <ErrorIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      default:
        return <InfoIcon />;
    }
  };

  const contextValue: NotificationContextType = {
    notifications: state.notifications,
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification,
    clearAll,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Render notifications */}
      <Box
        sx={{
          position: 'fixed',
          top: 24,
          right: 24,
          zIndex: 9999,
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          maxWidth: 400,
        }}
      >
        {state.notifications.map((notification) => (
          <Snackbar
            key={notification.id}
            open={true}
            TransitionComponent={SlideTransition}
            sx={{
              position: 'relative',
              transform: 'none !important',
              left: 'auto !important',
              right: 'auto !important',
              top: 'auto !important',
              bottom: 'auto !important',
            }}
          >
            <Alert
              severity={notification.type}
              variant="filled"
              icon={getIcon(notification.type)}
              action={
                <Stack direction="row" spacing={1} alignItems="center">
                  {notification.action && (
                    <Typography
                      variant="button"
                      sx={{
                        cursor: 'pointer',
                        textDecoration: 'underline',
                        color: 'inherit',
                        fontSize: '0.75rem',
                      }}
                      onClick={notification.action.onClick}
                    >
                      {notification.action.label}
                    </Typography>
                  )}
                  <IconButton
                    size="small"
                    aria-label="close"
                    color="inherit"
                    onClick={() => removeNotification(notification.id)}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Stack>
              }
              sx={{
                width: '100%',
                minWidth: 300,
                maxWidth: 400,
                '& .MuiAlert-message': {
                  width: '100%',
                },
              }}
            >
              {notification.title && (
                <AlertTitle sx={{ mb: notification.message ? 1 : 0 }}>
                  {notification.title}
                </AlertTitle>
              )}
              {notification.message && (
                <Typography variant="body2">
                  {notification.message}
                </Typography>
              )}
            </Alert>
          </Snackbar>
        ))}
      </Box>
      
      {/* Queue indicator */}
      {state.queue.length > 0 && (
        <Box
          sx={{
            position: 'fixed',
            top: 24,
            right: 440,
            zIndex: 9998,
            bgcolor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            px: 2,
            py: 1,
            boxShadow: 2,
          }}
        >
          <Typography variant="caption" color="text.secondary">
            +{state.queue.length} more notification{state.queue.length > 1 ? 's' : ''}
          </Typography>
        </Box>
      )}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotification = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

// Higher-order component for error handling with notifications
export const withNotificationErrorHandler = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = (props: P) => {
    const { showError } = useNotification();

    React.useEffect(() => {
      const handleError = (event: ErrorEvent) => {
        showError(
          event.message || 'An unexpected error occurred',
          'Application Error',
          { persistent: true }
        );
      };

      const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
        showError(
          event.reason?.message || 'An unexpected error occurred',
          'Promise Rejection',
          { persistent: true }
        );
      };

      window.addEventListener('error', handleError);
      window.addEventListener('unhandledrejection', handleUnhandledRejection);

      return () => {
        window.removeEventListener('error', handleError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }, [showError]);

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withNotificationErrorHandler(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

export default NotificationContext;
