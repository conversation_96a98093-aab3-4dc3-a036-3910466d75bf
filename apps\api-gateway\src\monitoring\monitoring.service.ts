/**
 * CloudForge Platform - Monitoring Service (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);
  private readonly monitoringServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.monitoringServiceUrl = this.configService.get<string>('services.monitoringService.url');
  }

  async getMetrics(query: any) {
    try {
      this.logger.log('Proxying get metrics request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/metrics`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get metrics request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getLogs(query: any) {
    try {
      this.logger.log('Proxying get logs request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/logs`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get logs request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getAlerts(query: any) {
    try {
      this.logger.log('Proxying get alerts request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/alerts`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get alerts request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async createAlert(alertData: any) {
    try {
      this.logger.log('Proxying create alert request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.monitoringServiceUrl}/alerts`, alertData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Create alert request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getDashboards() {
    try {
      this.logger.log('Proxying get dashboards request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/dashboards`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get dashboards request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getHealthChecks() {
    try {
      this.logger.log('Proxying get health checks request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/health-checks`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get health checks request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getPerformance(query: any) {
    try {
      this.logger.log('Proxying get performance request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/performance`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get performance request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getTraces(query: any) {
    try {
      this.logger.log('Proxying get traces request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/traces`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get traces request failed', error.response?.data || error.message);
      throw error;
    }
  }
}
