{"name": "cloudforge-frontend", "version": "1.0.0", "description": "CloudForge Platform Frontend - Transcendent Excellence at €0.001/user/month", "author": "Marwan <PERSON>", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@mui/lab": "^5.0.0-alpha.157", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-charts": "^6.18.3", "@mui/x-date-pickers": "^6.18.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "axios": "^1.6.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.12.2", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-syntax-highlighter": "^15.5.0", "react-markdown": "^9.0.1", "react-helmet-async": "^2.0.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node": "^20.10.4", "typescript": "^5.3.3"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0", "@storybook/react": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}