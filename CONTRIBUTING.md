# Contributing to CloudForge Platform

Thank you for your interest in contributing to CloudForge Platform! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md). Please read it before contributing.

## 🚀 Getting Started

### Prerequisites

Before contributing, ensure you have:
- **Node.js** 18+ and npm 9+
- **Docker** and Docker Compose
- **Git** for version control
- **TypeScript** knowledge
- **React** and **NestJS** familiarity

### Development Setup

1. **Fork the Repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/your-username/cloudforge-platform.git
   cd cloudforge-platform
   ```

2. **Set Up Development Environment**
   ```bash
   # Run the setup script
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   
   # Start development environment
   docker-compose up -d
   ```

3. **Install Dependencies**
   ```bash
   # Install root dependencies
   npm install
   
   # Install service dependencies
   npm run install:all
   ```

4. **Verify Setup**
   ```bash
   # Run tests
   npm test
   
   # Start development servers
   npm run dev
   ```

## 📝 How to Contribute

### Reporting Issues

Before creating an issue, please:
1. Check existing issues to avoid duplicates
2. Use the issue templates provided
3. Include detailed information about the problem
4. Provide steps to reproduce the issue

### Suggesting Features

For feature requests:
1. Use the feature request template
2. Explain the use case and benefits
3. Consider the impact on existing functionality
4. Be open to discussion and feedback

### Code Contributions

#### 1. Choose an Issue
- Look for issues labeled `good first issue` for beginners
- Check `help wanted` for areas needing assistance
- Comment on the issue to indicate you're working on it

#### 2. Create a Branch
```bash
# Create a feature branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/issue-description
```

#### 3. Make Changes
- Follow the coding standards (see below)
- Write tests for new functionality
- Update documentation as needed
- Ensure all tests pass

#### 4. Commit Changes
```bash
# Use conventional commit format
git commit -m "feat: add user profile management"
git commit -m "fix: resolve authentication token expiry"
git commit -m "docs: update API documentation"
```

#### 5. Submit Pull Request
- Push your branch to your fork
- Create a pull request with a clear description
- Link to related issues
- Wait for review and address feedback

## 📋 Coding Standards

### TypeScript Guidelines

```typescript
// Use explicit types
interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

// Use async/await instead of promises
async function getUserProfile(id: string): Promise<UserProfile> {
  try {
    const user = await userRepository.findById(id);
    return user;
  } catch (error) {
    throw new Error(`Failed to get user profile: ${error.message}`);
  }
}

// Use proper error handling
class UserService {
  async createUser(userData: CreateUserDto): Promise<User> {
    try {
      // Validate input
      if (!userData.email) {
        throw new BadRequestException('Email is required');
      }
      
      // Business logic
      const user = await this.userRepository.create(userData);
      return user;
    } catch (error) {
      this.logger.error('Failed to create user', error);
      throw error;
    }
  }
}
```

### React Component Guidelines

```tsx
// Use functional components with TypeScript
interface UserCardProps {
  user: User;
  onEdit: (user: User) => void;
  onDelete: (userId: string) => void;
}

const UserCard: React.FC<UserCardProps> = ({ user, onEdit, onDelete }) => {
  const handleEdit = useCallback(() => {
    onEdit(user);
  }, [user, onEdit]);

  const handleDelete = useCallback(() => {
    onDelete(user.id);
  }, [user.id, onDelete]);

  return (
    <Card>
      <CardContent>
        <Typography variant="h6">{user.firstName} {user.lastName}</Typography>
        <Typography variant="body2">{user.email}</Typography>
      </CardContent>
      <CardActions>
        <Button onClick={handleEdit}>Edit</Button>
        <Button onClick={handleDelete} color="error">Delete</Button>
      </CardActions>
    </Card>
  );
};

export default UserCard;
```

### API Design Guidelines

```typescript
// Use proper HTTP status codes
@Controller('users')
export class UsersController {
  @Get()
  async findAll(@Query() query: FindUsersDto): Promise<PaginatedResponse<User>> {
    const users = await this.usersService.findAll(query);
    return {
      success: true,
      data: users,
      total: users.length,
      page: query.page,
      limit: query.limit,
    };
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createUserDto: CreateUserDto): Promise<ApiResponse<User>> {
    const user = await this.usersService.create(createUserDto);
    return {
      success: true,
      data: user,
      message: 'User created successfully',
    };
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<ApiResponse<User>> {
    const user = await this.usersService.update(id, updateUserDto);
    return {
      success: true,
      data: user,
      message: 'User updated successfully',
    };
  }
}
```

### Database Guidelines

```typescript
// Use proper entity definitions
@Entity('users')
export class User extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  @Index()
  email: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ select: false })
  password: string;

  @ManyToMany(() => Role, role => role.users)
  @JoinTable()
  roles: Role[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// Use repository pattern
@Injectable()
export class UserRepository extends Repository<User> {
  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {
    super(User, dataSource.createEntityManager());
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.findOne({
      where: { email },
      relations: ['roles'],
    });
  }

  async findWithPagination(
    page: number,
    limit: number,
  ): Promise<[User[], number]> {
    return this.findAndCount({
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });
  }
}
```

## 🧪 Testing Guidelines

### Unit Tests

```typescript
// Service unit tests
describe('UserService', () => {
  let service: UserService;
  let repository: MockType<UserRepository>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: UserRepository,
          useFactory: repositoryMockFactory,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    repository = module.get(UserRepository);
  });

  describe('create', () => {
    it('should create a user successfully', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
      };

      const expectedUser = { id: '1', ...createUserDto };
      repository.create.mockReturnValue(expectedUser);
      repository.save.mockReturnValue(expectedUser);

      const result = await service.create(createUserDto);

      expect(result).toEqual(expectedUser);
      expect(repository.create).toHaveBeenCalledWith(createUserDto);
      expect(repository.save).toHaveBeenCalledWith(expectedUser);
    });
  });
});
```

### Integration Tests

```typescript
// API integration tests
describe('UsersController (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get('UserRepository');
    await app.init();
  });

  describe('/users (POST)', () => {
    it('should create a new user', () => {
      const createUserDto = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
      };

      return request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.email).toBe(createUserDto.email);
        });
    });
  });
});
```

### Frontend Tests

```tsx
// React component tests
describe('UserCard', () => {
  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
  };

  const mockOnEdit = jest.fn();
  const mockOnDelete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders user information correctly', () => {
    render(
      <UserCard
        user={mockUser}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', () => {
    render(
      <UserCard
        user={mockUser}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    fireEvent.click(screen.getByText('Edit'));
    expect(mockOnEdit).toHaveBeenCalledWith(mockUser);
  });
});
```

## 📚 Documentation Guidelines

### Code Documentation

```typescript
/**
 * Service for managing user accounts and profiles
 * 
 * @example
 * ```typescript
 * const userService = new UserService(userRepository);
 * const user = await userService.create({
 *   email: '<EMAIL>',
 *   firstName: 'John',
 *   lastName: 'Doe'
 * });
 * ```
 */
@Injectable()
export class UserService {
  /**
   * Creates a new user account
   * 
   * @param createUserDto - User creation data
   * @returns Promise resolving to the created user
   * @throws BadRequestException when email already exists
   * @throws InternalServerErrorException when creation fails
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    // Implementation
  }
}
```

### API Documentation

```typescript
@ApiTags('users')
@Controller('users')
export class UsersController {
  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: User,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.create(createUserDto);
  }
}
```

## 🔍 Code Review Process

### For Contributors
1. Ensure all tests pass
2. Follow coding standards
3. Update documentation
4. Write clear commit messages
5. Respond to review feedback promptly

### For Reviewers
1. Check code quality and standards
2. Verify test coverage
3. Test functionality manually
4. Provide constructive feedback
5. Approve when ready

## 📦 Release Process

### Version Numbering
We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist
- [ ] All tests pass
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
- [ ] Version bumped
- [ ] Release notes prepared

## 🆘 Getting Help

### Community Support
- **GitHub Discussions**: For questions and discussions
- **Issues**: For bug reports and feature requests
- **Discord**: Real-time community chat (link in README)

### Documentation
- **API Docs**: Comprehensive API documentation
- **Architecture Guide**: System design and architecture
- **Deployment Guide**: Setup and deployment instructions

## 📄 License

By contributing to CloudForge Platform, you agree that your contributions will be licensed under the same license as the project.

## 🙏 Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation
- Annual contributor highlights

Thank you for contributing to CloudForge Platform! 🚀
