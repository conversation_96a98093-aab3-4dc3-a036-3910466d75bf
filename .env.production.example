# CloudForge Platform - Production Environment Configuration
# Enterprise-grade cloud services platform

# ================================
# ENVIRONMENT CONFIGURATION
# ================================
NODE_ENV=production
APP_ENV=production
LOG_LEVEL=info
LOG_FORMAT=json

# ================================
# DATABASE CONFIGURATION
# ================================
DATABASE_HOST=your-postgres-host
DATABASE_PORT=5432
DATABASE_USERNAME=your-db-username
DATABASE_PASSWORD=your-secure-db-password
DATABASE_NAME=cloudforge_platform
DATABASE_SSL=true
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30000

# ================================
# REDIS CONFIGURATION
# ================================
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password
REDIS_DB=0
REDIS_KEY_PREFIX=cloudforge:
REDIS_TTL=3600

# ================================
# JWT CONFIGURATION
# ================================
JWT_SECRET=your-256-bit-jwt-secret-key-here
JWT_REFRESH_SECRET=your-256-bit-refresh-secret-key-here
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=cloudforge-platform
JWT_AUDIENCE=cloudforge-users

# ================================
# API CONFIGURATION
# ================================
API_PORT=3000
API_PREFIX=api/v1
API_RATE_LIMIT=1000
API_RATE_WINDOW=900000
API_CORS_ORIGIN=https://your-domain.com,https://admin.your-domain.com
API_BODY_LIMIT=10mb

# ================================
# EXTERNAL SERVICES
# ================================

# Stripe Payment Processing
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key

# SendGrid Email Service
SENDGRID_API_KEY=SG.your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=CloudForge Platform

# AWS Services
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-west-2
AWS_S3_BUCKET=your-s3-bucket-name

# Twilio SMS Service (Optional)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# ================================
# MONITORING & OBSERVABILITY
# ================================
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
SENTRY_DSN=https://<EMAIL>/project-id

# ================================
# SECURITY CONFIGURATION
# ================================
ENCRYPTION_KEY=your-32-character-encryption-key
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key
CSRF_SECRET=your-csrf-secret-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# ================================
# FEATURE FLAGS
# ================================
ENABLE_SWAGGER=false
ENABLE_METRICS=true
ENABLE_HEALTH_CHECKS=true
ENABLE_AUDIT_LOGGING=true
ENABLE_TWO_FACTOR_AUTH=true
ENABLE_EMAIL_VERIFICATION=true

# ================================
# NOTIFICATION SETTINGS
# ================================
EMAIL_QUEUE_CONCURRENCY=5
SMS_QUEUE_CONCURRENCY=3
PUSH_QUEUE_CONCURRENCY=10
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=5000

# ================================
# BILLING CONFIGURATION
# ================================
BILLING_CURRENCY=USD
BILLING_TAX_RATE=0.08
BILLING_GRACE_PERIOD_DAYS=3
BILLING_INVOICE_DUE_DAYS=30

# ================================
# FILE UPLOAD CONFIGURATION
# ================================
UPLOAD_MAX_FILE_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_DESTINATION=uploads/

# ================================
# CACHE CONFIGURATION
# ================================
CACHE_TTL=3600
CACHE_MAX_ITEMS=10000
CACHE_CHECK_PERIOD=600

# ================================
# WEBHOOK CONFIGURATION
# ================================
WEBHOOK_TIMEOUT=30000
WEBHOOK_RETRY_ATTEMPTS=3
WEBHOOK_RETRY_DELAY=5000

# ================================
# BACKUP CONFIGURATION
# ================================
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket

# ================================
# SSL/TLS CONFIGURATION
# ================================
SSL_CERT_PATH=/etc/ssl/certs/cloudforge.crt
SSL_KEY_PATH=/etc/ssl/private/cloudforge.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# ================================
# DOMAIN CONFIGURATION
# ================================
FRONTEND_URL=https://your-domain.com
ADMIN_URL=https://admin.your-domain.com
API_URL=https://api.your-domain.com

# ================================
# KUBERNETES CONFIGURATION
# ================================
KUBERNETES_NAMESPACE=cloudforge-platform
KUBERNETES_SERVICE_ACCOUNT=cloudforge-platform
KUBERNETES_CLUSTER_NAME=cloudforge-cluster

# ================================
# CUSTOM BUSINESS CONFIGURATION
# ================================
COMPANY_NAME=Your Company Name
COMPANY_EMAIL=<EMAIL>
COMPANY_PHONE=******-123-4567
SUPPORT_EMAIL=<EMAIL>
TERMS_URL=https://your-domain.com/terms
PRIVACY_URL=https://your-domain.com/privacy
