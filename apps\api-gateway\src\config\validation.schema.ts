/**
 * CloudForge Platform - Configuration Validation Schema
 * Enterprise-grade cloud services platform
 */

import * as <PERSON><PERSON> from 'joi';

export const validationSchema = Joi.object({
  // Application
  NODE_ENV: Joi.string()
    .valid('development', 'staging', 'production', 'test')
    .default('development'),
  APP_NAME: Joi.string().default('CloudForge Platform'),
  APP_VERSION: Joi.string().default('1.0.0'),
  API_PORT: Joi.number().port().default(3000),
  API_PREFIX: Joi.string().default('api/v1'),
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug', 'verbose')
    .default('debug'),

  // Database
  DATABASE_HOST: Joi.string().required(),
  DATABASE_PORT: Joi.number().port().default(5432),
  DATABASE_USERNAME: Joi.string().required(),
  DATABASE_PASSWORD: Joi.string().required(),
  DATABASE_NAME: Joi.string().required(),
  DATABASE_SSL: Joi.boolean().default(false),
  DATABASE_POOL_SIZE: Joi.number().min(1).max(50).default(10),
  DATABASE_TIMEOUT: Joi.number().min(1000).default(30000),

  // Redis
  REDIS_HOST: Joi.string().required(),
  REDIS_PORT: Joi.number().port().default(6379),
  REDIS_PASSWORD: Joi.string().optional(),
  REDIS_DB: Joi.number().min(0).max(15).default(0),
  REDIS_KEY_PREFIX: Joi.string().default('cloudforge:'),
  REDIS_RETRY_DELAY: Joi.number().min(10).default(100),
  REDIS_MAX_RETRIES: Joi.number().min(1).default(3),

  // JWT
  JWT_SECRET: Joi.string().min(32).required(),
  JWT_EXPIRES_IN: Joi.string().default('24h'),
  JWT_REFRESH_SECRET: Joi.string().min(32).required(),
  JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),

  // OAuth2
  GOOGLE_CLIENT_ID: Joi.string().optional(),
  GOOGLE_CLIENT_SECRET: Joi.string().optional(),
  GOOGLE_CALLBACK_URL: Joi.string().uri().optional(),
  GITHUB_CLIENT_ID: Joi.string().optional(),
  GITHUB_CLIENT_SECRET: Joi.string().optional(),
  GITHUB_CALLBACK_URL: Joi.string().uri().optional(),

  // Security
  BCRYPT_ROUNDS: Joi.number().min(8).max(15).default(12),
  SESSION_SECRET: Joi.string().min(32).required(),
  SESSION_TIMEOUT: Joi.number().min(60000).default(3600000), // 1 hour
  MAX_LOGIN_ATTEMPTS: Joi.number().min(3).max(10).default(5),
  LOCKOUT_DURATION: Joi.number().min(60000).default(900000), // 15 minutes
  ENCRYPTION_KEY: Joi.string().length(32).required(),

  // Rate Limiting
  RATE_LIMIT_WINDOW: Joi.number().min(60000).default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().min(10).default(100),

  // CORS
  CORS_ORIGIN: Joi.alternatives()
    .try(
      Joi.string().uri(),
      Joi.array().items(Joi.string().uri())
    )
    .default('http://localhost:3001'),

  // Email
  SMTP_HOST: Joi.string().hostname().optional(),
  SMTP_PORT: Joi.number().port().default(587),
  SMTP_SECURE: Joi.boolean().default(false),
  SMTP_USER: Joi.string().email().optional(),
  SMTP_PASSWORD: Joi.string().optional(),
  EMAIL_FROM: Joi.string().email().default('<EMAIL>'),
  EMAIL_TEMPLATES_PATH: Joi.string().default('./templates/email'),

  // File Upload
  MAX_FILE_SIZE: Joi.number().min(1024).default(10485760), // 10MB
  ALLOWED_FILE_TYPES: Joi.string().default('image/jpeg,image/png,image/gif,application/pdf'),
  UPLOAD_DESTINATION: Joi.string().default('./uploads'),
  STORAGE_TYPE: Joi.string()
    .valid('local', 'aws_s3', 'gcp_storage', 'azure_blob')
    .default('local'),

  // Monitoring
  ENABLE_MONITORING: Joi.boolean().default(true),
  PROMETHEUS_PORT: Joi.number().port().default(9090),
  GRAFANA_PORT: Joi.number().port().default(3000),
  HEALTH_CHECK_PATH: Joi.string().default('/health'),
  METRICS_PATH: Joi.string().default('/metrics'),
  JAEGER_ENDPOINT: Joi.string().uri().optional(),

  // Billing
  STRIPE_SECRET_KEY: Joi.string().optional(),
  STRIPE_WEBHOOK_SECRET: Joi.string().optional(),
  BILLING_CURRENCY: Joi.string()
    .valid('USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD')
    .default('USD'),
  DEFAULT_PLAN: Joi.string()
    .valid('free', 'basic', 'professional', 'enterprise')
    .default('free'),

  // Feature Flags
  ENABLE_REGISTRATION: Joi.boolean().default(true),
  ENABLE_OAUTH: Joi.boolean().default(true),
  ENABLE_BILLING: Joi.boolean().default(true),
  ENABLE_NOTIFICATIONS: Joi.boolean().default(true),

  // External Services
  AUTH_SERVICE_URL: Joi.string().uri().default('http://localhost:3001'),
  AUTH_SERVICE_TIMEOUT: Joi.number().min(1000).default(30000),
  USER_SERVICE_URL: Joi.string().uri().default('http://localhost:3002'),
  USER_SERVICE_TIMEOUT: Joi.number().min(1000).default(30000),
  BILLING_SERVICE_URL: Joi.string().uri().default('http://localhost:3003'),
  BILLING_SERVICE_TIMEOUT: Joi.number().min(1000).default(30000),
  NOTIFICATION_SERVICE_URL: Joi.string().uri().default('http://localhost:3004'),
  NOTIFICATION_SERVICE_TIMEOUT: Joi.number().min(1000).default(30000),
  MONITORING_SERVICE_URL: Joi.string().uri().default('http://localhost:3005'),
  MONITORING_SERVICE_TIMEOUT: Joi.number().min(1000).default(30000),

  // Cloud Provider
  CLOUD_PROVIDER: Joi.string()
    .valid('aws', 'gcp', 'azure', 'digitalocean', 'linode')
    .default('aws'),
  CLOUD_REGION: Joi.string().default('us-east-1'),

  // AWS
  AWS_ACCESS_KEY_ID: Joi.string().optional(),
  AWS_SECRET_ACCESS_KEY: Joi.string().optional(),
  AWS_REGION: Joi.string().default('us-east-1'),

  // GCP
  GCP_PROJECT_ID: Joi.string().optional(),
  GCP_KEY_FILENAME: Joi.string().optional(),
  GCP_REGION: Joi.string().default('us-central1'),

  // Azure
  AZURE_SUBSCRIPTION_ID: Joi.string().optional(),
  AZURE_TENANT_ID: Joi.string().optional(),
  AZURE_CLIENT_ID: Joi.string().optional(),
  AZURE_CLIENT_SECRET: Joi.string().optional(),
  AZURE_REGION: Joi.string().default('East US'),

  // Logging
  LOG_FORMAT: Joi.string().valid('json', 'simple', 'combined').default('json'),
  LOG_FILE_ENABLED: Joi.boolean().default(false),
  LOG_FILENAME: Joi.string().default('app.log'),
  LOG_MAX_SIZE: Joi.string().default('20m'),
  LOG_MAX_FILES: Joi.number().min(1).default(5),
  ELASTICSEARCH_ENABLED: Joi.boolean().default(false),
  ELASTICSEARCH_HOST: Joi.string().default('localhost:9200'),
  ELASTICSEARCH_INDEX: Joi.string().default('cloudforge-logs'),

  // Cache
  CACHE_TTL: Joi.number().min(1).default(300), // 5 minutes
  CACHE_MAX: Joi.number().min(1).default(1000),
  CACHE_UPDATE_AGE_ON_GET: Joi.boolean().default(false),

  // Queue
  QUEUE_REDIS_HOST: Joi.string().optional(),
  QUEUE_REDIS_PORT: Joi.number().port().optional(),
  QUEUE_REDIS_PASSWORD: Joi.string().optional(),
  QUEUE_REDIS_DB: Joi.number().min(0).max(15).default(3),
  QUEUE_REMOVE_ON_COMPLETE: Joi.number().min(0).default(10),
  QUEUE_REMOVE_ON_FAIL: Joi.number().min(0).default(5),
  QUEUE_ATTEMPTS: Joi.number().min(1).default(3),
  QUEUE_BACKOFF_DELAY: Joi.number().min(100).default(2000),
});
