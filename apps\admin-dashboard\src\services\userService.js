import { apiClient } from './apiClient';

class UserService {
  async getUsers(params = {}) {
    const response = await apiClient.get('/users', { params });
    return response.data;
  }

  async getUserById(id) {
    const response = await apiClient.get(`/users/${id}`);
    return response.data;
  }

  async getCurrentUser() {
    const response = await apiClient.get('/users/me');
    return response.data;
  }

  async createUser(userData) {
    const response = await apiClient.post('/users', userData);
    return response.data;
  }

  async updateUser(id, userData) {
    const response = await apiClient.patch(`/users/${id}`, userData);
    return response.data;
  }

  async updateCurrentUser(userData) {
    const response = await apiClient.patch('/users/me', userData);
    return response.data;
  }

  async deleteUser(id) {
    const response = await apiClient.delete(`/users/${id}`);
    return response.data;
  }

  async activateUser(id) {
    const response = await apiClient.post(`/users/${id}/activate`);
    return response.data;
  }

  async deactivateUser(id) {
    const response = await apiClient.post(`/users/${id}/deactivate`);
    return response.data;
  }

  async getUserRoles(id) {
    const response = await apiClient.get(`/users/${id}/roles`);
    return response.data;
  }

  async assignRole(userId, roleId) {
    const response = await apiClient.post(`/users/${userId}/roles/${roleId}`);
    return response.data;
  }

  async removeRole(userId, roleId) {
    const response = await apiClient.delete(`/users/${userId}/roles/${roleId}`);
    return response.data;
  }

  async getUserActivity(id) {
    const response = await apiClient.get(`/users/${id}/activity`);
    return response.data;
  }

  async bulkImport(users) {
    const response = await apiClient.post('/users/bulk-import', users);
    return response.data;
  }

  async bulkExport(filters = {}) {
    const response = await apiClient.post('/users/bulk-export', filters);
    return response.data;
  }

  async searchUsers(query) {
    const response = await apiClient.get('/users', {
      params: { search: query, limit: 10 }
    });
    return response.data;
  }

  async getUserStats() {
    const response = await apiClient.get('/users/stats');
    return response.data;
  }

  async changePassword(currentPassword, newPassword) {
    const response = await apiClient.post('/auth/change-password', {
      currentPassword,
      newPassword
    });
    return response.data;
  }

  async resetPassword(email) {
    const response = await apiClient.post('/auth/forgot-password', { email });
    return response.data;
  }

  async verifyEmail(token) {
    const response = await apiClient.post('/auth/verify-email', { token });
    return response.data;
  }

  async resendVerificationEmail(email) {
    const response = await apiClient.post('/auth/resend-verification', { email });
    return response.data;
  }

  async updateUserPreferences(preferences) {
    const response = await apiClient.patch('/users/me/preferences', preferences);
    return response.data;
  }

  async getUserPreferences() {
    const response = await apiClient.get('/users/me/preferences');
    return response.data;
  }

  async uploadAvatar(file) {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await apiClient.post('/users/me/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteAvatar() {
    const response = await apiClient.delete('/users/me/avatar');
    return response.data;
  }

  async getUserSessions() {
    const response = await apiClient.get('/users/me/sessions');
    return response.data;
  }

  async revokeSession(sessionId) {
    const response = await apiClient.delete(`/users/me/sessions/${sessionId}`);
    return response.data;
  }

  async revokeAllSessions() {
    const response = await apiClient.delete('/users/me/sessions');
    return response.data;
  }

  async enableTwoFactor() {
    const response = await apiClient.post('/auth/2fa/enable');
    return response.data;
  }

  async disableTwoFactor(code) {
    const response = await apiClient.post('/auth/2fa/disable', { code });
    return response.data;
  }

  async verifyTwoFactor(code) {
    const response = await apiClient.post('/auth/2fa/verify', { code });
    return response.data;
  }

  async generateBackupCodes() {
    const response = await apiClient.post('/auth/2fa/backup-codes');
    return response.data;
  }
}

export const userService = new UserService();
