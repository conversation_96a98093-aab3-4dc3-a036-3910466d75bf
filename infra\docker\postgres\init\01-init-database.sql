-- CloudForge Platform - Database Initialization
-- Enterprise-grade cloud services platform

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE cloudforge_platform'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'cloudforge_platform')\gexec

-- Connect to the database
\c cloudforge_platform;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS monitoring;
CREATE SCHEMA IF NOT EXISTS analytics;

-- Create audit function for tracking changes
CREATE OR REPLACE FUNCTION audit.audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit.audit_logs (
            table_name,
            operation,
            old_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            current_setting('app.current_user_id', true),
            NOW()
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit.audit_logs (
            table_name,
            operation,
            old_values,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            current_setting('app.current_user_id', true),
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit.audit_logs (
            table_name,
            operation,
            new_values,
            user_id,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(NEW),
            current_setting('app.current_user_id', true),
            NOW()
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create audit logs table
CREATE TABLE IF NOT EXISTS audit.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name TEXT NOT NULL,
    operation TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_id TEXT,
    ip_address INET
);

-- Create indexes for audit logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON audit.audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_operation ON audit.audit_logs(operation);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit.audit_logs(timestamp);

-- Create monitoring tables
CREATE TABLE IF NOT EXISTS monitoring.system_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name TEXT NOT NULL,
    metric_value NUMERIC NOT NULL,
    metric_unit TEXT,
    tags JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_system_metrics_name ON monitoring.system_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON monitoring.system_metrics(timestamp);

-- Create analytics tables
CREATE TABLE IF NOT EXISTS analytics.user_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    event_name TEXT NOT NULL,
    event_data JSONB,
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON analytics.user_events(user_id);
CREATE INDEX IF NOT EXISTS idx_user_events_event_name ON analytics.user_events(event_name);
CREATE INDEX IF NOT EXISTS idx_user_events_timestamp ON analytics.user_events(timestamp);

-- Create performance monitoring view
CREATE OR REPLACE VIEW monitoring.slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
ORDER BY mean_time DESC;

-- Create user activity summary view
CREATE OR REPLACE VIEW analytics.user_activity_summary AS
SELECT 
    user_id,
    DATE(timestamp) as activity_date,
    COUNT(*) as event_count,
    COUNT(DISTINCT session_id) as session_count,
    MIN(timestamp) as first_activity,
    MAX(timestamp) as last_activity
FROM analytics.user_events
GROUP BY user_id, DATE(timestamp);

-- Grant permissions
GRANT USAGE ON SCHEMA audit TO PUBLIC;
GRANT USAGE ON SCHEMA monitoring TO PUBLIC;
GRANT USAGE ON SCHEMA analytics TO PUBLIC;

GRANT SELECT ON ALL TABLES IN SCHEMA audit TO PUBLIC;
GRANT SELECT ON ALL TABLES IN SCHEMA monitoring TO PUBLIC;
GRANT SELECT ON ALL TABLES IN SCHEMA analytics TO PUBLIC;

-- Create application user
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'cloudforge_app') THEN
        CREATE USER cloudforge_app WITH PASSWORD 'cloudforge_app_password';
    END IF;
END
$$;

-- Grant permissions to application user
GRANT CONNECT ON DATABASE cloudforge_platform TO cloudforge_app;
GRANT USAGE ON SCHEMA public TO cloudforge_app;
GRANT USAGE ON SCHEMA audit TO cloudforge_app;
GRANT USAGE ON SCHEMA monitoring TO cloudforge_app;
GRANT USAGE ON SCHEMA analytics TO cloudforge_app;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cloudforge_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cloudforge_app;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO cloudforge_app;

GRANT INSERT ON ALL TABLES IN SCHEMA audit TO cloudforge_app;
GRANT INSERT ON ALL TABLES IN SCHEMA monitoring TO cloudforge_app;
GRANT INSERT ON ALL TABLES IN SCHEMA analytics TO cloudforge_app;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO cloudforge_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO cloudforge_app;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO cloudforge_app;

-- Create backup user
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'cloudforge_backup') THEN
        CREATE USER cloudforge_backup WITH PASSWORD 'cloudforge_backup_password';
    END IF;
END
$$;

-- Grant read-only permissions to backup user
GRANT CONNECT ON DATABASE cloudforge_platform TO cloudforge_backup;
GRANT USAGE ON SCHEMA public, audit, monitoring, analytics TO cloudforge_backup;
GRANT SELECT ON ALL TABLES IN SCHEMA public, audit, monitoring, analytics TO cloudforge_backup;

-- Create read-only user for analytics
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'cloudforge_readonly') THEN
        CREATE USER cloudforge_readonly WITH PASSWORD 'cloudforge_readonly_password';
    END IF;
END
$$;

-- Grant read-only permissions
GRANT CONNECT ON DATABASE cloudforge_platform TO cloudforge_readonly;
GRANT USAGE ON SCHEMA public, audit, monitoring, analytics TO cloudforge_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public, audit, monitoring, analytics TO cloudforge_readonly;

-- Set up connection limits
ALTER USER cloudforge_app CONNECTION LIMIT 50;
ALTER USER cloudforge_backup CONNECTION LIMIT 5;
ALTER USER cloudforge_readonly CONNECTION LIMIT 10;

-- Log initialization completion
INSERT INTO monitoring.system_metrics (metric_name, metric_value, metric_unit)
VALUES ('database_initialization', 1, 'boolean');
