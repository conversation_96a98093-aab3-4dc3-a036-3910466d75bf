#!/bin/bash

# CloudForge Platform - Setup Script
# Enterprise-grade cloud services platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        warn "Node.js is not installed. Some development features may not work."
    else
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VERSION" -lt 18 ]; then
            warn "Node.js version 18 or higher is recommended. Current version: $(node --version)"
        fi
    fi
    
    # Check available disk space (minimum 10GB)
    AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
    REQUIRED_SPACE=10485760  # 10GB in KB
    
    if [ "$AVAILABLE_SPACE" -lt "$REQUIRED_SPACE" ]; then
        warn "Low disk space. At least 10GB is recommended for CloudForge Platform."
    fi
    
    # Check available memory (minimum 4GB)
    AVAILABLE_MEMORY=$(free -m | awk 'NR==2{print $7}')
    REQUIRED_MEMORY=4096  # 4GB in MB
    
    if [ "$AVAILABLE_MEMORY" -lt "$REQUIRED_MEMORY" ]; then
        warn "Low available memory. At least 4GB is recommended for CloudForge Platform."
    fi
    
    log "System requirements check completed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/prometheus
    mkdir -p data/grafana
    mkdir -p data/elasticsearch
    mkdir -p infra/docker/ssl
    mkdir -p backups
    
    log "Directories created successfully"
}

# Generate SSL certificates for development
generate_ssl_certificates() {
    log "Generating SSL certificates for development..."
    
    SSL_DIR="infra/docker/ssl"
    
    if [ ! -f "$SSL_DIR/cloudforge.crt" ] || [ ! -f "$SSL_DIR/cloudforge.key" ]; then
        # Create a configuration file for the certificate
        cat > "$SSL_DIR/cloudforge.conf" << EOF
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = CA
L = San Francisco
O = CloudForge Platform
OU = Development
CN = cloudforge.local

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = cloudforge.local
DNS.2 = api.cloudforge.local
DNS.3 = admin.cloudforge.local
DNS.4 = localhost
IP.1 = 127.0.0.1
EOF

        # Generate private key
        openssl genrsa -out "$SSL_DIR/cloudforge.key" 2048
        
        # Generate certificate
        openssl req -new -x509 -key "$SSL_DIR/cloudforge.key" -out "$SSL_DIR/cloudforge.crt" -days 365 -config "$SSL_DIR/cloudforge.conf"
        
        # Set appropriate permissions
        chmod 600 "$SSL_DIR/cloudforge.key"
        chmod 644 "$SSL_DIR/cloudforge.crt"
        
        log "SSL certificates generated successfully"
    else
        info "SSL certificates already exist"
    fi
}

# Setup environment files
setup_environment() {
    log "Setting up environment files..."
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# CloudForge Platform - Environment Configuration
# Enterprise-grade cloud services platform

# Environment
NODE_ENV=development
APP_ENV=development

# Database Configuration
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USERNAME=cloudforge
DATABASE_PASSWORD=cloudforge_password
DATABASE_NAME=cloudforge_platform
DATABASE_SSL=false

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=cloudforge_redis_password
REDIS_DB=0

# JWT Configuration
JWT_SECRET=development-jwt-secret-key-change-in-production
JWT_REFRESH_SECRET=development-refresh-secret-key-change-in-production
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# API Configuration
API_PORT=3000
API_PREFIX=api/v1
API_RATE_LIMIT=100

# External Services (Development)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
SENDGRID_API_KEY=your_sendgrid_api_key_here
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-west-2

# Monitoring
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
CORS_ORIGIN=http://localhost:3010,https://admin.cloudforge.local

# Feature Flags
ENABLE_SWAGGER=true
ENABLE_METRICS=true
ENABLE_HEALTH_CHECKS=true
EOF
        log "Environment file created"
    else
        info "Environment file already exists"
    fi
    
    # Create .env.example
    cp .env .env.example
    
    # Replace sensitive values in .env.example
    sed -i 's/cloudforge_password/your_database_password_here/g' .env.example
    sed -i 's/cloudforge_redis_password/your_redis_password_here/g' .env.example
    sed -i 's/development-jwt-secret-key-change-in-production/your_jwt_secret_here/g' .env.example
    sed -i 's/development-refresh-secret-key-change-in-production/your_refresh_secret_here/g' .env.example
    
    log "Environment setup completed"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    if command -v npm &> /dev/null; then
        # Install root dependencies
        npm install
        
        # Install dependencies for each service
        for service in apps/*/; do
            if [ -f "$service/package.json" ]; then
                info "Installing dependencies for $(basename "$service")"
                (cd "$service" && npm install)
            fi
        done
        
        # Install shared library dependencies
        for lib in libs/*/; do
            if [ -f "$lib/package.json" ]; then
                info "Installing dependencies for $(basename "$lib")"
                (cd "$lib" && npm install)
            fi
        done
        
        log "Dependencies installed successfully"
    else
        warn "Node.js not found. Skipping dependency installation."
    fi
}

# Setup database
setup_database() {
    log "Setting up database..."
    
    # Start only PostgreSQL for initial setup
    docker-compose up -d postgres
    
    # Wait for PostgreSQL to be ready
    info "Waiting for PostgreSQL to be ready..."
    until docker-compose exec postgres pg_isready -U cloudforge -d cloudforge_platform; do
        sleep 2
    done
    
    log "Database setup completed"
}

# Build Docker images
build_images() {
    log "Building Docker images..."
    
    # Build all services
    docker-compose build
    
    log "Docker images built successfully"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring..."
    
    # Create Grafana provisioning directories
    mkdir -p infra/docker/grafana/provisioning/datasources
    mkdir -p infra/docker/grafana/provisioning/dashboards
    mkdir -p infra/docker/grafana/dashboards
    
    # Create Prometheus datasource for Grafana
    cat > infra/docker/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    # Create dashboard provisioning config
    cat > infra/docker/grafana/provisioning/dashboards/dashboards.yml << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF
    
    log "Monitoring setup completed"
}

# Main setup function
main() {
    log "Starting CloudForge Platform setup..."
    
    check_root
    check_requirements
    create_directories
    generate_ssl_certificates
    setup_environment
    install_dependencies
    setup_database
    build_images
    setup_monitoring
    
    log "CloudForge Platform setup completed successfully!"
    echo
    info "Next steps:"
    echo "1. Review and update the .env file with your configuration"
    echo "2. Run 'docker-compose up -d' to start all services"
    echo "3. Access the admin dashboard at https://admin.cloudforge.local"
    echo "4. Access the API at https://api.cloudforge.local"
    echo "5. Access Grafana at http://localhost:3000 (admin/cloudforge_grafana_password)"
    echo "6. Access Prometheus at http://localhost:9090"
    echo
    warn "Don't forget to add the following entries to your /etc/hosts file:"
    echo "127.0.0.1 cloudforge.local"
    echo "127.0.0.1 api.cloudforge.local"
    echo "127.0.0.1 admin.cloudforge.local"
}

# Run main function
main "$@"
