/**
 * CloudForge Platform - Base Repository
 * Enterprise-grade cloud services platform
 */

import {
  Repository,
  FindOptionsWhere,
  FindManyOptions,
  FindOneOptions,
  DeepPartial,
  UpdateResult,
  DeleteResult,
  SelectQueryBuilder,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { PaginationOptions, PaginatedResult } from '@cloudforge/shared';

export abstract class BaseRepository<T extends BaseEntity> {
  constructor(protected readonly repository: Repository<T>) {}

  /**
   * Create a new entity
   */
  async create(entityData: DeepPartial<T>, createdBy?: string): Promise<T> {
    const entity = this.repository.create(entityData);
    if (createdBy) {
      entity.createdBy = createdBy;
    }
    return this.repository.save(entity);
  }

  /**
   * Create multiple entities
   */
  async createMany(entitiesData: DeepPartial<T>[], createdBy?: string): Promise<T[]> {
    const entities = entitiesData.map(data => {
      const entity = this.repository.create(data);
      if (createdBy) {
        entity.createdBy = createdBy;
      }
      return entity;
    });
    return this.repository.save(entities);
  }

  /**
   * Find entity by ID
   */
  async findById(id: string, options?: FindOneOptions<T>): Promise<T | null> {
    return this.repository.findOne({
      where: { id } as FindOptionsWhere<T>,
      ...options,
    });
  }

  /**
   * Find one entity by criteria
   */
  async findOne(options: FindOneOptions<T>): Promise<T | null> {
    return this.repository.findOne(options);
  }

  /**
   * Find multiple entities
   */
  async find(options?: FindManyOptions<T>): Promise<T[]> {
    return this.repository.find(options);
  }

  /**
   * Find entities with pagination
   */
  async findWithPagination(
    options: FindManyOptions<T> = {},
    paginationOptions: PaginationOptions
  ): Promise<PaginatedResult<T>> {
    const { page, limit, sortBy, sortOrder } = paginationOptions;
    
    const skip = (page - 1) * limit;
    const take = limit;

    // Add sorting if specified
    if (sortBy) {
      options.order = {
        [sortBy]: sortOrder || 'ASC',
      } as any;
    }

    const [data, total] = await this.repository.findAndCount({
      ...options,
      skip,
      take,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Update entity by ID
   */
  async updateById(
    id: string,
    updateData: Partial<T>,
    updatedBy?: string
  ): Promise<T | null> {
    const entity = await this.findById(id);
    if (!entity) {
      return null;
    }

    Object.assign(entity, updateData);
    if (updatedBy) {
      entity.updatedBy = updatedBy;
    }

    return this.repository.save(entity);
  }

  /**
   * Update entities by criteria
   */
  async update(
    criteria: FindOptionsWhere<T>,
    updateData: Partial<T>
  ): Promise<UpdateResult> {
    return this.repository.update(criteria, updateData as any);
  }

  /**
   * Soft delete entity by ID
   */
  async softDeleteById(id: string, deletedBy?: string): Promise<boolean> {
    const entity = await this.findById(id);
    if (!entity) {
      return false;
    }

    entity.softDelete(deletedBy);
    await this.repository.save(entity);
    return true;
  }

  /**
   * Soft delete entities by criteria
   */
  async softDelete(criteria: FindOptionsWhere<T>): Promise<UpdateResult> {
    return this.repository.softDelete(criteria);
  }

  /**
   * Hard delete entity by ID
   */
  async deleteById(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected > 0;
  }

  /**
   * Hard delete entities by criteria
   */
  async delete(criteria: FindOptionsWhere<T>): Promise<DeleteResult> {
    return this.repository.delete(criteria);
  }

  /**
   * Restore soft deleted entity by ID
   */
  async restoreById(id: string): Promise<boolean> {
    const entity = await this.repository.findOne({
      where: { id } as FindOptionsWhere<T>,
      withDeleted: true,
    });

    if (!entity || !entity.isDeleted()) {
      return false;
    }

    entity.restore();
    await this.repository.save(entity);
    return true;
  }

  /**
   * Restore soft deleted entities by criteria
   */
  async restore(criteria: FindOptionsWhere<T>): Promise<UpdateResult> {
    return this.repository.restore(criteria);
  }

  /**
   * Count entities
   */
  async count(options?: FindManyOptions<T>): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Check if entity exists
   */
  async exists(criteria: FindOptionsWhere<T>): Promise<boolean> {
    const count = await this.repository.count({ where: criteria });
    return count > 0;
  }

  /**
   * Create query builder
   */
  createQueryBuilder(alias?: string): SelectQueryBuilder<T> {
    return this.repository.createQueryBuilder(alias);
  }

  /**
   * Execute raw query
   */
  async query(sql: string, parameters?: any[]): Promise<any> {
    return this.repository.query(sql, parameters);
  }

  /**
   * Save entity
   */
  async save(entity: T): Promise<T> {
    return this.repository.save(entity);
  }

  /**
   * Save multiple entities
   */
  async saveMany(entities: T[]): Promise<T[]> {
    return this.repository.save(entities);
  }

  /**
   * Reload entity from database
   */
  async reload(entity: T): Promise<T> {
    return this.repository.reload(entity);
  }

  /**
   * Get repository instance
   */
  getRepository(): Repository<T> {
    return this.repository;
  }

  /**
   * Find entities with advanced search
   */
  async search(
    searchTerm: string,
    searchFields: (keyof T)[],
    options?: FindManyOptions<T>
  ): Promise<T[]> {
    const queryBuilder = this.createQueryBuilder('entity');

    // Add search conditions
    const searchConditions = searchFields.map((field, index) => {
      const paramName = `search${index}`;
      queryBuilder.setParameter(paramName, `%${searchTerm}%`);
      return `entity.${String(field)} ILIKE :${paramName}`;
    });

    if (searchConditions.length > 0) {
      queryBuilder.where(`(${searchConditions.join(' OR ')})`);
    }

    // Apply additional options
    if (options?.where) {
      queryBuilder.andWhere(options.where as any);
    }

    if (options?.order) {
      Object.entries(options.order).forEach(([field, direction]) => {
        queryBuilder.addOrderBy(`entity.${field}`, direction as 'ASC' | 'DESC');
      });
    }

    if (options?.take) {
      queryBuilder.take(options.take);
    }

    if (options?.skip) {
      queryBuilder.skip(options.skip);
    }

    return queryBuilder.getMany();
  }
}
