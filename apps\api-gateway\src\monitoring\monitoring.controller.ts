/**
 * CloudForge Platform - Monitoring Controller (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Controller, Get, Post, Query, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MonitoringService } from './monitoring.service';

@ApiTags('Monitoring')
@ApiBearerAuth('JWT-auth')
@Controller('monitoring')
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  @Get('metrics')
  @ApiOperation({ summary: 'Get system metrics' })
  @ApiResponse({ status: 200, description: 'Metrics retrieved successfully' })
  async getMetrics(@Query() query: any) {
    return this.monitoringService.getMetrics(query);
  }

  @Get('logs')
  @ApiOperation({ summary: 'Get system logs' })
  @ApiResponse({ status: 200, description: 'Logs retrieved successfully' })
  async getLogs(@Query() query: any) {
    return this.monitoringService.getLogs(query);
  }

  @Get('alerts')
  @ApiOperation({ summary: 'Get system alerts' })
  @ApiResponse({ status: 200, description: 'Alerts retrieved successfully' })
  async getAlerts(@Query() query: any) {
    return this.monitoringService.getAlerts(query);
  }

  @Post('alerts')
  @ApiOperation({ summary: 'Create alert rule' })
  @ApiResponse({ status: 201, description: 'Alert rule created successfully' })
  async createAlert(@Body() alertData: any) {
    return this.monitoringService.createAlert(alertData);
  }

  @Get('dashboards')
  @ApiOperation({ summary: 'Get monitoring dashboards' })
  @ApiResponse({ status: 200, description: 'Dashboards retrieved successfully' })
  async getDashboards() {
    return this.monitoringService.getDashboards();
  }

  @Get('health-checks')
  @ApiOperation({ summary: 'Get health check results' })
  @ApiResponse({ status: 200, description: 'Health checks retrieved successfully' })
  async getHealthChecks() {
    return this.monitoringService.getHealthChecks();
  }

  @Get('performance')
  @ApiOperation({ summary: 'Get performance metrics' })
  @ApiResponse({ status: 200, description: 'Performance metrics retrieved successfully' })
  async getPerformance(@Query() query: any) {
    return this.monitoringService.getPerformance(query);
  }

  @Get('traces')
  @ApiOperation({ summary: 'Get distributed traces' })
  @ApiResponse({ status: 200, description: 'Traces retrieved successfully' })
  async getTraces(@Query() query: any) {
    return this.monitoringService.getTraces(query);
  }
}
