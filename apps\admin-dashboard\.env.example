# CloudForge Platform - Frontend Environment Variables
# Created by <PERSON><PERSON>
# Copy this file to .env.local and fill in your values

# ===========================================
# API CONFIGURATION
# ===========================================
REACT_APP_API_URL=http://localhost:3001/api/v1
REACT_APP_WS_URL=ws://localhost:3001
REACT_APP_APP_NAME="CloudForge Platform"
REACT_APP_APP_VERSION=1.0.0

# ===========================================
# AUTHENTICATION
# ===========================================
REACT_APP_JWT_STORAGE_KEY=cloudforge_token
REACT_APP_REFRESH_TOKEN_KEY=cloudforge_refresh_token
REACT_APP_USER_STORAGE_KEY=cloudforge_user

# ===========================================
# FEATURES FLAGS
# ===========================================
REACT_APP_ENABLE_AI_FEATURES=true
REACT_APP_ENABLE_QUANTUM_PROCESSING=true
REACT_APP_ENABLE_CONSCIOUSNESS_ENGINE=true
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_BILLING=true
REACT_APP_ENABLE_NOTIFICATIONS=true

# ===========================================
# AI CONFIGURATION
# ===========================================
REACT_APP_AI_MODEL_VERSION=v1
REACT_APP_QUANTUM_QUBITS=1000000
REACT_APP_CONSCIOUSNESS_LEVEL=95
REACT_APP_AI_RESPONSE_TIMEOUT=30000

# ===========================================
# ANALYTICS
# ===========================================
REACT_APP_ANALYTICS_ENABLED=true
REACT_APP_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
REACT_APP_MIXPANEL_TOKEN=your_mixpanel_token
REACT_APP_HOTJAR_ID=your_hotjar_id

# ===========================================
# MONITORING
# ===========================================
REACT_APP_SENTRY_DSN=https://<EMAIL>/project-id
REACT_APP_LOG_LEVEL=info
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true

# ===========================================
# THEME & UI
# ===========================================
REACT_APP_DEFAULT_THEME=light
REACT_APP_ENABLE_DARK_MODE=true
REACT_APP_PRIMARY_COLOR=#1976d2
REACT_APP_SECONDARY_COLOR=#9c27b0

# ===========================================
# DEVELOPMENT
# ===========================================
REACT_APP_DEBUG=true
REACT_APP_MOCK_DATA=false
REACT_APP_ENABLE_DEVTOOLS=true

# ===========================================
# PRODUCTION OVERRIDES
# ===========================================
# Uncomment and modify for production deployment
# REACT_APP_API_URL=https://api.your-domain.com/api/v1
# REACT_APP_WS_URL=wss://api.your-domain.com
# REACT_APP_DEBUG=false
# REACT_APP_MOCK_DATA=false
# REACT_APP_LOG_LEVEL=warn
