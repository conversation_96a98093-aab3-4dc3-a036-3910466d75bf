import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';
import { theme } from '../theme/theme';
import { AuthProvider } from '../contexts/AuthContext';
import { AppProvider } from '../contexts/AppContext';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'CloudForge Platform - Transcendent Excellence at €0.001/user/month',
  description: 'The most advanced enterprise platform ever created. Consciousness-level AI, quantum-enhanced processing, 50+ years maintenance-free operation. Created by <PERSON><PERSON>.',
  keywords: [
    'CloudForge',
    'AI Platform',
    'Enterprise Software',
    'Consciousness AI',
    'Quantum Computing',
    'Cost Efficient',
    '<PERSON><PERSON>',
  ],
  authors: [{ name: '<PERSON><PERSON>' }],
  creator: '<PERSON><PERSON>',
  publisher: 'CloudForge <PERSON>',
  robots: 'index, follow',
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#1976d2',
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://cloudforge.com',
    title: 'CloudForge Platform - Transcendent Excellence',
    description: 'Beyond Google & Amazon\'s Combined Capabilities at €0.001/user/month',
    siteName: 'CloudForge Platform',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'CloudForge Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CloudForge Platform - Transcendent Excellence',
    description: 'The most advanced enterprise platform ever created',
    images: ['/twitter-image.png'],
    creator: '@MarwanElQaouti',
  },
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="msapplication-TileColor" content="#1976d2" />
        <meta name="theme-color" content="#1976d2" />
        
        {/* Performance optimizations */}
        <link rel="dns-prefetch" href="//api.cloudforge.com" />
        <link rel="preconnect" href="https://api.cloudforge.com" />
        
        {/* CloudForge Platform branding */}
        <meta name="application-name" content="CloudForge Platform" />
        <meta name="apple-mobile-web-app-title" content="CloudForge" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        
        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        
        {/* Structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'SoftwareApplication',
              name: 'CloudForge Platform',
              description: 'Transcendent Excellence at €0.001/user/month',
              applicationCategory: 'BusinessApplication',
              operatingSystem: 'Web',
              offers: {
                '@type': 'Offer',
                price: '0.001',
                priceCurrency: 'EUR',
                priceSpecification: {
                  '@type': 'UnitPriceSpecification',
                  price: '0.001',
                  priceCurrency: 'EUR',
                  unitText: 'per user per month',
                },
              },
              creator: {
                '@type': 'Person',
                name: 'Marwan El-Qaouti',
              },
              publisher: {
                '@type': 'Organization',
                name: 'CloudForge Platform',
              },
            }),
          }}
        />
      </head>
      <body className={inter.className}>
        <HelmetProvider>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              <AuthProvider>
                <AppProvider>
                  {children}
                  <Toaster
                    position="top-right"
                    toastOptions={{
                      duration: 4000,
                      style: {
                        background: '#1976d2',
                        color: '#fff',
                        fontWeight: 500,
                      },
                      success: {
                        iconTheme: {
                          primary: '#4caf50',
                          secondary: '#fff',
                        },
                      },
                      error: {
                        iconTheme: {
                          primary: '#f44336',
                          secondary: '#fff',
                        },
                      },
                    }}
                  />
                </AppProvider>
              </AuthProvider>
            </ThemeProvider>
          </QueryClientProvider>
        </HelmetProvider>
      </body>
    </html>
  );
}
