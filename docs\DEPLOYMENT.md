# Deployment Guide

## CI/CD

- Uses GitHub Actions for build, test, and deployment.
- See `.github/workflows/` for pipeline configuration.

## Kubernetes

1. Ensure your cluster is running.
2. Run the deployment script:
   ```
   ./scripts/deploy.sh
   ```
3. Monitor rollout:
   ```
   kubectl get pods -n enterprise-platform
   ```

## Infrastructure as Code

- All resources are managed via Terraform (`infra/terraform/`).
- Kubernetes manifests in `infra/k8s/`.

## Post-Deployment

- Access API Gateway and Admin Panel using the service IPs.
