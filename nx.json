{"$schema": "./node_modules/nx/schemas/nx-schema.json", "npmScope": "cloudforge", "affected": {"defaultBase": "origin/main"}, "cli": {"packageManager": "npm"}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "e2e": {"inputs": ["default", "^production"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "generators": {"@nx/react": {"application": {"style": "scss", "linter": "eslint", "bundler": "webpack"}, "component": {"style": "scss"}, "library": {"style": "scss", "linter": "eslint"}}, "@nx/nest": {"application": {"linter": "eslint"}}}, "defaultProject": "api-gateway"}