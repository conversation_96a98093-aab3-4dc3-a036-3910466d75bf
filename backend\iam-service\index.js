const express = require('express');
const jwt = require('jsonwebtoken');
const app = express();

app.use(express.json());

const users = [{ username: 'admin', password: 'admin', role: 'admin' }];

function authenticate(req, res, next) {
  const auth = req.headers.authorization;
  if (!auth) return res.sendStatus(401);
  try {
    req.user = jwt.verify(auth.split(' ')[1], 'secret');
    next();
  } catch {
    res.sendStatus(403);
  }
}

function authorize(role) {
  return (req, res, next) => {
    if (req.user.role !== role) return res.sendStatus(403);
    next();
  };
}

// Example: User registration endpoint
app.post('/register', (req, res) => {
  const { username, password, role } = req.body;
  users.push({ username, password, role: role || 'user' });
  res.status(201).json({ message: 'User registered' });
});

// Example: Login endpoint
app.post('/login', (req, res) => {
  const { username, password } = req.body;
  const user = users.find(u => u.username === username && u.password === password);
  if (!user) return res.sendStatus(401);
  const token = jwt.sign({ username: user.username, role: user.role }, 'secret');
  res.json({ token });
});

app.get('/me', authenticate, (req, res) => {
  res.json({ user: req.user });
});

app.get('/admin', authenticate, authorize('admin'), (req, res) => {
  res.json({ message: 'Admin access granted' });
});

app.listen(4001, () => {
  console.log('IAM Service running on port 4001');
});
