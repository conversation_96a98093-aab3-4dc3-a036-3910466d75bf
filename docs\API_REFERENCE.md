# CloudForge Platform - Complete API Reference

**Enterprise API Documentation**
**RESTful APIs for €60M Platform Integration**

---

## 🎯 API Overview

CloudForge Platform provides **comprehensive RESTful APIs** designed for enterprise integration, supporting the **€60 million investment** with robust, scalable, and secure API endpoints.

### API Characteristics
- **RESTful Design**: Standard HTTP methods and status codes
- **OpenAPI 3.0**: Complete API specification and documentation
- **Authentication**: JWT-based authentication with OAuth2 support
- **Rate Limiting**: Enterprise-grade rate limiting and throttling
- **Versioning**: API versioning for backward compatibility
- **Monitoring**: Comprehensive API analytics and monitoring

### Base URL
```
Production: https://api.cloudforge.com/v1
Staging: https://staging-api.cloudforge.com/v1
```

---

## 🔐 Authentication & Authorization

### 1. Authentication Methods

#### JWT Authentication
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "mfaCode": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "roles": ["admin", "user"]
    }
  }
}
```

#### OAuth2 Integration
```http
GET /auth/oauth2/authorize?client_id=CLIENT_ID&response_type=code&redirect_uri=REDIRECT_URI&scope=read write
```

#### API Key Authentication
```http
GET /users
Authorization: Bearer YOUR_JWT_TOKEN
X-API-Key: your-api-key-here
```

### 2. Authorization Scopes

```yaml
# API Authorization Scopes
scopes:
  read: "Read access to resources"
  write: "Write access to resources"
  admin: "Administrative access"
  billing: "Billing and payment access"
  analytics: "Analytics and reporting access"
  system: "System administration access"
```

---

## 👥 User Management API

### 3. User Operations

#### Create User
```http
POST /users
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "roles": ["user"],
  "metadata": {
    "department": "Engineering",
    "location": "New York"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_456",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["user"],
    "status": "active",
    "createdAt": "2024-12-17T10:00:00Z",
    "metadata": {
      "department": "Engineering",
      "location": "New York"
    }
  }
}
```

#### Get User
```http
GET /users/{userId}
Authorization: Bearer YOUR_JWT_TOKEN
```

#### Update User
```http
PUT /users/{userId}
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "firstName": "John",
  "lastName": "Smith",
  "metadata": {
    "department": "Product",
    "location": "San Francisco"
  }
}
```

#### List Users
```http
GET /users?page=1&limit=20&role=admin&status=active&search=john
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_123",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "roles": ["admin"],
        "status": "active",
        "lastLoginAt": "2024-12-17T09:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

#### Delete User
```http
DELETE /users/{userId}
Authorization: Bearer YOUR_JWT_TOKEN
```
