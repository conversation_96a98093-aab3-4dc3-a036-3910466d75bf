# API Reference

## IAM Service

- `POST /register` - Register a new user
- `POST /login` - Authenticate and receive JWT
- `GET /me` - Get current user info (JWT required)
- `GET /admin` - Admin-only endpoint (JWT required)

## Resource Service

- `POST /vms` - Create a new VM
- `GET /vms` - List all VMs

## Billing Service

- `GET /usage` - Get usage data
- `POST /usage` - Record usage

## API Gateway

- Proxies all requests to respective services
