# Third-Party Licenses

CloudForge Platform incorporates several open-source libraries and components. This document lists all third-party dependencies and their respective licenses.

## Backend Dependencies

### Core Framework
- **NestJS** - MIT License
  - Version: 10.2.x
  - License: https://github.com/nestjs/nest/blob/master/LICENSE
  - Description: Progressive Node.js framework for building efficient and scalable server-side applications

- **Express.js** - MIT License
  - Version: 4.18.x
  - License: https://github.com/expressjs/express/blob/master/LICENSE
  - Description: Fast, unopinionated, minimalist web framework for Node.js

### Database & ORM
- **TypeORM** - MIT License
  - Version: 0.3.x
  - License: https://github.com/typeorm/typeorm/blob/master/LICENSE
  - Description: ORM for TypeScript and JavaScript

- **PostgreSQL Driver (pg)** - MIT License
  - Version: 8.11.x
  - License: https://github.com/brianc/node-postgres/blob/master/LICENSE
  - Description: PostgreSQL client for Node.js

- **Redis Client (ioredis)** - MIT License
  - Version: 5.3.x
  - License: https://github.com/luin/ioredis/blob/master/LICENSE
  - Description: Robust, performance-focused Redis client for Node.js

### Authentication & Security
- **jsonwebtoken** - MIT License
  - Version: 9.0.x
  - License: https://github.com/auth0/node-jsonwebtoken/blob/master/LICENSE
  - Description: JSON Web Token implementation for Node.js

- **bcrypt** - MIT License
  - Version: 5.1.x
  - License: https://github.com/kelektiv/node.bcrypt.js/blob/master/LICENSE
  - Description: Library for hashing passwords

- **helmet** - MIT License
  - Version: 7.1.x
  - License: https://github.com/helmetjs/helmet/blob/master/LICENSE
  - Description: Security middleware for Express.js

### Validation & Transformation
- **class-validator** - MIT License
  - Version: 0.14.x
  - License: https://github.com/typestack/class-validator/blob/develop/LICENSE
  - Description: Decorator-based property validation for classes

- **class-transformer** - MIT License
  - Version: 0.5.x
  - License: https://github.com/typestack/class-transformer/blob/develop/LICENSE
  - Description: Decorator-based transformation, serialization, and deserialization

### Utilities
- **lodash** - MIT License
  - Version: 4.17.x
  - License: https://github.com/lodash/lodash/blob/master/LICENSE
  - Description: Modern JavaScript utility library

- **moment** - MIT License
  - Version: 2.29.x
  - License: https://github.com/moment/moment/blob/develop/LICENSE
  - Description: Parse, validate, manipulate, and display dates in JavaScript

## Frontend Dependencies

### Core Framework
- **React** - MIT License
  - Version: 18.2.x
  - License: https://github.com/facebook/react/blob/main/LICENSE
  - Description: JavaScript library for building user interfaces

- **React DOM** - MIT License
  - Version: 18.2.x
  - License: https://github.com/facebook/react/blob/main/LICENSE
  - Description: React package for working with the DOM

### UI Framework
- **Material-UI (@mui/material)** - MIT License
  - Version: 5.14.x
  - License: https://github.com/mui/material-ui/blob/master/LICENSE
  - Description: React components implementing Google's Material Design

- **Material-UI Icons (@mui/icons-material)** - MIT License
  - Version: 5.14.x
  - License: https://github.com/mui/material-ui/blob/master/LICENSE
  - Description: Material Design icons as React components

### State Management
- **Redux Toolkit** - MIT License
  - Version: 1.9.x
  - License: https://github.com/reduxjs/redux-toolkit/blob/master/LICENSE
  - Description: Official, opinionated, batteries-included toolset for efficient Redux development

- **React Redux** - MIT License
  - Version: 8.1.x
  - License: https://github.com/reduxjs/react-redux/blob/master/LICENSE
  - Description: Official React bindings for Redux

### Data Fetching
- **React Query (@tanstack/react-query)** - MIT License
  - Version: 4.35.x
  - License: https://github.com/TanStack/query/blob/main/LICENSE
  - Description: Powerful data synchronization for React

- **Axios** - MIT License
  - Version: 1.5.x
  - License: https://github.com/axios/axios/blob/master/LICENSE
  - Description: Promise-based HTTP client for the browser and Node.js

### Routing
- **React Router DOM** - MIT License
  - Version: 6.16.x
  - License: https://github.com/remix-run/react-router/blob/main/LICENSE.md
  - Description: Declarative routing for React

### Charts & Visualization
- **Recharts** - MIT License
  - Version: 2.8.x
  - License: https://github.com/recharts/recharts/blob/master/LICENSE
  - Description: Redefined chart library built with React and D3

### Build Tools
- **Vite** - MIT License
  - Version: 4.4.x
  - License: https://github.com/vitejs/vite/blob/main/LICENSE
  - Description: Next generation frontend tooling

## Development Dependencies

### TypeScript
- **TypeScript** - Apache License 2.0
  - Version: 5.2.x
  - License: https://github.com/microsoft/TypeScript/blob/main/LICENSE.txt
  - Description: TypeScript is a superset of JavaScript that compiles to clean JavaScript output

### Testing
- **Jest** - MIT License
  - Version: 29.7.x
  - License: https://github.com/facebook/jest/blob/main/LICENSE
  - Description: Delightful JavaScript testing framework

- **Supertest** - MIT License
  - Version: 6.3.x
  - License: https://github.com/visionmedia/supertest/blob/master/LICENSE
  - Description: HTTP assertions made easy via superagent

### Code Quality
- **ESLint** - MIT License
  - Version: 8.50.x
  - License: https://github.com/eslint/eslint/blob/main/LICENSE
  - Description: Tool for identifying and reporting on patterns found in ECMAScript/JavaScript code

- **Prettier** - MIT License
  - Version: 3.0.x
  - License: https://github.com/prettier/prettier/blob/main/LICENSE
  - Description: Opinionated code formatter

## Infrastructure Dependencies

### Containerization
- **Docker** - Apache License 2.0
  - License: https://github.com/moby/moby/blob/master/LICENSE
  - Description: Platform for developing, shipping, and running applications

### Database
- **PostgreSQL** - PostgreSQL License
  - Version: 15.x
  - License: https://www.postgresql.org/about/licence/
  - Description: Advanced open source relational database

- **Redis** - BSD 3-Clause License
  - Version: 7.x
  - License: https://github.com/redis/redis/blob/unstable/COPYING
  - Description: In-memory data structure store

### Monitoring
- **Prometheus** - Apache License 2.0
  - License: https://github.com/prometheus/prometheus/blob/main/LICENSE
  - Description: Monitoring system and time series database

- **Grafana** - AGPL v3 License
  - Version: 10.x
  - License: https://github.com/grafana/grafana/blob/main/LICENSE
  - Description: Open source analytics and interactive visualization web application

### Load Balancing
- **Nginx** - BSD 2-Clause License
  - License: https://nginx.org/LICENSE
  - Description: HTTP and reverse proxy server, mail proxy server, and generic TCP/UDP proxy server

## External Services

### Payment Processing
- **Stripe** - Commercial Service
  - Documentation: https://stripe.com/docs
  - Terms: https://stripe.com/legal
  - Description: Online payment processing platform

### Email Services
- **SendGrid** - Commercial Service
  - Documentation: https://docs.sendgrid.com/
  - Terms: https://sendgrid.com/policies/tos/
  - Description: Cloud-based email delivery platform

### Cloud Storage
- **AWS S3** - Commercial Service
  - Documentation: https://docs.aws.amazon.com/s3/
  - Terms: https://aws.amazon.com/service-terms/
  - Description: Object storage service

## License Compliance

### MIT License Summary
Most dependencies use the MIT License, which permits:
- Commercial use
- Modification
- Distribution
- Private use

Requirements:
- Include license and copyright notice
- Include original license text

### Apache License 2.0 Summary
Some dependencies use Apache License 2.0, which permits:
- Commercial use
- Modification
- Distribution
- Patent use
- Private use

Requirements:
- Include license and copyright notice
- State changes made to the code

### BSD Licenses Summary
Some dependencies use BSD licenses, which permit:
- Commercial use
- Modification
- Distribution
- Private use

Requirements:
- Include license and copyright notice

## Acknowledgments

We thank all the open source contributors and maintainers whose work makes CloudForge Platform possible. Their dedication to creating high-quality, freely available software is invaluable to the development community.

## Updates

This document is updated regularly to reflect changes in dependencies. For the most current information, please refer to the package.json files in each service directory.

Last Updated: December 2024
