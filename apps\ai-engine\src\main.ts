import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('AIEngine');

  // Security middleware
  app.use(helmet());
  app.use(compression());

  // CORS configuration
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // API documentation
  const config = new DocumentBuilder()
    .setTitle('CloudForge AI Engine')
    .setDescription('Advanced AI and Machine Learning Platform - Proprietary Algorithms by <PERSON><PERSON>uti')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('ai', 'Artificial Intelligence Services')
    .addTag('ml', 'Machine Learning Models')
    .addTag('automation', 'Intelligent Automation')
    .addTag('analytics', 'Predictive Analytics')
    .addTag('nlp', 'Natural Language Processing')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Global prefix
  app.setGlobalPrefix('api/v1');

  const port = configService.get('PORT', 3006);
  await app.listen(port);

  logger.log(`🤖 AI Engine running on port ${port}`);
  logger.log(`🧠 Proprietary AI Algorithms by Marwan El-Qaouti`);
  logger.log(`📚 API Documentation available at http://localhost:${port}/api/docs`);
}

bootstrap().catch((error) => {
  console.error('Failed to start AI Engine:', error);
  process.exit(1);
});
