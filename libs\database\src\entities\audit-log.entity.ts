/**
 * CloudForge Platform - Audit Log Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { User } from './user.entity';

@Entity('audit_logs')
@Index(['userId'])
@Index(['action'])
@Index(['resource'])
@Index(['resourceId'])
@Index(['ipAddress'])
@Index(['createdAt'])
@Index(['action', 'resource'])
export class AuditLog extends BaseEntity {
  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who performed the action',
  })
  userId?: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Action performed (create, update, delete, etc.)',
  })
  action: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Resource type affected',
  })
  resource: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'ID of the affected resource',
  })
  resourceId?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Human-readable description of the action',
  })
  description?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Previous state of the resource (for updates)',
  })
  oldValues?: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'New state of the resource (for creates/updates)',
  })
  newValues?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address of the user',
  })
  ipAddress?: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'User agent string',
  })
  userAgent?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Session ID',
  })
  sessionId?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Request ID for tracing',
  })
  requestId?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Service that performed the action',
  })
  service?: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'HTTP method used',
  })
  method?: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'URL path accessed',
  })
  path?: string;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'HTTP status code',
  })
  statusCode?: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Request duration in milliseconds',
  })
  duration?: number;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Risk level of the action',
  })
  riskLevel?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this action was successful',
  })
  success: boolean;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Error message if action failed',
  })
  errorMessage?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional context or metadata',
  })
  auditMetadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User, user => user.auditLogs)
  @JoinColumn({ name: 'userId' })
  user?: User;

  // Methods
  isSuccessful(): boolean {
    return this.success;
  }

  isFailed(): boolean {
    return !this.success;
  }

  isHighRisk(): boolean {
    return this.riskLevel === 'high' || this.riskLevel === 'critical';
  }

  isCreate(): boolean {
    return this.action.toLowerCase() === 'create';
  }

  isUpdate(): boolean {
    return this.action.toLowerCase() === 'update';
  }

  isDelete(): boolean {
    return this.action.toLowerCase() === 'delete';
  }

  isRead(): boolean {
    return this.action.toLowerCase() === 'read' || this.action.toLowerCase() === 'view';
  }

  getChangedFields(): string[] {
    if (!this.oldValues || !this.newValues) return [];
    
    const changedFields: string[] = [];
    
    for (const key in this.newValues) {
      if (this.oldValues[key] !== this.newValues[key]) {
        changedFields.push(key);
      }
    }
    
    return changedFields;
  }

  getFieldChange(field: string): { old: any; new: any } | null {
    if (!this.oldValues || !this.newValues) return null;
    
    if (this.oldValues[field] !== this.newValues[field]) {
      return {
        old: this.oldValues[field],
        new: this.newValues[field],
      };
    }
    
    return null;
  }

  hasFieldChanged(field: string): boolean {
    return this.getFieldChange(field) !== null;
  }

  getDurationInSeconds(): number | null {
    return this.duration ? this.duration / 1000 : null;
  }

  isSlowRequest(thresholdMs: number = 5000): boolean {
    return this.duration ? this.duration > thresholdMs : false;
  }

  updateMetadata(key: string, value: any): void {
    if (!this.auditMetadata) {
      this.auditMetadata = {};
    }
    this.auditMetadata[key] = value;
  }

  getMetadata(key: string, defaultValue?: any): any {
    return this.auditMetadata?.[key] ?? defaultValue;
  }

  static createLog(data: {
    userId?: string;
    action: string;
    resource: string;
    resourceId?: string;
    description?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
    requestId?: string;
    service?: string;
    method?: string;
    path?: string;
    statusCode?: number;
    duration?: number;
    riskLevel?: string;
    success?: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }): Partial<AuditLog> {
    return {
      userId: data.userId,
      action: data.action,
      resource: data.resource,
      resourceId: data.resourceId,
      description: data.description,
      oldValues: data.oldValues,
      newValues: data.newValues,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      sessionId: data.sessionId,
      requestId: data.requestId,
      service: data.service,
      method: data.method,
      path: data.path,
      statusCode: data.statusCode,
      duration: data.duration,
      riskLevel: data.riskLevel || 'low',
      success: data.success ?? true,
      errorMessage: data.errorMessage,
      auditMetadata: data.metadata,
    };
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    
    // Add computed fields
    obj.isSuccessful = this.isSuccessful();
    obj.isFailed = this.isFailed();
    obj.isHighRisk = this.isHighRisk();
    obj.isCreate = this.isCreate();
    obj.isUpdate = this.isUpdate();
    obj.isDelete = this.isDelete();
    obj.isRead = this.isRead();
    obj.changedFields = this.getChangedFields();
    obj.durationInSeconds = this.getDurationInSeconds();
    obj.isSlowRequest = this.isSlowRequest();
    
    return obj;
  }
}
