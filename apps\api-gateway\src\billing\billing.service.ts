/**
 * CloudForge Platform - Billing Service (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name);
  private readonly billingServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.billingServiceUrl = this.configService.get<string>('services.billingService.url');
  }

  async getSubscriptions(query: any) {
    try {
      this.logger.log('Proxying get subscriptions request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.billingServiceUrl}/subscriptions`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get subscriptions request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async createSubscription(subscriptionData: any) {
    try {
      this.logger.log('Proxying create subscription request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.billingServiceUrl}/subscriptions`, subscriptionData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Create subscription request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async updateSubscription(id: string, updateData: any) {
    try {
      this.logger.log(`Proxying update subscription request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.billingServiceUrl}/subscriptions/${id}`, updateData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Update subscription request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async cancelSubscription(id: string) {
    try {
      this.logger.log(`Proxying cancel subscription request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.delete(`${this.billingServiceUrl}/subscriptions/${id}`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Cancel subscription request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getPlans() {
    try {
      this.logger.log('Proxying get plans request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.billingServiceUrl}/plans`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get plans request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getInvoices(query: any) {
    try {
      this.logger.log('Proxying get invoices request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.billingServiceUrl}/invoices`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get invoices request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getUsage(query: any) {
    try {
      this.logger.log('Proxying get usage request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.billingServiceUrl}/usage`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get usage request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async addPaymentMethod(paymentMethodData: any) {
    try {
      this.logger.log('Proxying add payment method request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.billingServiceUrl}/payment-methods`, paymentMethodData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Add payment method request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getPaymentMethods() {
    try {
      this.logger.log('Proxying get payment methods request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.billingServiceUrl}/payment-methods`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get payment methods request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async removePaymentMethod(id: string) {
    try {
      this.logger.log(`Proxying remove payment method request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.delete(`${this.billingServiceUrl}/payment-methods/${id}`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Remove payment method request failed', error.response?.data || error.message);
      throw error;
    }
  }
}
