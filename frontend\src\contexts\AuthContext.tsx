'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authApi } from '../services/api';
import { toast } from 'react-hot-toast';

interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: string;
  organizationId?: string;
  organization?: {
    id: string;
    name: string;
    plan: string;
  };
}

interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string; refreshToken: string } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'CLEAR_ERROR' };

const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...initialState,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    email: string;
    password: string;
    username: string;
    firstName?: string;
    lastName?: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load auth state from localStorage on mount
  useEffect(() => {
    const token = localStorage.getItem('cloudforge_token');
    const refreshToken = localStorage.getItem('cloudforge_refresh_token');
    const userData = localStorage.getItem('cloudforge_user');

    if (token && refreshToken && userData) {
      try {
        const user = JSON.parse(userData);
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user, token, refreshToken },
        });
      } catch (error) {
        // Clear invalid data
        localStorage.removeItem('cloudforge_token');
        localStorage.removeItem('cloudforge_refresh_token');
        localStorage.removeItem('cloudforge_user');
      }
    }
  }, []);

  // Save auth state to localStorage
  useEffect(() => {
    if (state.isAuthenticated && state.token && state.refreshToken && state.user) {
      localStorage.setItem('cloudforge_token', state.token);
      localStorage.setItem('cloudforge_refresh_token', state.refreshToken);
      localStorage.setItem('cloudforge_user', JSON.stringify(state.user));
    } else {
      localStorage.removeItem('cloudforge_token');
      localStorage.removeItem('cloudforge_refresh_token');
      localStorage.removeItem('cloudforge_user');
    }
  }, [state.isAuthenticated, state.token, state.refreshToken, state.user]);

  const login = async (email: string, password: string) => {
    dispatch({ type: 'AUTH_START' });
    
    try {
      const response = await authApi.login({ email, password });
      
      if (response.success) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: response.data.user,
            token: response.data.accessToken,
            refreshToken: response.data.refreshToken,
          },
        });
        
        toast.success('Welcome to CloudForge Platform! 🚀');
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Login failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  const register = async (userData: {
    email: string;
    password: string;
    username: string;
    firstName?: string;
    lastName?: string;
  }) => {
    dispatch({ type: 'AUTH_START' });
    
    try {
      const response = await authApi.register(userData);
      
      if (response.success) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: response.data.user,
            token: response.data.accessToken,
            refreshToken: response.data.refreshToken,
          },
        });
        
        toast.success('Welcome to CloudForge Platform! 🌟');
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Registration failed';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  const logout = async () => {
    try {
      if (state.token) {
        await authApi.logout();
      }
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API error:', error);
    } finally {
      dispatch({ type: 'LOGOUT' });
      toast.success('Logged out successfully');
    }
  };

  const refreshAuth = async () => {
    if (!state.refreshToken) {
      dispatch({ type: 'LOGOUT' });
      return;
    }

    try {
      const response = await authApi.refreshToken({ refreshToken: state.refreshToken });
      
      if (response.success) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: state.user!,
            token: response.data.accessToken,
            refreshToken: state.refreshToken,
          },
        });
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      dispatch({ type: 'LOGOUT' });
      toast.error('Session expired. Please log in again.');
    }
  };

  const updateUser = async (userData: Partial<User>) => {
    try {
      const response = await authApi.updateProfile(userData);
      
      if (response.success) {
        dispatch({ type: 'UPDATE_USER', payload: response.data });
        toast.success('Profile updated successfully');
      } else {
        throw new Error(response.message || 'Update failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Update failed';
      toast.error(errorMessage);
      throw error;
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshAuth,
    updateUser,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
