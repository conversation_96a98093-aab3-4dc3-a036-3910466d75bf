apiVersion: v2
name: cloudforge-platform
description: CloudForge Platform - Enterprise Cloud Infrastructure Solution
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://cloudforge.com
sources:
  - https://github.com/cloudforge/platform
maintainers:
  - name: CloudForge Team
    email: <EMAIL>
    url: https://cloudforge.com
keywords:
  - cloudforge
  - platform
  - enterprise
  - microservices
  - kubernetes
  - cloud
annotations:
  category: Infrastructure
  licenses: Commercial
  images: |
    - name: api-gateway
      image: cloudforge/api-gateway:1.0.0
    - name: auth-service
      image: cloudforge/auth-service:1.0.0
    - name: user-service
      image: cloudforge/user-service:1.0.0
    - name: billing-service
      image: cloudforge/billing-service:1.0.0
    - name: notification-service
      image: cloudforge/notification-service:1.0.0
    - name: monitoring-service
      image: cloudforge/monitoring-service:1.0.0
    - name: admin-dashboard
      image: cloudforge/admin-dashboard:1.0.0

dependencies:
  - name: postgresql
    version: "12.x.x"
    repository: "https://charts.bitnami.com/bitnami"
    condition: postgresql.enabled
  - name: redis
    version: "17.x.x"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled
  - name: prometheus
    version: "23.x.x"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "6.x.x"
    repository: "https://grafana.github.io/helm-charts"
    condition: monitoring.grafana.enabled
