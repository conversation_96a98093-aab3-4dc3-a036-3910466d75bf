'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { systemApi } from '../services/api';

interface AppState {
  isLoading: boolean;
  systemHealth: any;
  metrics: any;
  theme: 'light' | 'dark';
  notifications: any[];
  error: string | null;
}

type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SYSTEM_HEALTH'; payload: any }
  | { type: 'SET_METRICS'; payload: any }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'ADD_NOTIFICATION'; payload: any }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'SET_ERROR'; payload: string | null };

const initialState: AppState = {
  isLoading: false,
  systemHealth: null,
  metrics: null,
  theme: 'light',
  notifications: [],
  error: null,
};

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_SYSTEM_HEALTH':
      return { ...state, systemHealth: action.payload };
    case 'SET_METRICS':
      return { ...state, metrics: action.payload };
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    case 'ADD_NOTIFICATION':
      return { ...state, notifications: [...state.notifications, action.payload] };
    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload),
      };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
}

interface AppContextType extends AppState {
  loadSystemHealth: () => Promise<void>;
  loadMetrics: () => Promise<void>;
  toggleTheme: () => void;
  addNotification: (notification: any) => void;
  removeNotification: (id: string) => void;
  clearError: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load theme from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('cloudforge_theme') as 'light' | 'dark';
    if (savedTheme) {
      dispatch({ type: 'SET_THEME', payload: savedTheme });
    }
  }, []);

  // Save theme to localStorage
  useEffect(() => {
    localStorage.setItem('cloudforge_theme', state.theme);
  }, [state.theme]);

  const loadSystemHealth = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await systemApi.getHealth();
      if (response.success) {
        dispatch({ type: 'SET_SYSTEM_HEALTH', payload: response.data });
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const loadMetrics = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const response = await systemApi.getMetrics();
      if (response.success) {
        dispatch({ type: 'SET_METRICS', payload: response.data });
      }
    } catch (error: any) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const toggleTheme = () => {
    const newTheme = state.theme === 'light' ? 'dark' : 'light';
    dispatch({ type: 'SET_THEME', payload: newTheme });
  };

  const addNotification = (notification: any) => {
    const notificationWithId = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
    };
    dispatch({ type: 'ADD_NOTIFICATION', payload: notificationWithId });

    // Auto-remove after 5 seconds
    setTimeout(() => {
      removeNotification(notificationWithId.id);
    }, 5000);
  };

  const removeNotification = (id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  };

  const clearError = () => {
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  const value: AppContextType = {
    ...state,
    loadSystemHealth,
    loadMetrics,
    toggleTheme,
    addNotification,
    removeNotification,
    clearError,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
