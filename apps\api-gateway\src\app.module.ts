/**
 * CloudForge Platform - API Gateway Module
 * Enterprise-grade cloud services platform
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { HealthModule } from './health/health.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { BillingModule } from './billing/billing.module';
import { NotificationsModule } from './notifications/notifications.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { AdminModule } from './admin/admin.module';
import { ProxyModule } from './proxy/proxy.module';
import { validationSchema } from './config/validation.schema';
import { configuration } from './config/configuration';

@Module({
  imports: [
    // Configuration module with validation
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      validationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
      expandVariables: true,
    }),

    // Rate limiting / Throttling
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>('RATE_LIMIT_WINDOW', 60),
        limit: configService.get<number>('RATE_LIMIT_MAX_REQUESTS', 100),
        storage: configService.get<string>('REDIS_HOST') 
          ? {
              type: 'redis',
              host: configService.get<string>('REDIS_HOST'),
              port: configService.get<number>('REDIS_PORT', 6379),
              password: configService.get<string>('REDIS_PASSWORD'),
              db: configService.get<number>('REDIS_DB', 0),
            }
          : undefined,
      }),
    }),

    // Prometheus metrics
    PrometheusModule.register({
      path: '/metrics',
      defaultMetrics: {
        enabled: true,
        config: {
          prefix: 'cloudforge_api_gateway_',
        },
      },
    }),

    // Core modules
    HealthModule,
    AuthModule,
    UsersModule,
    BillingModule,
    NotificationsModule,
    MonitoringModule,
    AdminModule,
    ProxyModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // Log configuration on startup
    const environment = this.configService.get<string>('NODE_ENV');
    const port = this.configService.get<number>('API_PORT');
    
    console.log(`🔧 Configuration loaded for environment: ${environment}`);
    console.log(`🚀 API Gateway will start on port: ${port}`);
  }
}
