/**
 * CloudForge Platform - Users Service (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  private readonly userServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.userServiceUrl = this.configService.get<string>('services.userService.url');
  }

  async findAll(query: any) {
    try {
      this.logger.log('Proxying get all users request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/users`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get all users request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async findOne(id: string) {
    try {
      this.logger.log(`Proxying get user request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/users/${id}`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get user request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async update(id: string, updateData: any) {
    try {
      this.logger.log(`Proxying update user request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.userServiceUrl}/users/${id}`, updateData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Update user request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async remove(id: string) {
    try {
      this.logger.log(`Proxying delete user request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.delete(`${this.userServiceUrl}/users/${id}`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Delete user request failed', error.response?.data || error.message);
      throw error;
    }
  }
}
