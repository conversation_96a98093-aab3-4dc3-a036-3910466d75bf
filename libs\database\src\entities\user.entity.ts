/**
 * CloudForge Platform - User Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToMany,
  JoinTable,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { BaseEntity } from '../base/base.entity';
import { Role } from './role.entity';
import { Subscription } from './subscription.entity';
import { Notification } from './notification.entity';
import { AuditLog } from './audit-log.entity';

@Entity('users')
@Index(['email'], { unique: true })
@Index(['username'], { unique: true })
@Index(['isActive'])
@Index(['isEmailVerified'])
@Index(['createdAt'])
export class User extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'User email address',
  })
  email: string;

  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: 'Username for login',
  })
  username: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Hashed password',
    select: false, // Don't include in queries by default
  })
  password: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'User first name',
  })
  firstName?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'User last name',
  })
  lastName?: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'User avatar URL',
  })
  avatar?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'User phone number',
  })
  phone?: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'User timezone',
  })
  timezone?: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'User preferred language',
  })
  language?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the user account is active',
  })
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether the user email is verified',
  })
  isEmailVerified: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether two-factor authentication is enabled',
  })
  isTwoFactorEnabled: boolean;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Two-factor authentication secret',
    select: false,
  })
  twoFactorSecret?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Last login timestamp',
  })
  lastLoginAt?: Date;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'Last login IP address',
  })
  lastLoginIp?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of failed login attempts',
  })
  failedLoginAttempts: number;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Account locked until this timestamp',
  })
  lockedUntil?: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Email verification token',
    select: false,
  })
  emailVerificationToken?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Email verification token expiry',
  })
  emailVerificationExpiry?: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Password reset token',
    select: false,
  })
  passwordResetToken?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Password reset token expiry',
  })
  passwordResetExpiry?: Date;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'User preferences and settings',
  })
  preferences?: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'OAuth provider information',
  })
  oauthProviders?: Record<string, any>;

  // Relationships
  @ManyToMany(() => Role, role => role.users, { eager: true })
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'userId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'roleId', referencedColumnName: 'id' },
  })
  roles: Role[];

  @OneToMany(() => Subscription, subscription => subscription.user)
  subscriptions: Subscription[];

  @OneToMany(() => Notification, notification => notification.user)
  notifications: Notification[];

  @OneToMany(() => AuditLog, auditLog => auditLog.user)
  auditLogs: AuditLog[];

  // Hooks
  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword(): Promise<void> {
    if (this.password && !this.password.startsWith('$2')) {
      // Only hash if it's not already hashed
      const saltRounds = 12;
      this.password = await bcrypt.hash(this.password, saltRounds);
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  normalizeEmail(): void {
    if (this.email) {
      this.email = this.email.toLowerCase().trim();
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  normalizeUsername(): void {
    if (this.username) {
      this.username = this.username.toLowerCase().trim();
    }
  }

  // Methods
  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }

  getFullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return this.firstName || this.lastName || this.username;
  }

  isLocked(): boolean {
    return this.lockedUntil && this.lockedUntil > new Date();
  }

  lockAccount(duration: number = 15 * 60 * 1000): void {
    this.lockedUntil = new Date(Date.now() + duration);
  }

  unlockAccount(): void {
    this.lockedUntil = null;
    this.failedLoginAttempts = 0;
  }

  incrementFailedLoginAttempts(): void {
    this.failedLoginAttempts += 1;
    
    // Lock account after 5 failed attempts
    if (this.failedLoginAttempts >= 5) {
      this.lockAccount();
    }
  }

  resetFailedLoginAttempts(): void {
    this.failedLoginAttempts = 0;
    this.lockedUntil = null;
  }

  updateLastLogin(ipAddress?: string): void {
    this.lastLoginAt = new Date();
    this.lastLoginIp = ipAddress;
    this.resetFailedLoginAttempts();
  }

  hasRole(roleName: string): boolean {
    return this.roles?.some(role => role.name === roleName) || false;
  }

  hasPermission(permissionName: string): boolean {
    if (!this.roles) return false;
    
    return this.roles.some(role =>
      role.permissions?.some(permission => permission.name === permissionName)
    );
  }

  getRoleNames(): string[] {
    return this.roles?.map(role => role.name) || [];
  }

  getPermissionNames(): string[] {
    if (!this.roles) return [];
    
    const permissions = new Set<string>();
    this.roles.forEach(role => {
      role.permissions?.forEach(permission => {
        permissions.add(permission.name);
      });
    });
    
    return Array.from(permissions);
  }

  updatePreference(key: string, value: any): void {
    if (!this.preferences) {
      this.preferences = {};
    }
    this.preferences[key] = value;
  }

  getPreference(key: string, defaultValue?: any): any {
    return this.preferences?.[key] ?? defaultValue;
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    
    // Remove sensitive fields
    delete obj.password;
    delete obj.twoFactorSecret;
    delete obj.emailVerificationToken;
    delete obj.passwordResetToken;
    
    return obj;
  }
}
