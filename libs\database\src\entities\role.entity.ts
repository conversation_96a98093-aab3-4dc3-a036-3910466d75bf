/**
 * CloudForge Platform - Role Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToMany,
  JoinTable,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { User } from './user.entity';
import { Permission } from './permission.entity';

@Entity('roles')
@Index(['name'], { unique: true })
@Index(['isSystem'])
@Index(['isActive'])
export class Role extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Role name (unique identifier)',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Role description',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Human-readable display name',
  })
  displayName?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a system role (cannot be deleted)',
  })
  isSystem: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this role is active',
  })
  isActive: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Role priority/hierarchy level (higher = more privileged)',
  })
  priority: number;

  @Column({
    type: 'varchar',
    length: 7,
    nullable: true,
    comment: 'Color code for UI display',
  })
  color?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Icon name for UI display',
  })
  icon?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional role configuration',
  })
  config?: Record<string, any>;

  // Relationships
  @ManyToMany(() => User, user => user.roles)
  users: User[];

  @ManyToMany(() => Permission, permission => permission.roles, { eager: true })
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'roleId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'permissionId', referencedColumnName: 'id' },
  })
  permissions: Permission[];

  // Hooks
  @BeforeInsert()
  @BeforeUpdate()
  normalizeName(): void {
    if (this.name) {
      this.name = this.name.toLowerCase().trim();
    }
  }

  @BeforeInsert()
  setDefaults(): void {
    if (!this.displayName) {
      this.displayName = this.capitalizeWords(this.name);
    }
  }

  // Methods
  hasPermission(permissionName: string): boolean {
    return this.permissions?.some(permission => permission.name === permissionName) || false;
  }

  getPermissionNames(): string[] {
    return this.permissions?.map(permission => permission.name) || [];
  }

  addPermission(permission: Permission): void {
    if (!this.permissions) {
      this.permissions = [];
    }
    
    if (!this.hasPermission(permission.name)) {
      this.permissions.push(permission);
    }
  }

  removePermission(permissionName: string): void {
    if (this.permissions) {
      this.permissions = this.permissions.filter(
        permission => permission.name !== permissionName
      );
    }
  }

  getUserCount(): number {
    return this.users?.length || 0;
  }

  getPermissionCount(): number {
    return this.permissions?.length || 0;
  }

  canBeDeleted(): boolean {
    return !this.isSystem && this.getUserCount() === 0;
  }

  private capitalizeWords(str: string): string {
    return str
      .split(/[-_\s]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  updateConfig(key: string, value: any): void {
    if (!this.config) {
      this.config = {};
    }
    this.config[key] = value;
  }

  getConfig(key: string, defaultValue?: any): any {
    return this.config?.[key] ?? defaultValue;
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    
    // Add computed fields
    obj.userCount = this.getUserCount();
    obj.permissionCount = this.getPermissionCount();
    obj.canBeDeleted = this.canBeDeleted();
    
    return obj;
  }
}
