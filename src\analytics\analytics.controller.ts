/**
 * CloudForge Platform - Analytics Controller
 * Provides real-time analytics data for the dashboard
 * Created by <PERSON><PERSON>
 */

import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalyticsService } from './analytics.service';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { User } from '@prisma/client';

@ApiTags('Analytics')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('metrics')
  @ApiOperation({ summary: 'Get real-time analytics metrics' })
  @ApiResponse({ status: 200, description: 'Analytics metrics retrieved successfully' })
  async getMetrics(@GetUser() user: User) {
    return this.analyticsService.getMetrics(user.organizationId);
  }

  @Get('users')
  @ApiOperation({ summary: 'Get user analytics data' })
  @ApiResponse({ status: 200, description: 'User analytics retrieved successfully' })
  async getUserAnalytics(@GetUser() user: User) {
    return this.analyticsService.getUserAnalytics(user.organizationId);
  }

  @Get('performance')
  @ApiOperation({ summary: 'Get performance analytics' })
  @ApiResponse({ status: 200, description: 'Performance analytics retrieved successfully' })
  async getPerformanceAnalytics(@GetUser() user: User) {
    return this.analyticsService.getPerformanceAnalytics(user.organizationId);
  }

  @Get('revenue')
  @ApiOperation({ summary: 'Get revenue analytics' })
  @ApiResponse({ status: 200, description: 'Revenue analytics retrieved successfully' })
  async getRevenueAnalytics(@GetUser() user: User) {
    return this.analyticsService.getRevenueAnalytics(user.organizationId);
  }

  @Get('usage')
  @ApiOperation({ summary: 'Get usage analytics' })
  @ApiResponse({ status: 200, description: 'Usage analytics retrieved successfully' })
  async getUsageAnalytics(
    @GetUser() user: User,
    @Query('days') days: string = '30'
  ) {
    return this.analyticsService.getUsageAnalytics(user.organizationId, parseInt(days));
  }

  @Get('trends')
  @ApiOperation({ summary: 'Get analytics trends' })
  @ApiResponse({ status: 200, description: 'Analytics trends retrieved successfully' })
  async getTrends(
    @GetUser() user: User,
    @Query('period') period: string = 'week'
  ) {
    return this.analyticsService.getTrends(user.organizationId, period);
  }

  @Get('insights')
  @ApiOperation({ summary: 'Get AI-powered analytics insights' })
  @ApiResponse({ status: 200, description: 'Analytics insights retrieved successfully' })
  async getInsights(@GetUser() user: User) {
    return this.analyticsService.getInsights(user.organizationId);
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Get complete dashboard data' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  async getDashboardData(@GetUser() user: User) {
    const [metrics, userAnalytics, performance, revenue, usage, trends, insights] = await Promise.all([
      this.analyticsService.getMetrics(user.organizationId),
      this.analyticsService.getUserAnalytics(user.organizationId),
      this.analyticsService.getPerformanceAnalytics(user.organizationId),
      this.analyticsService.getRevenueAnalytics(user.organizationId),
      this.analyticsService.getUsageAnalytics(user.organizationId, 30),
      this.analyticsService.getTrends(user.organizationId, 'week'),
      this.analyticsService.getInsights(user.organizationId)
    ]);

    return {
      metrics,
      userAnalytics,
      performance,
      revenue,
      usage,
      trends,
      insights,
      timestamp: new Date().toISOString()
    };
  }
}
