{"name": "cloudforge-platform", "version": "1.0.0", "description": "Enterprise-grade cloud services platform with microservices architecture", "private": true, "workspaces": ["apps/*", "libs/*"], "scripts": {"build": "nx build", "dev": "nx serve", "test": "nx test", "test:e2e": "nx e2e", "test:coverage": "nx test --coverage", "lint": "nx lint", "format": "prettier --write .", "format:check": "prettier --check .", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "k8s:deploy": "./scripts/k8s-deploy.sh", "terraform:init": "cd infra/terraform && terraform init", "terraform:plan": "cd infra/terraform && terraform plan", "terraform:apply": "cd infra/terraform && terraform apply", "security:scan": "./scripts/security-scan.sh", "load:test": "k6 run tests/load/api-load-test.js", "docs:serve": "docsify serve docs", "prepare": "husky install"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.8", "@nx/eslint-plugin": "^17.1.3", "@nx/jest": "^17.1.3", "@nx/js": "^17.1.3", "@nx/nest": "^17.1.3", "@nx/node": "^17.1.3", "@nx/react": "^17.1.3", "@nx/webpack": "^17.1.3", "@nx/workspace": "^17.1.3", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "cypress": "^13.5.1", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^2.15.1", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "lint-staged": "^15.1.0", "nx": "^17.1.3", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"@nestjs/common": "^10.2.8", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.2.8", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.2.8", "@nestjs/swagger": "^7.1.16", "@nestjs/typeorm": "^10.0.1", "@nestjs/throttler": "^5.0.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/schedule": "^4.0.0", "@nestjs/bull": "^10.0.1", "@prometheus-prom/client": "^15.0.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.11.3", "react": "^18.2.0", "react-dom": "^18.2.0", "redis": "^4.6.10", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "winston": "^3.11.0", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "bull": "^4.12.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "axios": "^1.6.2", "cache-manager": "^5.2.4", "cache-manager-redis-store": "^3.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/cloudforge/platform.git"}, "keywords": ["cloud", "platform", "microservices", "enterprise", "kubernetes", "<PERSON><PERSON><PERSON>", "typescript"], "author": "CloudForge Engineering Team", "license": "MIT"}