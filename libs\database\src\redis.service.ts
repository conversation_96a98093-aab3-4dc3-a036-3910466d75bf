/**
 * CloudForge Platform - Redis Service
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: Redis;
  private subscriber: Redis;
  private publisher: Redis;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit(): Promise<void> {
    try {
      const redisConfig = {
        host: this.configService.get<string>('redis.host'),
        port: this.configService.get<number>('redis.port'),
        password: this.configService.get<string>('redis.password'),
        db: this.configService.get<number>('redis.db'),
        keyPrefix: this.configService.get<string>('redis.keyPrefix'),
        retryDelayOnFailover: this.configService.get<number>('redis.retryDelayOnFailover'),
        maxRetriesPerRequest: this.configService.get<number>('redis.maxRetriesPerRequest'),
        lazyConnect: true,
        maxLoadingTimeout: 5000,
      };

      // Main client for general operations
      this.client = new Redis(redisConfig);
      
      // Separate clients for pub/sub
      this.subscriber = new Redis(redisConfig);
      this.publisher = new Redis(redisConfig);

      // Connect all clients
      await Promise.all([
        this.client.connect(),
        this.subscriber.connect(),
        this.publisher.connect(),
      ]);

      this.logger.log('Redis connections established successfully');
      
      // Set up error handlers
      this.setupErrorHandlers();
      
    } catch (error) {
      this.logger.error('Failed to connect to Redis', error);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      await Promise.all([
        this.client?.quit(),
        this.subscriber?.quit(),
        this.publisher?.quit(),
      ]);
      this.logger.log('Redis connections closed');
    } catch (error) {
      this.logger.error('Error closing Redis connections', error);
    }
  }

  private setupErrorHandlers(): void {
    this.client.on('error', (error) => {
      this.logger.error('Redis client error', error);
    });

    this.subscriber.on('error', (error) => {
      this.logger.error('Redis subscriber error', error);
    });

    this.publisher.on('error', (error) => {
      this.logger.error('Redis publisher error', error);
    });

    this.client.on('connect', () => {
      this.logger.log('Redis client connected');
    });

    this.client.on('ready', () => {
      this.logger.log('Redis client ready');
    });

    this.client.on('close', () => {
      this.logger.warn('Redis client connection closed');
    });
  }

  /**
   * Get the main Redis client
   */
  getClient(): Redis {
    return this.client;
  }

  /**
   * Get the subscriber client
   */
  getSubscriber(): Redis {
    return this.subscriber;
  }

  /**
   * Get the publisher client
   */
  getPublisher(): Redis {
    return this.publisher;
  }

  /**
   * Set a key-value pair with optional TTL
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await this.client.setex(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
    } catch (error) {
      this.logger.error(`Failed to set key ${key}`, error);
      throw error;
    }
  }

  /**
   * Get a value by key
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error(`Failed to get key ${key}`, error);
      throw error;
    }
  }

  /**
   * Delete a key
   */
  async del(key: string): Promise<number> {
    try {
      return await this.client.del(key);
    } catch (error) {
      this.logger.error(`Failed to delete key ${key}`, error);
      throw error;
    }
  }

  /**
   * Check if a key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to check existence of key ${key}`, error);
      throw error;
    }
  }

  /**
   * Set TTL for a key
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, seconds);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to set TTL for key ${key}`, error);
      throw error;
    }
  }

  /**
   * Get TTL for a key
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);
    } catch (error) {
      this.logger.error(`Failed to get TTL for key ${key}`, error);
      throw error;
    }
  }

  /**
   * Increment a numeric value
   */
  async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key);
    } catch (error) {
      this.logger.error(`Failed to increment key ${key}`, error);
      throw error;
    }
  }

  /**
   * Increment by a specific amount
   */
  async incrby(key: string, increment: number): Promise<number> {
    try {
      return await this.client.incrby(key, increment);
    } catch (error) {
      this.logger.error(`Failed to increment key ${key} by ${increment}`, error);
      throw error;
    }
  }

  /**
   * Add to a set
   */
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sadd(key, ...members);
    } catch (error) {
      this.logger.error(`Failed to add to set ${key}`, error);
      throw error;
    }
  }

  /**
   * Get all members of a set
   */
  async smembers(key: string): Promise<string[]> {
    try {
      return await this.client.smembers(key);
    } catch (error) {
      this.logger.error(`Failed to get members of set ${key}`, error);
      throw error;
    }
  }

  /**
   * Publish a message to a channel
   */
  async publish(channel: string, message: any): Promise<number> {
    try {
      const serializedMessage = JSON.stringify(message);
      return await this.publisher.publish(channel, serializedMessage);
    } catch (error) {
      this.logger.error(`Failed to publish to channel ${channel}`, error);
      throw error;
    }
  }

  /**
   * Subscribe to a channel
   */
  async subscribe(channel: string, callback: (message: any) => void): Promise<void> {
    try {
      await this.subscriber.subscribe(channel);
      this.subscriber.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          try {
            const parsedMessage = JSON.parse(message);
            callback(parsedMessage);
          } catch (error) {
            this.logger.error('Failed to parse subscription message', error);
          }
        }
      });
    } catch (error) {
      this.logger.error(`Failed to subscribe to channel ${channel}`, error);
      throw error;
    }
  }

  /**
   * Get Redis health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: any;
  }> {
    try {
      const info = await this.client.info();
      const ping = await this.client.ping();
      
      return {
        status: ping === 'PONG' ? 'healthy' : 'unhealthy',
        details: {
          connected: this.client.status === 'ready',
          ping: ping === 'PONG',
          info: info.split('\r\n').slice(0, 5), // First few lines of info
        },
      };
    } catch (error) {
      this.logger.error('Redis health check failed', error);
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          error: error.message,
        },
      };
    }
  }

  /**
   * Flush all data (use with caution!)
   */
  async flushall(): Promise<void> {
    try {
      await this.client.flushall();
      this.logger.warn('Redis database flushed');
    } catch (error) {
      this.logger.error('Failed to flush Redis database', error);
      throw error;
    }
  }
}
