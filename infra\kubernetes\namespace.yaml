# CloudForge Platform - Kubernetes Namespace
# Enterprise-grade cloud services platform

apiVersion: v1
kind: Namespace
metadata:
  name: cloudforge-platform
  labels:
    name: cloudforge-platform
    environment: production
    app.kubernetes.io/name: cloudforge-platform
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: cloudforge-platform
    app.kubernetes.io/managed-by: helm
  annotations:
    description: "CloudForge Platform - Enterprise-grade cloud services platform"
    contact: "<EMAIL>"
    documentation: "https://docs.cloudforge.com"
    
---
# Resource Quota for the namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: cloudforge-platform-quota
  namespace: cloudforge-platform
  labels:
    app.kubernetes.io/name: cloudforge-platform
    app.kubernetes.io/component: resource-quota
spec:
  hard:
    # Compute resources
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    
    # Storage resources
    requests.storage: 100Gi
    persistentvolumeclaims: "10"
    
    # Object counts
    pods: "50"
    services: "20"
    secrets: "20"
    configmaps: "20"
    replicationcontrollers: "10"
    deployments.apps: "20"
    statefulsets.apps: "5"
    jobs.batch: "10"
    cronjobs.batch: "5"
    
---
# Limit Range for the namespace
apiVersion: v1
kind: LimitRange
metadata:
  name: cloudforge-platform-limits
  namespace: cloudforge-platform
  labels:
    app.kubernetes.io/name: cloudforge-platform
    app.kubernetes.io/component: limit-range
spec:
  limits:
    # Default limits for containers
    - default:
        cpu: "1"
        memory: 1Gi
      defaultRequest:
        cpu: 100m
        memory: 128Mi
      type: Container
      
    # Limits for pods
    - max:
        cpu: "4"
        memory: 8Gi
      min:
        cpu: 50m
        memory: 64Mi
      type: Pod
      
    # Limits for persistent volume claims
    - max:
        storage: 50Gi
      min:
        storage: 1Gi
      type: PersistentVolumeClaim

---
# Network Policy for the namespace
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cloudforge-platform-network-policy
  namespace: cloudforge-platform
  labels:
    app.kubernetes.io/name: cloudforge-platform
    app.kubernetes.io/component: network-policy
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow ingress from ingress controller
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
    # Allow ingress from monitoring namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
    # Allow internal communication within namespace
    - from:
        - namespaceSelector:
            matchLabels:
              name: cloudforge-platform
  egress:
    # Allow egress to DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow egress to external services (HTTPS)
    - to: []
      ports:
        - protocol: TCP
          port: 443
    # Allow egress to external services (HTTP)
    - to: []
      ports:
        - protocol: TCP
          port: 80
    # Allow internal communication within namespace
    - to:
        - namespaceSelector:
            matchLabels:
              name: cloudforge-platform
