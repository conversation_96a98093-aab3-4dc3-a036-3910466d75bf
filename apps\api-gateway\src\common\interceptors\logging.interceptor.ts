/**
 * CloudForge Platform - Logging Interceptor
 * Enterprise-grade cloud services platform
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const contentLength = headers['content-length'] || '0';
    
    // Generate request ID for tracing
    const requestId = this.generateRequestId();
    request['requestId'] = requestId;
    response.setHeader('X-Request-ID', requestId);

    // Log incoming request
    this.logger.log(
      `Incoming Request: ${method} ${url}`,
      JSON.stringify({
        requestId,
        method,
        url,
        ip,
        userAgent,
        contentLength,
        timestamp: new Date().toISOString(),
      })
    );

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - startTime;
        const { statusCode } = response;
        const responseSize = JSON.stringify(data || {}).length;

        // Log successful response
        this.logger.log(
          `Outgoing Response: ${method} ${url} ${statusCode} ${duration}ms`,
          JSON.stringify({
            requestId,
            method,
            url,
            statusCode,
            duration,
            responseSize,
            timestamp: new Date().toISOString(),
          })
        );
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const statusCode = error.status || 500;

        // Log error response
        this.logger.error(
          `Error Response: ${method} ${url} ${statusCode} ${duration}ms`,
          JSON.stringify({
            requestId,
            method,
            url,
            statusCode,
            duration,
            error: error.message,
            timestamp: new Date().toISOString(),
          }),
          error.stack
        );

        throw error;
      })
    );
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
