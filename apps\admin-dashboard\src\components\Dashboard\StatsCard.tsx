/**
 * CloudForge Platform - Stats Card Component
 * Enterprise-grade cloud services platform
 */

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  SvgIcon,
  useTheme,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';

interface StatsCardProps {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
  icon: typeof SvgIcon;
  color: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  trend,
  icon: Icon,
  color,
}) => {
  const theme = useTheme();

  const isPositive = trend === 'up';
  const changeColor = isPositive ? theme.palette.success.main : theme.palette.error.main;
  const TrendIcon = isPositive ? TrendingUp : TrendingDown;

  return (
    <Card
      sx={{
        height: '100%',
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8],
        },
      }}
    >
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: 2,
              backgroundColor: `${color}15`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Icon sx={{ color, fontSize: 24 }} />
          </Box>
          <Chip
            icon={<TrendIcon sx={{ fontSize: 16 }} />}
            label={change}
            size="small"
            sx={{
              backgroundColor: `${changeColor}15`,
              color: changeColor,
              fontWeight: 600,
              '& .MuiChip-icon': {
                color: changeColor,
              },
            }}
          />
        </Box>

        <Typography variant="h4" component="div" fontWeight={700} mb={0.5}>
          {value}
        </Typography>

        <Typography variant="body2" color="text.secondary">
          {title}
        </Typography>
      </CardContent>
    </Card>
  );
};
