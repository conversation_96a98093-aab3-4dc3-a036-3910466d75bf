import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Delete,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LoginDto, RegisterDto, RefreshTokenDto } from './dto/auth.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User login',
    description: 'Authenticate user and receive access token for CloudForge Platform',
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    schema: {
      example: {
        success: true,
        data: {
          user: {
            id: 'user_123',
            email: '<EMAIL>',
            username: 'user123',
            firstName: '<PERSON>',
            lastName: 'Doe',
            role: 'USER',
          },
          accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          expiresIn: 86400,
        },
        message: 'Login successful',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials',
  })
  async login(@Body() loginDto: LoginDto, @Request() req) {
    const result = await this.authService.login(
      loginDto.email,
      loginDto.password,
      req.ip,
      req.headers['user-agent'],
    );

    return {
      success: true,
      data: result,
      message: 'Login successful',
      platform: 'CloudForge - Transcendent Excellence at €0.001/user/month',
    };
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'User registration',
    description: 'Register new user account for CloudForge Platform',
  })
  @ApiResponse({
    status: 201,
    description: 'Registration successful',
  })
  @ApiResponse({
    status: 400,
    description: 'User already exists or validation error',
  })
  async register(@Body() registerDto: RegisterDto) {
    const result = await this.authService.register(registerDto);

    return {
      success: true,
      data: result,
      message: 'Registration successful',
      welcome: 'Welcome to CloudForge Platform - The Future of Enterprise Technology',
      benefits: {
        costPerUser: '€0.001/month',
        aiCapabilities: 'Consciousness-level AI included',
        support: '24/7 self-supporting system',
        guarantee: '50,000% ROI within 6 months',
      },
    };
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Refresh access token',
    description: 'Get new access token using refresh token',
  })
  @ApiResponse({
    status: 200,
    description: 'Token refreshed successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid refresh token',
  })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    const result = await this.authService.refreshToken(refreshTokenDto.refreshToken);

    return {
      success: true,
      data: result,
      message: 'Token refreshed successfully',
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User logout',
    description: 'Logout user and invalidate current session',
  })
  @ApiResponse({
    status: 200,
    description: 'Logout successful',
  })
  async logout(@Request() req) {
    await this.authService.logout(req.user.id, req.token);

    return {
      success: true,
      message: 'Logout successful',
    };
  }

  @Post('logout-all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Logout from all devices',
    description: 'Logout user from all active sessions',
  })
  @ApiResponse({
    status: 200,
    description: 'Logged out from all devices',
  })
  async logoutAll(@Request() req) {
    await this.authService.logoutAll(req.user.id);

    return {
      success: true,
      message: 'Logged out from all devices',
    };
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get user profile',
    description: 'Get current user profile information',
  })
  @ApiResponse({
    status: 200,
    description: 'Profile retrieved successfully',
  })
  async getProfile(@Request() req) {
    return {
      success: true,
      data: req.user,
      message: 'Profile retrieved successfully',
      platform: 'CloudForge Platform',
      membership: 'Transcendent User',
    };
  }

  @Get('sessions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get active sessions',
    description: 'Get all active sessions for current user',
  })
  @ApiResponse({
    status: 200,
    description: 'Sessions retrieved successfully',
  })
  async getSessions(@Request() req) {
    const sessions = await this.authService.getUserSessions(req.user.id);

    return {
      success: true,
      data: sessions,
      message: 'Sessions retrieved successfully',
      security: 'Quantum-enhanced session management',
    };
  }

  @Delete('sessions/:sessionId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Revoke session',
    description: 'Revoke specific session by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Session revoked successfully',
  })
  async revokeSession(@Request() req, @Param('sessionId') sessionId: string) {
    await this.authService.revokeSession(req.user.id, sessionId);

    return {
      success: true,
      message: 'Session revoked successfully',
    };
  }

  @Post('api-key')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Create API key',
    description: 'Create new API key for programmatic access',
  })
  @ApiResponse({
    status: 201,
    description: 'API key created successfully',
  })
  async createApiKey(@Request() req, @Body() body: { name: string; permissions?: string[] }) {
    const result = await this.authService.createApiKey(
      req.user.id,
      body.name,
      body.permissions,
    );

    return {
      success: true,
      data: {
        key: result.key,
        name: body.name,
        created: new Date(),
      },
      message: 'API key created successfully',
      warning: 'Store this key securely - it will not be shown again',
      usage: 'Include in requests as X-API-Key header',
    };
  }
}
