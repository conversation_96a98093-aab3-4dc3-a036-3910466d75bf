# CloudForge Platform - Admin Dashboard Dockerfile
# Enterprise-grade cloud services platform

# Multi-stage build for optimized production image

# Stage 1: Base image with Node.js
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Stage 2: Dependencies
FROM base AS dependencies

# Install all dependencies
RUN npm ci --silent && npm cache clean --force

# Stage 3: Development
FROM base AS development

# Install all dependencies
RUN npm ci --silent

# Copy source code
COPY . .

# Expose port
EXPOSE 3010

# Start development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3010"]

# Stage 4: Build
FROM dependencies AS build

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Stage 5: Production
FROM nginx:alpine AS production

# Copy built application
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Change ownership
RUN chown -R nextjs:nodejs /usr/share/nginx/html
RUN chown -R nextjs:nodejs /var/cache/nginx
RUN chown -R nextjs:nodejs /var/log/nginx
RUN chown -R nextjs:nodejs /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R nextjs:nodejs /var/run/nginx.pid

USER nextjs

# Expose port
EXPOSE 3010

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3010 || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
