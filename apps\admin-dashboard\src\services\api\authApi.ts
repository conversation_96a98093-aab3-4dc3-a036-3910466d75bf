/**
 * CloudForge Platform - Auth API Service
 * Enterprise-grade cloud services platform
 */

import api, { ApiResponse } from './apiClient';
import { User } from '../../store/slices/authSlice';

// Auth API types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// Auth API service
export const authApi = {
  /**
   * Login user
   */
  login: (credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
    return api.post('/auth/login', credentials);
  },

  /**
   * Logout user
   */
  logout: (): Promise<ApiResponse<void>> => {
    return api.post('/auth/logout');
  },

  /**
   * Refresh access token
   */
  refreshToken: (data: RefreshTokenRequest): Promise<ApiResponse<RefreshTokenResponse>> => {
    return api.post('/auth/refresh', data);
  },

  /**
   * Get current user profile
   */
  getCurrentUser: (): Promise<ApiResponse<User>> => {
    return api.get('/auth/me');
  },

  /**
   * Update user profile
   */
  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> => {
    return api.put('/auth/me', data);
  },

  /**
   * Change password
   */
  changePassword: (data: ChangePasswordRequest): Promise<ApiResponse<void>> => {
    return api.post('/auth/change-password', data);
  },

  /**
   * Request password reset
   */
  forgotPassword: (data: ForgotPasswordRequest): Promise<ApiResponse<void>> => {
    return api.post('/auth/forgot-password', data);
  },

  /**
   * Reset password with token
   */
  resetPassword: (data: ResetPasswordRequest): Promise<ApiResponse<void>> => {
    return api.post('/auth/reset-password', data);
  },

  /**
   * Verify email address
   */
  verifyEmail: (token: string): Promise<ApiResponse<void>> => {
    return api.post('/auth/verify-email', { token });
  },

  /**
   * Resend email verification
   */
  resendEmailVerification: (): Promise<ApiResponse<void>> => {
    return api.post('/auth/resend-verification');
  },

  /**
   * Enable two-factor authentication
   */
  enableTwoFactor: (): Promise<ApiResponse<{ qrCode: string; secret: string }>> => {
    return api.post('/auth/2fa/enable');
  },

  /**
   * Verify two-factor authentication setup
   */
  verifyTwoFactor: (code: string): Promise<ApiResponse<{ backupCodes: string[] }>> => {
    return api.post('/auth/2fa/verify', { code });
  },

  /**
   * Disable two-factor authentication
   */
  disableTwoFactor: (password: string): Promise<ApiResponse<void>> => {
    return api.post('/auth/2fa/disable', { password });
  },

  /**
   * Verify two-factor authentication code during login
   */
  verifyTwoFactorLogin: (code: string, token: string): Promise<ApiResponse<LoginResponse>> => {
    return api.post('/auth/2fa/login', { code, token });
  },

  /**
   * Get user sessions
   */
  getSessions: (): Promise<ApiResponse<Array<{
    id: string;
    ipAddress: string;
    userAgent: string;
    location?: string;
    lastActivity: string;
    isCurrent: boolean;
  }>>> => {
    return api.get('/auth/sessions');
  },

  /**
   * Revoke user session
   */
  revokeSession: (sessionId: string): Promise<ApiResponse<void>> => {
    return api.delete(`/auth/sessions/${sessionId}`);
  },

  /**
   * Revoke all other sessions
   */
  revokeAllOtherSessions: (): Promise<ApiResponse<void>> => {
    return api.post('/auth/sessions/revoke-all');
  },
};
