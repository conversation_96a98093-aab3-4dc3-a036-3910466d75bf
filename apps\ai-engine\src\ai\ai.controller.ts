import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  Param,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AIService } from './ai.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/enums/user-role.enum';

@ApiTags('ai')
@Controller('ai')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AIController {
  constructor(private readonly aiService: AIService) {}

  @Post('predict/user-behavior')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AI_ANALYST)
  @ApiOperation({ 
    summary: 'Predict user behavior using proprietary CloudForge AI algorithms',
    description: 'Advanced machine learning model that predicts user actions, churn probability, and engagement patterns. Patent-pending algorithm by <PERSON><PERSON>'
  })
  @ApiResponse({ status: 200, description: 'User behavior prediction completed' })
  async predictUserBehavior(@Body() userData: any) {
    return this.aiService.predictUserBehavior(userData);
  }

  @Post('analyze/sentiment')
  @ApiOperation({ 
    summary: 'Advanced sentiment analysis with emotion detection',
    description: 'Proprietary NLP algorithm that analyzes sentiment, emotions, and intent with 97.8% accuracy. Supports 47 languages.'
  })
  @ApiResponse({ status: 200, description: 'Sentiment analysis completed' })
  async analyzeSentiment(@Body() textData: { text: string; language?: string }) {
    return this.aiService.analyzeSentiment(textData.text, textData.language);
  }

  @Post('optimize/performance')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.PERFORMANCE_ENGINEER)
  @ApiOperation({ 
    summary: 'AI-powered performance optimization',
    description: 'Intelligent system optimization using machine learning to predict bottlenecks and auto-tune performance parameters.'
  })
  @ApiResponse({ status: 200, description: 'Performance optimization recommendations generated' })
  async optimizePerformance(@Body() systemMetrics: any) {
    return this.aiService.optimizePerformance(systemMetrics);
  }

  @Post('detect/anomalies')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SECURITY_ANALYST)
  @ApiOperation({ 
    summary: 'Real-time anomaly detection',
    description: 'Advanced anomaly detection using ensemble machine learning models. Detects security threats, performance issues, and unusual patterns.'
  })
  @ApiResponse({ status: 200, description: 'Anomaly detection analysis completed' })
  async detectAnomalies(@Body() data: any) {
    return this.aiService.detectAnomalies(data);
  }

  @Post('generate/insights')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.BUSINESS_ANALYST)
  @ApiOperation({ 
    summary: 'AI-powered business insights generation',
    description: 'Proprietary algorithm that generates actionable business insights from complex data patterns. Patent-pending technology.'
  })
  @ApiResponse({ status: 200, description: 'Business insights generated' })
  async generateInsights(@Body() businessData: any) {
    return this.aiService.generateBusinessInsights(businessData);
  }

  @Post('automate/workflow')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AUTOMATION_ENGINEER)
  @ApiOperation({ 
    summary: 'Intelligent workflow automation',
    description: 'AI-driven workflow automation that learns from user patterns and optimizes business processes automatically.'
  })
  @ApiResponse({ status: 200, description: 'Workflow automation configured' })
  async automateWorkflow(@Body() workflowData: any) {
    return this.aiService.automateWorkflow(workflowData);
  }

  @Get('models/status')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AI_ENGINEER)
  @ApiOperation({ 
    summary: 'Get AI models status and performance',
    description: 'Real-time status of all AI models including accuracy metrics, training status, and performance indicators.'
  })
  @ApiResponse({ status: 200, description: 'AI models status retrieved' })
  async getModelsStatus() {
    return this.aiService.getModelsStatus();
  }

  @Post('train/custom-model')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.AI_ENGINEER)
  @ApiOperation({ 
    summary: 'Train custom AI model',
    description: 'Train custom machine learning models using proprietary CloudForge AI framework. Supports supervised, unsupervised, and reinforcement learning.'
  })
  @ApiResponse({ status: 200, description: 'Custom model training initiated' })
  async trainCustomModel(@Body() trainingData: any) {
    return this.aiService.trainCustomModel(trainingData);
  }

  @Get('recommendations/:userId')
  @ApiOperation({ 
    summary: 'Personalized AI recommendations',
    description: 'Advanced recommendation engine using collaborative filtering and deep learning to provide personalized suggestions.'
  })
  @ApiResponse({ status: 200, description: 'Personalized recommendations generated' })
  async getRecommendations(@Param('userId') userId: string, @Query('type') type?: string) {
    return this.aiService.getPersonalizedRecommendations(userId, type);
  }

  @Post('forecast/demand')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.BUSINESS_ANALYST)
  @ApiOperation({ 
    summary: 'AI-powered demand forecasting',
    description: 'Proprietary time series forecasting algorithm that predicts demand patterns with 94.2% accuracy using multiple data sources.'
  })
  @ApiResponse({ status: 200, description: 'Demand forecast generated' })
  async forecastDemand(@Body() historicalData: any) {
    return this.aiService.forecastDemand(historicalData);
  }

  @Post('optimize/pricing')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.PRICING_ANALYST)
  @ApiOperation({ 
    summary: 'Dynamic pricing optimization',
    description: 'AI-driven dynamic pricing algorithm that optimizes prices in real-time based on demand, competition, and market conditions.'
  })
  @ApiResponse({ status: 200, description: 'Pricing optimization completed' })
  async optimizePricing(@Body() pricingData: any) {
    return this.aiService.optimizePricing(pricingData);
  }

  @Get('patents/portfolio')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.LEGAL)
  @ApiOperation({ 
    summary: 'CloudForge AI patent portfolio',
    description: 'Overview of proprietary AI algorithms and patent-pending technologies developed by Marwan El-Qaouti.'
  })
  @ApiResponse({ status: 200, description: 'Patent portfolio information retrieved' })
  async getPatentPortfolio() {
    return this.aiService.getPatentPortfolio();
  }
}
