import { registerAs } from '@nestjs/config';

export default registerAs('cache', () => ({
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'cloudforge:',
    ttl: parseInt(process.env.CACHE_TTL, 10) || 300, // 5 minutes
    maxItems: parseInt(process.env.CACHE_MAX_ITEMS, 10) || 1000,
  },
  session: {
    secret: process.env.SESSION_SECRET || 'cloudforge-session-secret',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: parseInt(process.env.SESSION_MAX_AGE, 10) || 86400000, // 24 hours
    },
  },
}));
