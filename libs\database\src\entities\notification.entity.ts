/**
 * CloudForge Platform - Notification Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { User } from './user.entity';

export enum NotificationType {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  WEBHOOK = 'webhook',
  IN_APP = 'in_app',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  FAILED = 'failed',
  DELIVERED = 'delivered',
  READ = 'read',
}

@Entity('notifications')
@Index(['userId'])
@Index(['type'])
@Index(['status'])
@Index(['scheduledAt'])
@Index(['sentAt'])
@Index(['isRead'])
export class Notification extends BaseEntity {
  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who will receive this notification',
  })
  userId?: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
    comment: 'Type of notification',
  })
  type: NotificationType;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Notification channel (email address, phone number, etc.)',
  })
  channel: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'Notification subject',
  })
  subject?: string;

  @Column({
    type: 'text',
    comment: 'Notification content',
  })
  content: string;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
    comment: 'Current notification status',
  })
  status: NotificationStatus;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When to send this notification',
  })
  scheduledAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this notification was sent',
  })
  sentAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this notification was delivered',
  })
  deliveredAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this notification was read',
  })
  readAt?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this notification has been read',
  })
  isRead: boolean;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Template used for this notification',
  })
  template?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Template variables used',
  })
  templateData?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Notification category',
  })
  category?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Priority level',
  })
  priority?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of retry attempts',
  })
  retryCount: number;

  @Column({
    type: 'int',
    default: 3,
    comment: 'Maximum retry attempts',
  })
  maxRetries: number;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Next retry attempt time',
  })
  nextRetryAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Error message if failed',
  })
  errorMessage?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'External notification ID (e.g., from email service)',
  })
  externalId?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional notification metadata',
  })
  notificationMetadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User, user => user.notifications)
  @JoinColumn({ name: 'userId' })
  user?: User;

  // Methods
  isPending(): boolean {
    return this.status === NotificationStatus.PENDING;
  }

  isSent(): boolean {
    return this.status === NotificationStatus.SENT;
  }

  isFailed(): boolean {
    return this.status === NotificationStatus.FAILED;
  }

  isDelivered(): boolean {
    return this.status === NotificationStatus.DELIVERED;
  }

  markAsSent(externalId?: string): void {
    this.status = NotificationStatus.SENT;
    this.sentAt = new Date();
    this.externalId = externalId;
  }

  markAsDelivered(): void {
    this.status = NotificationStatus.DELIVERED;
    this.deliveredAt = new Date();
  }

  markAsFailed(errorMessage: string): void {
    this.status = NotificationStatus.FAILED;
    this.errorMessage = errorMessage;
    this.retryCount += 1;
    
    if (this.retryCount < this.maxRetries) {
      // Schedule next retry with exponential backoff
      const backoffMinutes = Math.pow(2, this.retryCount) * 5; // 5, 10, 20 minutes
      this.nextRetryAt = new Date(Date.now() + backoffMinutes * 60 * 1000);
    }
  }

  markAsRead(): void {
    this.isRead = true;
    this.readAt = new Date();
    this.status = NotificationStatus.READ;
  }

  canRetry(): boolean {
    return this.isFailed() && 
           this.retryCount < this.maxRetries && 
           (!this.nextRetryAt || this.nextRetryAt <= new Date());
  }

  isScheduled(): boolean {
    return this.scheduledAt && this.scheduledAt > new Date();
  }

  shouldSend(): boolean {
    return this.isPending() && 
           (!this.scheduledAt || this.scheduledAt <= new Date());
  }

  getTimeSinceSent(): number | null {
    if (!this.sentAt) return null;
    return Date.now() - this.sentAt.getTime();
  }

  getTimeSinceRead(): number | null {
    if (!this.readAt) return null;
    return Date.now() - this.readAt.getTime();
  }

  updateMetadata(key: string, value: any): void {
    if (!this.notificationMetadata) {
      this.notificationMetadata = {};
    }
    this.notificationMetadata[key] = value;
  }

  getMetadata(key: string, defaultValue?: any): any {
    return this.notificationMetadata?.[key] ?? defaultValue;
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    
    // Add computed fields
    obj.isPending = this.isPending();
    obj.isSent = this.isSent();
    obj.isFailed = this.isFailed();
    obj.isDelivered = this.isDelivered();
    obj.canRetry = this.canRetry();
    obj.isScheduled = this.isScheduled();
    obj.shouldSend = this.shouldSend();
    obj.timeSinceSent = this.getTimeSinceSent();
    obj.timeSinceRead = this.getTimeSinceRead();
    
    return obj;
  }
}
