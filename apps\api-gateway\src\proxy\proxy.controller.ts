/**
 * CloudForge Platform - Proxy Controller (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Controller, All, Req, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ProxyService } from './proxy.service';

@ApiTags('Proxy')
@Controller('proxy')
export class ProxyController {
  constructor(private readonly proxyService: ProxyService) {}

  @All('*')
  @ApiOperation({ summary: 'Proxy requests to microservices' })
  @ApiResponse({ status: 200, description: 'Request proxied successfully' })
  @ApiResponse({ status: 404, description: 'Service not found' })
  @ApiResponse({ status: 503, description: 'Service unavailable' })
  async proxyRequest(@Req() req: Request, @Res() res: Response) {
    return this.proxyService.proxyRequest(req, res);
  }
}
