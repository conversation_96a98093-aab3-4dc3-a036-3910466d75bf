# CloudForge Platform - Helm Chart
# Enterprise-grade cloud services platform

apiVersion: v2
name: cloudforge-platform
description: A Helm chart for CloudForge Platform - Enterprise-grade cloud services platform
type: application
version: 1.0.0
appVersion: "1.0.0"

keywords:
  - cloudforge
  - platform
  - microservices
  - enterprise
  - cloud
  - saas

home: https://cloudforge.com
sources:
  - https://github.com/cloudforge/platform

maintainers:
  - name: CloudForge Platform Team
    email: <EMAIL>
    url: https://cloudforge.com

annotations:
  category: Platform
  licenses: MIT
  images: |
    - name: api-gateway
      image: cloudforge/api-gateway:1.0.0
    - name: auth-service
      image: cloudforge/auth-service:1.0.0
    - name: user-service
      image: cloudforge/user-service:1.0.0
    - name: billing-service
      image: cloudforge/billing-service:1.0.0
    - name: notification-service
      image: cloudforge/notification-service:1.0.0
    - name: monitoring-service
      image: cloudforge/monitoring-service:1.0.0
    - name: admin-dashboard
      image: cloudforge/admin-dashboard:1.0.0

dependencies:
  - name: postgresql
    version: "12.1.2"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
    
  - name: redis
    version: "17.3.7"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
    
  - name: prometheus
    version: "15.18.0"
    repository: https://prometheus-community.github.io/helm-charts
    condition: prometheus.enabled
    
  - name: grafana
    version: "6.50.7"
    repository: https://grafana.github.io/helm-charts
    condition: grafana.enabled
