# CloudForge Platform - Development Makefile
# Enterprise-grade cloud services platform

.PHONY: help install dev build test clean docker k8s terraform docs security

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
RESET := \033[0m

## Help
help: ## Show this help message
	@echo "$(BLUE)CloudForge Platform - Development Commands$(RESET)"
	@echo ""
	@echo "$(GREEN)Available commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

## Development
install: ## Install all dependencies
	@echo "$(BLUE)Installing dependencies...$(RESET)"
	npm install
	@echo "$(GREEN)Dependencies installed successfully!$(RESET)"

dev: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(RESET)"
	docker-compose up -d postgres redis
	npm run dev

build: ## Build all applications
	@echo "$(BLUE)Building applications...$(RESET)"
	npm run build
	@echo "$(GREEN)Build completed successfully!$(RESET)"

clean: ## Clean build artifacts and dependencies
	@echo "$(BLUE)Cleaning build artifacts...$(RESET)"
	rm -rf dist/ node_modules/ coverage/ .nx/cache/
	@echo "$(GREEN)Clean completed!$(RESET)"

## Testing
test: ## Run all tests
	@echo "$(BLUE)Running tests...$(RESET)"
	npm test

test-coverage: ## Run tests with coverage report
	@echo "$(BLUE)Running tests with coverage...$(RESET)"
	npm run test:coverage
	@echo "$(GREEN)Coverage report generated in coverage/$(RESET)"

test-e2e: ## Run end-to-end tests
	@echo "$(BLUE)Running E2E tests...$(RESET)"
	npm run test:e2e

test-load: ## Run load tests
	@echo "$(BLUE)Running load tests...$(RESET)"
	npm run test:load

## Docker
docker-build: ## Build Docker images
	@echo "$(BLUE)Building Docker images...$(RESET)"
	docker-compose build
	@echo "$(GREEN)Docker images built successfully!$(RESET)"

docker-up: ## Start all services with Docker Compose
	@echo "$(BLUE)Starting services with Docker Compose...$(RESET)"
	docker-compose up -d
	@echo "$(GREEN)Services started successfully!$(RESET)"

docker-down: ## Stop all Docker services
	@echo "$(BLUE)Stopping Docker services...$(RESET)"
	docker-compose down
	@echo "$(GREEN)Services stopped!$(RESET)"

docker-logs: ## View Docker logs
	docker-compose logs -f

## Kubernetes
k8s-deploy: ## Deploy to Kubernetes
	@echo "$(BLUE)Deploying to Kubernetes...$(RESET)"
	./scripts/k8s-deploy.sh
	@echo "$(GREEN)Kubernetes deployment completed!$(RESET)"

k8s-status: ## Check Kubernetes deployment status
	kubectl get pods,services,ingress -n cloudforge

k8s-logs: ## View Kubernetes logs
	kubectl logs -f -l app=cloudforge -n cloudforge

## Terraform
terraform-init: ## Initialize Terraform
	@echo "$(BLUE)Initializing Terraform...$(RESET)"
	cd infra/terraform && terraform init

terraform-plan: ## Plan Terraform deployment
	@echo "$(BLUE)Planning Terraform deployment...$(RESET)"
	cd infra/terraform && terraform plan

terraform-apply: ## Apply Terraform configuration
	@echo "$(BLUE)Applying Terraform configuration...$(RESET)"
	cd infra/terraform && terraform apply

terraform-destroy: ## Destroy Terraform infrastructure
	@echo "$(RED)Destroying Terraform infrastructure...$(RESET)"
	cd infra/terraform && terraform destroy

## Security
security-scan: ## Run security scans
	@echo "$(BLUE)Running security scans...$(RESET)"
	./scripts/security-scan.sh
	@echo "$(GREEN)Security scan completed!$(RESET)"

security-audit: ## Run npm audit
	@echo "$(BLUE)Running npm security audit...$(RESET)"
	npm audit

## Database
db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(RESET)"
	npm run db:migrate
	@echo "$(GREEN)Migrations completed!$(RESET)"

db-seed: ## Seed database with sample data
	@echo "$(BLUE)Seeding database...$(RESET)"
	npm run db:seed
	@echo "$(GREEN)Database seeded!$(RESET)"

db-reset: ## Reset database (drop and recreate)
	@echo "$(RED)Resetting database...$(RESET)"
	npm run db:reset
	@echo "$(GREEN)Database reset completed!$(RESET)"

## Documentation
docs-serve: ## Serve documentation locally
	@echo "$(BLUE)Starting documentation server...$(RESET)"
	npm run docs:serve

docs-build: ## Build documentation
	@echo "$(BLUE)Building documentation...$(RESET)"
	npm run docs:build

## Linting and Formatting
lint: ## Run linting
	@echo "$(BLUE)Running linting...$(RESET)"
	npm run lint

lint-fix: ## Fix linting issues
	@echo "$(BLUE)Fixing linting issues...$(RESET)"
	npm run lint:fix

format: ## Format code
	@echo "$(BLUE)Formatting code...$(RESET)"
	npm run format

format-check: ## Check code formatting
	@echo "$(BLUE)Checking code formatting...$(RESET)"
	npm run format:check

## Production
prod-build: ## Build for production
	@echo "$(BLUE)Building for production...$(RESET)"
	NODE_ENV=production npm run build
	@echo "$(GREEN)Production build completed!$(RESET)"

prod-deploy: ## Deploy to production
	@echo "$(BLUE)Deploying to production...$(RESET)"
	./scripts/deploy.sh
	@echo "$(GREEN)Production deployment completed!$(RESET)"

## Monitoring
logs: ## View application logs
	docker-compose logs -f

metrics: ## Open metrics dashboard
	@echo "$(BLUE)Opening metrics dashboard...$(RESET)"
	open http://localhost:9090

grafana: ## Open Grafana dashboard
	@echo "$(BLUE)Opening Grafana dashboard...$(RESET)"
	open http://localhost:3000

## Utilities
status: ## Check service status
	@echo "$(BLUE)Service Status:$(RESET)"
	@docker-compose ps

health: ## Check health of all services
	@echo "$(BLUE)Health Check:$(RESET)"
	@curl -s http://localhost:3000/health || echo "$(RED)API Gateway: DOWN$(RESET)"
	@curl -s http://localhost:3001/health || echo "$(RED)Auth Service: DOWN$(RESET)"

backup: ## Backup database
	@echo "$(BLUE)Creating database backup...$(RESET)"
	./scripts/backup.sh
	@echo "$(GREEN)Backup completed!$(RESET)"

restore: ## Restore database from backup
	@echo "$(BLUE)Restoring database from backup...$(RESET)"
	./scripts/restore.sh
	@echo "$(GREEN)Restore completed!$(RESET)"
