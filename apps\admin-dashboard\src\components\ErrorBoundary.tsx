/**
 * CloudForge Platform - Error Boundary Component
 * Enterprise-grade error handling for production applications
 * Created by <PERSON><PERSON>
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  BugReport as BugIcon,
  Home as HomeIcon
} from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // Update state with error info
    this.setState({
      error,
      errorInfo,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    });

    // Send error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToService(error, errorInfo);
    }
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to your error monitoring service
    // like Sentry, LogRocket, or Bugsnag
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(), // Implement this method
    };

    // Example: Send to monitoring service
    // fetch('/api/errors', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorData)
    // });

    console.error('Error logged to service:', errorData);
  };

  private getCurrentUserId = (): string | null => {
    // Get current user ID from your auth system
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.id || null;
    } catch {
      return null;
    }
  };

  private handleRefresh = () => {
    // Reset error state and reload the component
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleReload = () => {
    // Reload the entire page
    window.location.reload();
  };

  private handleGoHome = () => {
    // Navigate to home page
    window.location.href = '/';
  };

  private handleReportBug = () => {
    // Open bug report with pre-filled error information
    const subject = encodeURIComponent(`Bug Report - Error ID: ${this.state.errorId}`);
    const body = encodeURIComponent(`
Error Details:
- Error ID: ${this.state.errorId}
- Message: ${this.state.error?.message}
- URL: ${window.location.href}
- Timestamp: ${new Date().toISOString()}
- User Agent: ${navigator.userAgent}

Steps to reproduce:
1. 
2. 
3. 

Expected behavior:


Actual behavior:


Additional context:

    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Box
          sx={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
            p: 3
          }}
        >
          <Card sx={{ maxWidth: 600, width: '100%' }}>
            <CardContent sx={{ p: 4 }}>
              <Stack spacing={3} alignItems="center" textAlign="center">
                {/* Error Icon */}
                <ErrorIcon sx={{ fontSize: 64, color: 'error.main' }} />
                
                {/* Error Title */}
                <Typography variant="h4" color="error.main" fontWeight="bold">
                  Oops! Something went wrong
                </Typography>
                
                {/* Error Description */}
                <Typography variant="body1" color="text.secondary">
                  We're sorry, but something unexpected happened. Our team has been notified 
                  and is working to fix this issue.
                </Typography>

                {/* Error ID */}
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Error ID for support:
                  </Typography>
                  <Chip 
                    label={this.state.errorId} 
                    variant="outlined" 
                    size="small"
                    sx={{ fontFamily: 'monospace' }}
                  />
                </Box>

                {/* Error Details (Development only) */}
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <Alert severity="error" sx={{ width: '100%', textAlign: 'left' }}>
                    <AlertTitle>Development Error Details</AlertTitle>
                    <Typography variant="body2" component="pre" sx={{ 
                      whiteSpace: 'pre-wrap',
                      fontSize: '0.75rem',
                      fontFamily: 'monospace'
                    }}>
                      {this.state.error.message}
                      {this.state.error.stack && (
                        <>
                          {'\n\nStack Trace:\n'}
                          {this.state.error.stack}
                        </>
                      )}
                    </Typography>
                  </Alert>
                )}

                {/* Action Buttons */}
                <Stack direction="row" spacing={2} flexWrap="wrap" justifyContent="center">
                  <Button
                    variant="contained"
                    startIcon={<RefreshIcon />}
                    onClick={this.handleRefresh}
                    color="primary"
                  >
                    Try Again
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<HomeIcon />}
                    onClick={this.handleGoHome}
                  >
                    Go Home
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<BugIcon />}
                    onClick={this.handleReportBug}
                    color="error"
                  >
                    Report Bug
                  </Button>
                </Stack>

                {/* Additional Actions */}
                <Stack direction="row" spacing={2}>
                  <Button
                    variant="text"
                    size="small"
                    onClick={this.handleReload}
                    color="inherit"
                  >
                    Reload Page
                  </Button>
                </Stack>

                {/* Support Information */}
                <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Need help? Contact our support team at{' '}
                    <a href="mailto:<EMAIL>" style={{ color: 'inherit' }}>
                      <EMAIL>
                    </a>
                    {' '}or call +1 (555) 123-4567
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Box>
      );
    }

    // No error, render children normally
    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Hook for error reporting in functional components
export const useErrorHandler = () => {
  const reportError = React.useCallback((error: Error, errorInfo?: any) => {
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error reported:', error, errorInfo);
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      const errorData = {
        message: error.message,
        stack: error.stack,
        errorInfo,
        errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      };

      // Send to monitoring service
      console.error('Error reported to service:', errorData);
    }
  }, []);

  return { reportError };
};

export default ErrorBoundary;
