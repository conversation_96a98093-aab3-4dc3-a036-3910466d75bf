# CloudForge Platform - Docker Compose Configuration
# Enterprise-grade cloud services platform

version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15-alpine
    container_name: cloudforge-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: cloudforge_platform
      POSTGRES_USER: cloudforge
      POSTGRES_PASSWORD: cloudforge_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infra/docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - cloudforge-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cloudforge -d cloudforge_platform"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: cloudforge-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass cloudforge_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./infra/docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - cloudforge-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Core Services
  api-gateway:
    build:
      context: .
      dockerfile: apps/api-gateway/Dockerfile
      target: development
    container_name: cloudforge-api-gateway
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      API_PORT: 3000
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge
      DATABASE_PASSWORD: cloudforge_password
      DATABASE_NAME: cloudforge_platform
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: cloudforge_redis_password
      JWT_SECRET: development-jwt-secret-key-change-in-production
      JWT_REFRESH_SECRET: development-refresh-secret-key-change-in-production
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    networks:
      - cloudforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  auth-service:
    build:
      context: .
      dockerfile: apps/auth-service/Dockerfile
      target: development
    container_name: cloudforge-auth-service
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      API_PORT: 3001
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge
      DATABASE_PASSWORD: cloudforge_password
      DATABASE_NAME: cloudforge_platform
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: cloudforge_redis_password
      JWT_SECRET: development-jwt-secret-key-change-in-production
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - cloudforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  user-service:
    build:
      context: .
      dockerfile: apps/user-service/Dockerfile
      target: development
    container_name: cloudforge-user-service
    restart: unless-stopped
    ports:
      - "3002:3002"
    environment:
      NODE_ENV: development
      API_PORT: 3002
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge
      DATABASE_PASSWORD: cloudforge_password
      DATABASE_NAME: cloudforge_platform
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: cloudforge_redis_password
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - cloudforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  billing-service:
    build:
      context: .
      dockerfile: apps/billing-service/Dockerfile
      target: development
    container_name: cloudforge-billing-service
    restart: unless-stopped
    ports:
      - "3003:3003"
    environment:
      NODE_ENV: development
      API_PORT: 3003
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge
      DATABASE_PASSWORD: cloudforge_password
      DATABASE_NAME: cloudforge_platform
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: cloudforge_redis_password
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - cloudforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  notification-service:
    build:
      context: .
      dockerfile: apps/notification-service/Dockerfile
      target: development
    container_name: cloudforge-notification-service
    restart: unless-stopped
    ports:
      - "3004:3004"
    environment:
      NODE_ENV: development
      API_PORT: 3004
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge
      DATABASE_PASSWORD: cloudforge_password
      DATABASE_NAME: cloudforge_platform
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: cloudforge_redis_password
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - cloudforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  monitoring-service:
    build:
      context: .
      dockerfile: apps/monitoring-service/Dockerfile
      target: development
    container_name: cloudforge-monitoring-service
    restart: unless-stopped
    ports:
      - "3005:3005"
    environment:
      NODE_ENV: development
      API_PORT: 3005
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge
      DATABASE_PASSWORD: cloudforge_password
      DATABASE_NAME: cloudforge_platform
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: cloudforge_redis_password
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - cloudforge-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Frontend Applications
  admin-dashboard:
    build:
      context: .
      dockerfile: apps/admin-dashboard/Dockerfile
      target: development
    container_name: cloudforge-admin-dashboard
    restart: unless-stopped
    ports:
      - "3010:3010"
    environment:
      NODE_ENV: development
      REACT_APP_API_URL: http://localhost:3000
      REACT_APP_WS_URL: ws://localhost:3000
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - cloudforge-network
    depends_on:
      - api-gateway

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: cloudforge-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./infra/docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cloudforge-network

  grafana:
    image: grafana/grafana:latest
    container_name: cloudforge-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: cloudforge_grafana_password
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infra/docker/grafana/provisioning:/etc/grafana/provisioning
      - ./infra/docker/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - cloudforge-network
    depends_on:
      - prometheus

  # Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: cloudforge-rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: cloudforge
      RABBITMQ_DEFAULT_PASS: cloudforge_rabbitmq_password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - cloudforge-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Elasticsearch for logging (optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: cloudforge-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - cloudforge-network
    profiles:
      - logging

  # Kibana for log visualization (optional)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: cloudforge-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    networks:
      - cloudforge-network
    depends_on:
      - elasticsearch
    profiles:
      - logging

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  rabbitmq_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  cloudforge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
