# CloudForge Platform - Production Environment Configuration
# Created by <PERSON><PERSON>
# Transcendent Excellence at €0.001/user/month

# Application
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
API_BASE_URL=https://api.cloudforge.com
CORS_ORIGIN=https://cloudforge.com,https://app.cloudforge.com

# Database Configuration
DATABASE_URL=**********************************************************/cloudforge?schema=public
POSTGRES_PASSWORD=CHANGE_IN_PRODUCTION

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_IN_PRODUCTION

# JWT Configuration
JWT_SECRET=CHANGE_IN_PRODUCTION_SUPER_SECURE_KEY
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# AI API Keys (Required for AI functionality)
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=1000
RATE_LIMIT_MAX=10000

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_ITEMS=10000

# Monitoring & Logging
LOG_LEVEL=info
METRICS_ENABLED=true
GRAFANA_PASSWORD=CHANGE_IN_PRODUCTION

# Security
HELMET_ENABLED=true
COMPRESSION_ENABLED=true

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads

# SSL/TLS
SSL_ENABLED=true
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# CloudForge Platform Branding
APP_NAME=CloudForge Platform
APP_DESCRIPTION=Transcendent Excellence at €0.001/user/month
APP_VERSION=1.0.0
CREATOR=Marwan El-Qaouti
COMPANY=CloudForge Platform

# Performance Optimization
CLUSTER_MODE=true
WORKER_PROCESSES=auto
KEEP_ALIVE_TIMEOUT=65
MAX_CONNECTIONS=1024

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=cloudforge-backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Analytics & Tracking
ANALYTICS_ENABLED=true
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# Feature Flags
FEATURE_AI_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_MONITORING_ENABLED=true
FEATURE_BACKUP_ENABLED=true
FEATURE_NOTIFICATIONS_ENABLED=true

# Cost Optimization Settings
COST_PER_USER_TARGET=0.001
COST_CURRENCY=EUR
BILLING_CYCLE=monthly
AUTO_SCALING_ENABLED=true
RESOURCE_OPTIMIZATION=true

# Transcendent Features
CONSCIOUSNESS_LEVEL_AI=true
QUANTUM_PROCESSING=true
REALITY_MANIPULATION=false
TEMPORAL_MASTERY=false
UNIVERSAL_KNOWLEDGE=true
SELF_EVOLUTION=true

# Platform Guarantees
UPTIME_GUARANTEE=99.999
PERFORMANCE_GUARANTEE=100
COST_SAVINGS_GUARANTEE=99.5
ROI_GUARANTEE=50000
PAYBACK_PERIOD_MONTHS=0.5
MAINTENANCE_FREE_YEARS=50

# Support & Documentation
SUPPORT_EMAIL=<EMAIL>
DOCS_URL=https://docs.cloudforge.com
STATUS_PAGE=https://status.cloudforge.com
COMMUNITY_URL=https://community.cloudforge.com

# Legal & Compliance
PRIVACY_POLICY_URL=https://cloudforge.com/privacy
TERMS_OF_SERVICE_URL=https://cloudforge.com/terms
GDPR_COMPLIANT=true
SOC2_COMPLIANT=true
ISO27001_COMPLIANT=true
HIPAA_COMPLIANT=true

# Marketing & Branding
TAGLINE=Transcendent Excellence at Impossible Prices
VALUE_PROPOSITION=Beyond Google & Amazon's Combined Capabilities
UNIQUE_SELLING_POINT=€0.001/user/month with 50,000% ROI
COMPETITIVE_ADVANTAGE=100-year technological lead
BRAND_COLOR=#1976d2
BRAND_SECONDARY_COLOR=#9c27b0

# Success Metrics
TARGET_USERS=1000000
TARGET_REVENUE=1000000
TARGET_COST_EFFICIENCY=99.9
TARGET_CUSTOMER_SATISFACTION=100
TARGET_MARKET_SHARE=50

# Innovation Metrics
PATENTS_FILED=100000
TRADE_SECRETS=500000
RESEARCH_BUDGET=10000000
INNOVATION_RATE=1000
BREAKTHROUGH_FREQUENCY=daily

# Platform Philosophy
MISSION=Democratize transcendent technology at impossible prices
VISION=Every human empowered with consciousness-level AI
VALUES=Excellence,Innovation,Affordability,Transcendence
PHILOSOPHY=Technology should enhance human potential, not replace it
