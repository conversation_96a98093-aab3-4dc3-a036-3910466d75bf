/**
 * CloudForge Platform - User Repository
 * Enterprise-grade cloud services platform
 */

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { BaseRepository } from '../base/base.repository';
import { User } from '../entities/user.entity';

@Injectable()
export class UserRepository extends BaseRepository<User> {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {
    super(userRepository);
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email: email.toLowerCase() },
      relations: ['roles', 'roles.permissions'],
    });
  }

  /**
   * Find user by username
   */
  async findByUsername(username: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { username: username.toLowerCase() },
      relations: ['roles', 'roles.permissions'],
    });
  }

  /**
   * Find user by email or username
   */
  async findByEmailOrUsername(identifier: string): Promise<User | null> {
    const normalizedIdentifier = identifier.toLowerCase();
    
    return this.userRepository.findOne({
      where: [
        { email: normalizedIdentifier },
        { username: normalizedIdentifier },
      ],
      relations: ['roles', 'roles.permissions'],
    });
  }

  /**
   * Find user with password (for authentication)
   */
  async findByEmailWithPassword(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email: email.toLowerCase() },
      select: [
        'id', 'email', 'username', 'password', 'isActive', 'isEmailVerified',
        'failedLoginAttempts', 'lockedUntil', 'twoFactorSecret', 'isTwoFactorEnabled'
      ],
      relations: ['roles', 'roles.permissions'],
    });
  }

  /**
   * Find user by email verification token
   */
  async findByEmailVerificationToken(token: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { emailVerificationToken: token },
      select: ['id', 'email', 'emailVerificationToken', 'emailVerificationExpiry', 'isEmailVerified'],
    });
  }

  /**
   * Find user by password reset token
   */
  async findByPasswordResetToken(token: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { passwordResetToken: token },
      select: ['id', 'email', 'passwordResetToken', 'passwordResetExpiry'],
    });
  }

  /**
   * Find active users
   */
  async findActiveUsers(): Promise<User[]> {
    return this.userRepository.find({
      where: { isActive: true },
      relations: ['roles'],
    });
  }

  /**
   * Find users by role
   */
  async findByRole(roleName: string): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .where('role.name = :roleName', { roleName })
      .getMany();
  }

  /**
   * Find users with specific permission
   */
  async findByPermission(permissionName: string): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .leftJoinAndSelect('role.permissions', 'permission')
      .where('permission.name = :permissionName', { permissionName })
      .getMany();
  }

  /**
   * Search users by name or email
   */
  async searchUsers(searchTerm: string, limit: number = 10): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .where('user.email ILIKE :search', { search: `%${searchTerm}%` })
      .orWhere('user.username ILIKE :search', { search: `%${searchTerm}%` })
      .orWhere('user.firstName ILIKE :search', { search: `%${searchTerm}%` })
      .orWhere('user.lastName ILIKE :search', { search: `%${searchTerm}%` })
      .limit(limit)
      .getMany();
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    locked: number;
    recentSignups: number;
  }> {
    const total = await this.userRepository.count();
    const active = await this.userRepository.count({ where: { isActive: true } });
    const verified = await this.userRepository.count({ where: { isEmailVerified: true } });
    
    const locked = await this.userRepository
      .createQueryBuilder('user')
      .where('user.lockedUntil > :now', { now: new Date() })
      .getCount();

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentSignups = await this.userRepository.count({
      where: {
        createdAt: {
          $gte: sevenDaysAgo,
        } as any,
      },
    });

    return {
      total,
      active,
      verified,
      locked,
      recentSignups,
    };
  }

  /**
   * Update last login
   */
  async updateLastLogin(userId: string, ipAddress?: string): Promise<void> {
    await this.userRepository.update(userId, {
      lastLoginAt: new Date(),
      lastLoginIp: ipAddress,
      failedLoginAttempts: 0,
      lockedUntil: null,
    });
  }

  /**
   * Increment failed login attempts
   */
  async incrementFailedLoginAttempts(userId: string): Promise<void> {
    const user = await this.findById(userId);
    if (user) {
      user.incrementFailedLoginAttempts();
      await this.save(user);
    }
  }

  /**
   * Lock user account
   */
  async lockAccount(userId: string, duration: number = 15 * 60 * 1000): Promise<void> {
    const lockedUntil = new Date(Date.now() + duration);
    await this.userRepository.update(userId, { lockedUntil });
  }

  /**
   * Unlock user account
   */
  async unlockAccount(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      lockedUntil: null,
      failedLoginAttempts: 0,
    });
  }

  /**
   * Verify user email
   */
  async verifyEmail(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      isEmailVerified: true,
      emailVerificationToken: null,
      emailVerificationExpiry: null,
    });
  }

  /**
   * Set email verification token
   */
  async setEmailVerificationToken(userId: string, token: string, expiry: Date): Promise<void> {
    await this.userRepository.update(userId, {
      emailVerificationToken: token,
      emailVerificationExpiry: expiry,
    });
  }

  /**
   * Set password reset token
   */
  async setPasswordResetToken(userId: string, token: string, expiry: Date): Promise<void> {
    await this.userRepository.update(userId, {
      passwordResetToken: token,
      passwordResetExpiry: expiry,
    });
  }

  /**
   * Clear password reset token
   */
  async clearPasswordResetToken(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      passwordResetToken: null,
      passwordResetExpiry: null,
    });
  }
}
