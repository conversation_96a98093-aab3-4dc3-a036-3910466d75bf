{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "paths": {"@cloudforge/shared": ["libs/shared/src/index.ts"], "@cloudforge/auth": ["libs/auth/src/index.ts"], "@cloudforge/database": ["libs/database/src/index.ts"], "@cloudforge/common": ["libs/common/src/index.ts"], "@cloudforge/config": ["libs/config/src/index.ts"], "@cloudforge/monitoring": ["libs/monitoring/src/index.ts"], "@cloudforge/security": ["libs/security/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}