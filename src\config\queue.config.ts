import { registerAs } from '@nestjs/config';

export default registerAs('queue', () => ({
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_QUEUE_DB, 10) || 1,
  },
  defaultJobOptions: {
    removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE, 10) || 100,
    removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL, 10) || 50,
    attempts: parseInt(process.env.QUEUE_ATTEMPTS, 10) || 3,
    backoff: {
      type: 'exponential',
      delay: parseInt(process.env.QUEUE_BACKOFF_DELAY, 10) || 2000,
    },
  },
  concurrency: parseInt(process.env.QUEUE_CONCURRENCY, 10) || 5,
}));
