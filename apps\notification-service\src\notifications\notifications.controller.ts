import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { SendNotificationDto } from './dto/send-notification.dto';
import { NotificationResponseDto } from './dto/notification-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../auth/enums/user-role.enum';

@ApiTags('notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post('send')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.NOTIFICATION_MANAGER)
  @ApiOperation({ summary: 'Send a notification' })
  @ApiResponse({
    status: 201,
    description: 'Notification sent successfully',
    type: NotificationResponseDto,
  })
  async sendNotification(@Body() sendNotificationDto: SendNotificationDto): Promise<NotificationResponseDto> {
    return this.notificationsService.send(sendNotificationDto);
  }

  @Post('send-bulk')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.NOTIFICATION_MANAGER)
  @ApiOperation({ summary: 'Send bulk notifications' })
  @ApiResponse({ status: 201, description: 'Bulk notifications sent successfully' })
  async sendBulkNotifications(@Body() notifications: SendNotificationDto[]): Promise<{ sent: number; failed: number }> {
    return this.notificationsService.sendBulk(notifications);
  }

  @Get()
  @ApiOperation({ summary: 'Get notifications with pagination' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  async findAll(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('status') status?: string,
    @Query('channel') channel?: string,
    @Query('userId') userId?: string,
  ) {
    return this.notificationsService.findAll({
      page: Math.max(1, page),
      limit: Math.min(100, Math.max(1, limit)),
      status,
      channel,
      userId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get notification by ID' })
  @ApiResponse({
    status: 200,
    description: 'Notification found',
    type: NotificationResponseDto,
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<NotificationResponseDto> {
    return this.notificationsService.findOne(id);
  }

  @Patch(':id/retry')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.NOTIFICATION_MANAGER)
  @ApiOperation({ summary: 'Retry failed notification' })
  @ApiResponse({ status: 200, description: 'Notification retry initiated' })
  async retryNotification(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.notificationsService.retry(id);
    return { message: 'Notification retry initiated' };
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get notifications for a specific user' })
  @ApiResponse({ status: 200, description: 'User notifications retrieved' })
  async getUserNotifications(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('unreadOnly') unreadOnly: boolean = false,
  ) {
    return this.notificationsService.getUserNotifications(userId, {
      page: Math.max(1, page),
      limit: Math.min(100, Math.max(1, limit)),
      unreadOnly,
    });
  }

  @Patch('user/:userId/mark-read')
  @ApiOperation({ summary: 'Mark user notifications as read' })
  @ApiResponse({ status: 200, description: 'Notifications marked as read' })
  async markUserNotificationsAsRead(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body('notificationIds') notificationIds?: string[],
  ): Promise<{ message: string }> {
    await this.notificationsService.markAsRead(userId, notificationIds);
    return { message: 'Notifications marked as read' };
  }

  @Get('stats/overview')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.NOTIFICATION_MANAGER)
  @ApiOperation({ summary: 'Get notification statistics' })
  @ApiResponse({ status: 200, description: 'Notification statistics retrieved' })
  async getStats(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.notificationsService.getStats({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    });
  }

  @Get('delivery-status/:id')
  @ApiOperation({ summary: 'Get notification delivery status' })
  @ApiResponse({ status: 200, description: 'Delivery status retrieved' })
  async getDeliveryStatus(@Param('id', ParseUUIDPipe) id: string) {
    return this.notificationsService.getDeliveryStatus(id);
  }

  @Post('preferences/:userId')
  @ApiOperation({ summary: 'Update user notification preferences' })
  @ApiResponse({ status: 200, description: 'Preferences updated successfully' })
  async updateUserPreferences(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() preferences: any,
  ): Promise<{ message: string }> {
    await this.notificationsService.updateUserPreferences(userId, preferences);
    return { message: 'Preferences updated successfully' };
  }

  @Get('preferences/:userId')
  @ApiOperation({ summary: 'Get user notification preferences' })
  @ApiResponse({ status: 200, description: 'User preferences retrieved' })
  async getUserPreferences(@Param('userId', ParseUUIDPipe) userId: string) {
    return this.notificationsService.getUserPreferences(userId);
  }
}
