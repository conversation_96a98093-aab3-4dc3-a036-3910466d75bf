import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { PrismaService } from '../database/prisma.service';
import { UserRole } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

// Mock bcrypt
jest.mock('bcryptjs');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AuthService', () => {
  let service: AuthService;
  let prismaService: jest.Mocked<PrismaService>;
  let jwtService: jest.Mocked<JwtService>;
  let configService: jest.Mocked<ConfigService>;

  const mockUser = {
    id: 'user-1',
    email: '<EMAIL>',
    username: 'testuser',
    firstName: 'Test',
    lastName: 'User',
    passwordHash: 'hashed-password',
    role: UserRole.USER,
    isActive: true,
    organizationId: null,
    emailVerified: true,
    avatar: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    lastLoginAt: null,
    organization: null,
  };

  beforeEach(async () => {
    const mockPrismaService = {
      user: {
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        updateMany: jest.fn(),
      },
      session: {
        create: jest.fn(),
        findFirst: jest.fn(),
        updateMany: jest.fn(),
      },
      apiKey: {
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
      },
    };

    const mockJwtService = {
      sign: jest.fn(),
      verify: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    prismaService = module.get(PrismaService);
    jwtService = module.get(JwtService);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateUser', () => {
    it('should return user when credentials are valid', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);

      // Act
      const result = await service.validateUser('<EMAIL>', 'password');

      // Assert
      expect(result).toEqual(mockUser);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
        include: { organization: true },
      });
      expect(mockedBcrypt.compare).toHaveBeenCalledWith('password', 'hashed-password');
      expect(prismaService.user.update).toHaveBeenCalledWith({
        where: { id: 'user-1' },
        data: { lastLoginAt: expect.any(Date) },
      });
    });

    it('should return null when user does not exist', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await service.validateUser('<EMAIL>', 'password');

      // Assert
      expect(result).toBeNull();
      expect(mockedBcrypt.compare).not.toHaveBeenCalled();
    });

    it('should return null when password is invalid', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(false as never);

      // Act
      const result = await service.validateUser('<EMAIL>', 'wrongpassword');

      // Assert
      expect(result).toBeNull();
      expect(mockedBcrypt.compare).toHaveBeenCalledWith('wrongpassword', 'hashed-password');
    });

    it('should return null when user is inactive', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, isActive: false };
      prismaService.user.findUnique.mockResolvedValue(inactiveUser);

      // Act
      const result = await service.validateUser('<EMAIL>', 'password');

      // Assert
      expect(result).toBeNull();
      expect(mockedBcrypt.compare).not.toHaveBeenCalled();
    });
  });

  describe('login', () => {
    it('should return login response when credentials are valid', async () => {
      // Arrange
      const mockSession = {
        id: 'session-1',
        userId: 'user-1',
        token: 'access-token',
        refreshToken: 'refresh-token',
        expiresAt: new Date(),
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaService.user.findUnique.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);
      jwtService.sign.mockReturnValueOnce('access-token').mockReturnValueOnce('refresh-token');
      prismaService.session.create.mockResolvedValue(mockSession);
      configService.get.mockReturnValue('7d');

      // Act
      const result = await service.login('<EMAIL>', 'password', '127.0.0.1', 'test-agent');

      // Assert
      expect(result).toEqual({
        user: expect.objectContaining({
          id: 'user-1',
          email: '<EMAIL>',
          username: 'testuser',
        }),
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresIn: 24 * 60 * 60,
      });
      expect(result.user).not.toHaveProperty('passwordHash');
      expect(jwtService.sign).toHaveBeenCalledTimes(2);
      expect(prismaService.session.create).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException when credentials are invalid', async () => {
      // Arrange
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.login('<EMAIL>', 'password')
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('register', () => {
    it('should create new user and return login response', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'password',
        username: 'newuser',
        firstName: 'New',
        lastName: 'User',
      };

      const newUser = {
        ...mockUser,
        id: 'user-2',
        email: '<EMAIL>',
        username: 'newuser',
        firstName: 'New',
        lastName: 'User',
      };

      prismaService.user.findFirst.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashed-password' as never);
      prismaService.user.create.mockResolvedValue(newUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);
      jwtService.sign.mockReturnValueOnce('access-token').mockReturnValueOnce('refresh-token');
      prismaService.session.create.mockResolvedValue({} as any);

      // Act
      const result = await service.register(userData);

      // Assert
      expect(result).toEqual({
        user: expect.objectContaining({
          id: 'user-2',
          email: '<EMAIL>',
          username: 'newuser',
        }),
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresIn: 24 * 60 * 60,
      });
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('password', 12);
      expect(prismaService.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: '<EMAIL>',
          username: 'newuser',
          passwordHash: 'hashed-password',
          role: UserRole.USER,
        }),
        include: { organization: true },
      });
    });

    it('should throw UnauthorizedException when user already exists', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'password',
        username: 'existinguser',
      };

      prismaService.user.findFirst.mockResolvedValue(mockUser);

      // Act & Assert
      await expect(service.register(userData)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('refreshToken', () => {
    it('should return new access token when refresh token is valid', async () => {
      // Arrange
      const mockSession = {
        id: 'session-1',
        userId: 'user-1',
        refreshToken: 'valid-refresh-token',
        isActive: true,
        expiresAt: new Date(Date.now() + 86400000), // 1 day from now
        user: mockUser,
      };

      jwtService.verify.mockReturnValue({
        sub: 'user-1',
        email: '<EMAIL>',
      });
      prismaService.session.findFirst.mockResolvedValue(mockSession);
      jwtService.sign.mockReturnValue('new-access-token');

      // Act
      const result = await service.refreshToken('valid-refresh-token');

      // Assert
      expect(result).toEqual({
        accessToken: 'new-access-token',
        expiresIn: 24 * 60 * 60,
      });
      expect(jwtService.verify).toHaveBeenCalledWith('valid-refresh-token');
      expect(prismaService.session.update).toHaveBeenCalledWith({
        where: { id: 'session-1' },
        data: { token: 'new-access-token' },
      });
    });

    it('should throw UnauthorizedException when refresh token is invalid', async () => {
      // Arrange
      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      await expect(service.refreshToken('invalid-token')).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('logout', () => {
    it('should deactivate user session', async () => {
      // Arrange
      prismaService.session.updateMany.mockResolvedValue({ count: 1 });

      // Act
      await service.logout('user-1', 'access-token');

      // Assert
      expect(prismaService.session.updateMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-1',
          token: 'access-token',
        },
        data: {
          isActive: false,
        },
      });
    });
  });

  describe('createApiKey', () => {
    it('should create new API key', async () => {
      // Arrange
      const mockApiKey = {
        id: 'api-key-1',
        name: 'Test API Key',
        key: 'cf_test_key',
        hashedKey: 'hashed-key',
        userId: 'user-1',
        permissions: [],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: null,
        lastUsedAt: null,
        organizationId: null,
      };

      mockedBcrypt.hash.mockResolvedValue('hashed-key' as never);
      prismaService.apiKey.create.mockResolvedValue(mockApiKey);

      // Act
      const result = await service.createApiKey('user-1', 'Test API Key', []);

      // Assert
      expect(result).toEqual({
        key: expect.stringMatching(/^cf_[a-z0-9]{32}$/),
        hashedKey: 'hashed-key',
      });
      expect(prismaService.apiKey.create).toHaveBeenCalledWith({
        data: {
          name: 'Test API Key',
          key: expect.stringMatching(/^cf_[a-z0-9]{32}$/),
          hashedKey: 'hashed-key',
          userId: 'user-1',
          permissions: [],
        },
      });
    });
  });

  describe('validateApiKey', () => {
    it('should return user when API key is valid', async () => {
      // Arrange
      const mockApiKey = {
        id: 'api-key-1',
        key: 'cf_test_key',
        isActive: true,
        expiresAt: null,
        user: mockUser,
      };

      prismaService.apiKey.findFirst.mockResolvedValue(mockApiKey);

      // Act
      const result = await service.validateApiKey('cf_test_key');

      // Assert
      expect(result).toEqual(mockUser);
      expect(prismaService.apiKey.update).toHaveBeenCalledWith({
        where: { id: 'api-key-1' },
        data: { lastUsedAt: expect.any(Date) },
      });
    });

    it('should return null when API key is invalid', async () => {
      // Arrange
      prismaService.apiKey.findFirst.mockResolvedValue(null);

      // Act
      const result = await service.validateApiKey('invalid-key');

      // Assert
      expect(result).toBeNull();
    });
  });
});
