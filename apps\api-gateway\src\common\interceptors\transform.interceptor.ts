/**
 * CloudForge Platform - Transform Interceptor
 * Enterprise-grade cloud services platform
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';
import { ApiResponse } from '@cloudforge/shared';

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    const request = context.switchToHttp().getRequest<Request>();
    
    return next.handle().pipe(
      map((data) => {
        // Don't transform if data is already in the correct format
        if (data && typeof data === 'object' && 'success' in data) {
          return data;
        }

        // Don't transform health check responses
        if (request.url.includes('/health')) {
          return data;
        }

        // Don't transform file downloads or streams
        if (data instanceof Buffer || data instanceof ArrayBuffer) {
          return data;
        }

        // Transform the response to standardized format
        const response: ApiResponse<T> = {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          path: request.url,
          statusCode: context.switchToHttp().getResponse().statusCode || 200,
        };

        return response;
      })
    );
  }
}
