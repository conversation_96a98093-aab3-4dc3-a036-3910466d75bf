import { Test, TestingModule } from '@nestjs/testing';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { AuditService } from '../audit/audit.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;
  let auditService: AuditService;

  const mockAuthService = {
    register: jest.fn(),
    login: jest.fn(),
    refreshToken: jest.fn(),
    logout: jest.fn(),
    forgotPassword: jest.fn(),
    resetPassword: jest.fn(),
    changePassword: jest.fn(),
    getProfile: jest.fn(),
  };

  const mockAuditService = {
    log: jest.fn(),
  };

  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    isActive: true,
    emailVerified: true,
    roles: [{ id: '1', name: 'user' }],
  };

  const mockAuthResponse = {
    user: mockUser,
    accessToken: 'accessToken',
    refreshToken: 'refreshToken',
    expiresIn: 86400,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: AuditService,
          useValue: mockAuditService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
    auditService = module.get<AuditService>(AuditService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const registerDto: RegisterDto = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'John',
      lastName: 'Doe',
    };

    it('should successfully register a new user', async () => {
      mockAuthService.register.mockResolvedValue(mockAuthResponse);

      const result = await controller.register(registerDto);

      expect(result).toEqual(mockAuthResponse);
      expect(mockAuthService.register).toHaveBeenCalledWith(registerDto);
      expect(mockAuditService.log).toHaveBeenCalledWith({
        action: 'USER_REGISTER',
        userId: mockUser.id,
        details: { email: registerDto.email },
        ipAddress: null,
      });
    });

    it('should handle registration errors', async () => {
      const error = new Error('Registration failed');
      error.code = '23505'; // Unique constraint violation
      mockAuthService.register.mockRejectedValue(error);

      await expect(controller.register(registerDto)).rejects.toThrow(BadRequestException);
      expect(mockAuthService.register).toHaveBeenCalledWith(registerDto);
    });
  });

  describe('login', () => {
    const loginDto: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should successfully login with valid credentials', async () => {
      mockAuthService.login.mockResolvedValue(mockAuthResponse);

      const result = await controller.login(loginDto);

      expect(result).toEqual(mockAuthResponse);
      expect(mockAuthService.login).toHaveBeenCalledWith(loginDto);
      expect(mockAuditService.log).toHaveBeenCalledWith({
        action: 'LOGIN_SUCCESS',
        userId: mockUser.id,
        details: { email: loginDto.email },
        ipAddress: null,
      });
    });

    it('should throw UnauthorizedException for invalid credentials', async () => {
      mockAuthService.login.mockResolvedValue(null);

      await expect(controller.login(loginDto)).rejects.toThrow(UnauthorizedException);
      expect(mockAuthService.login).toHaveBeenCalledWith(loginDto);
      expect(mockAuditService.log).toHaveBeenCalledWith({
        action: 'LOGIN_FAILED',
        userId: null,
        details: { email: loginDto.email, reason: 'Invalid credentials' },
        ipAddress: null,
      });
    });
  });

  describe('refresh', () => {
    const refreshTokenDto: RefreshTokenDto = {
      refreshToken: 'refreshToken123',
    };

    it('should successfully refresh token', async () => {
      mockAuthService.refreshToken.mockResolvedValue(mockAuthResponse);

      const result = await controller.refresh(refreshTokenDto);

      expect(result).toEqual(mockAuthResponse);
      expect(mockAuthService.refreshToken).toHaveBeenCalledWith(refreshTokenDto.refreshToken);
    });

    it('should throw UnauthorizedException for invalid refresh token', async () => {
      mockAuthService.refreshToken.mockResolvedValue(null);

      await expect(controller.refresh(refreshTokenDto)).rejects.toThrow(UnauthorizedException);
      expect(mockAuthService.refreshToken).toHaveBeenCalledWith(refreshTokenDto.refreshToken);
    });
  });

  describe('logout', () => {
    const mockRequest = {
      user: mockUser,
    };

    it('should successfully logout user', async () => {
      mockAuthService.logout.mockResolvedValue(undefined);

      const result = await controller.logout(mockRequest);

      expect(result).toEqual({ message: 'Successfully logged out' });
      expect(mockAuthService.logout).toHaveBeenCalledWith(mockUser.id);
      expect(mockAuditService.log).toHaveBeenCalledWith({
        action: 'LOGOUT',
        userId: mockUser.id,
        details: {},
        ipAddress: null,
      });
    });
  });

  describe('forgotPassword', () => {
    const email = '<EMAIL>';

    it('should successfully request password reset', async () => {
      mockAuthService.forgotPassword.mockResolvedValue(undefined);

      const result = await controller.forgotPassword(email);

      expect(result).toEqual({
        message: 'If the email exists, a password reset link has been sent',
      });
      expect(mockAuthService.forgotPassword).toHaveBeenCalledWith(email);
      expect(mockAuditService.log).toHaveBeenCalledWith({
        action: 'PASSWORD_RESET_REQUESTED',
        userId: null,
        details: { email },
        ipAddress: null,
      });
    });
  });

  describe('resetPassword', () => {
    const resetPasswordDto: ResetPasswordDto = {
      token: 'resetToken123',
      newPassword: 'newPassword123',
    };

    it('should successfully reset password', async () => {
      mockAuthService.resetPassword.mockResolvedValue({ userId: mockUser.id });

      const result = await controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({ message: 'Password successfully reset' });
      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(
        resetPasswordDto.token,
        resetPasswordDto.newPassword,
      );
      expect(mockAuditService.log).toHaveBeenCalledWith({
        action: 'PASSWORD_RESET_COMPLETED',
        userId: mockUser.id,
        details: {},
        ipAddress: null,
      });
    });

    it('should throw BadRequestException for invalid token', async () => {
      mockAuthService.resetPassword.mockResolvedValue(null);

      await expect(controller.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(
        resetPasswordDto.token,
        resetPasswordDto.newPassword,
      );
    });
  });

  describe('changePassword', () => {
    const mockRequest = {
      user: mockUser,
    };

    const changePasswordDto: ChangePasswordDto = {
      currentPassword: 'currentPassword',
      newPassword: 'newPassword123',
    };

    it('should successfully change password', async () => {
      mockAuthService.changePassword.mockResolvedValue(true);

      const result = await controller.changePassword(mockRequest, changePasswordDto);

      expect(result).toEqual({ message: 'Password successfully changed' });
      expect(mockAuthService.changePassword).toHaveBeenCalledWith(
        mockUser.id,
        changePasswordDto.currentPassword,
        changePasswordDto.newPassword,
      );
      expect(mockAuditService.log).toHaveBeenCalledWith({
        action: 'PASSWORD_CHANGED',
        userId: mockUser.id,
        details: {},
        ipAddress: null,
      });
    });

    it('should throw BadRequestException for invalid current password', async () => {
      mockAuthService.changePassword.mockResolvedValue(false);

      await expect(controller.changePassword(mockRequest, changePasswordDto)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockAuthService.changePassword).toHaveBeenCalledWith(
        mockUser.id,
        changePasswordDto.currentPassword,
        changePasswordDto.newPassword,
      );
    });
  });

  describe('getProfile', () => {
    const mockRequest = {
      user: mockUser,
    };

    it('should successfully get user profile', async () => {
      mockAuthService.getProfile.mockResolvedValue(mockUser);

      const result = await controller.getProfile(mockRequest);

      expect(result).toEqual(mockUser);
      expect(mockAuthService.getProfile).toHaveBeenCalledWith(mockUser.id);
    });
  });

  describe('verifyToken', () => {
    const mockRequest = {
      user: mockUser,
    };

    it('should successfully verify token', async () => {
      const result = await controller.verifyToken(mockRequest);

      expect(result).toEqual({
        valid: true,
        user: mockUser,
      });
    });
  });
});
