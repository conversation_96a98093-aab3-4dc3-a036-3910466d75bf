/**
 * CloudForge Platform - Base Entity
 * Enterprise-grade cloud services platform
 */

import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
  })
  deletedAt?: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'User who created this record',
  })
  createdBy?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'User who last updated this record',
  })
  updatedBy?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'User who deleted this record',
  })
  deletedBy?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional metadata for this record',
  })
  metadata?: Record<string, any>;

  @BeforeInsert()
  generateId(): void {
    if (!this.id) {
      this.id = uuidv4();
    }
  }

  @BeforeInsert()
  setCreatedAt(): void {
    if (!this.createdAt) {
      this.createdAt = new Date();
    }
  }

  @BeforeUpdate()
  setUpdatedAt(): void {
    this.updatedAt = new Date();
  }

  /**
   * Soft delete the entity
   */
  softDelete(deletedBy?: string): void {
    this.deletedAt = new Date();
    this.deletedBy = deletedBy;
  }

  /**
   * Restore a soft deleted entity
   */
  restore(): void {
    this.deletedAt = null;
    this.deletedBy = null;
  }

  /**
   * Check if entity is soft deleted
   */
  isDeleted(): boolean {
    return this.deletedAt !== null && this.deletedAt !== undefined;
  }

  /**
   * Update metadata
   */
  updateMetadata(key: string, value: any): void {
    if (!this.metadata) {
      this.metadata = {};
    }
    this.metadata[key] = value;
  }

  /**
   * Get metadata value
   */
  getMetadata(key: string): any {
    return this.metadata?.[key];
  }

  /**
   * Remove metadata key
   */
  removeMetadata(key: string): void {
    if (this.metadata && this.metadata[key] !== undefined) {
      delete this.metadata[key];
    }
  }

  /**
   * Convert entity to plain object
   */
  toJSON(): Record<string, any> {
    const obj = { ...this };
    
    // Remove sensitive or internal fields if needed
    // This can be overridden in child classes
    
    return obj;
  }

  /**
   * Create a copy of the entity (without ID and timestamps)
   */
  clone(): Partial<this> {
    const cloned = { ...this };
    delete cloned.id;
    delete cloned.createdAt;
    delete cloned.updatedAt;
    delete cloned.deletedAt;
    delete cloned.createdBy;
    delete cloned.updatedBy;
    delete cloned.deletedBy;
    
    return cloned;
  }
}
