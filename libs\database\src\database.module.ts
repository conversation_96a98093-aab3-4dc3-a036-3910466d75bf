/**
 * CloudForge Platform - Database Module
 * Enterprise-grade cloud services platform
 */

import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DatabaseService } from './database.service';
import { RedisService } from './redis.service';
import { User } from './entities/user.entity';
import { Role } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { Subscription } from './entities/subscription.entity';
import { BillingPlan } from './entities/billing-plan.entity';
import { Invoice } from './entities/invoice.entity';
import { Payment } from './entities/payment.entity';
import { Notification } from './entities/notification.entity';
import { AuditLog } from './entities/audit-log.entity';
import { CloudResource } from './entities/cloud-resource.entity';

@Global()
@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('database.host'),
        port: configService.get<number>('database.port'),
        username: configService.get<string>('database.username'),
        password: configService.get<string>('database.password'),
        database: configService.get<string>('database.name'),
        ssl: configService.get<boolean>('database.ssl'),
        entities: [
          User,
          Role,
          Permission,
          Subscription,
          BillingPlan,
          Invoice,
          Payment,
          Notification,
          AuditLog,
          CloudResource,
        ],
        migrations: ['dist/libs/database/src/migrations/*.js'],
        migrationsRun: false,
        synchronize: false, // Always use migrations in production
        logging: configService.get<string>('app.environment') === 'development',
        maxQueryExecutionTime: 10000, // 10 seconds
        extra: {
          max: configService.get<number>('database.poolSize', 10),
          connectionTimeoutMillis: configService.get<number>('database.timeout', 30000),
          idleTimeoutMillis: 30000,
        },
      }),
    }),
    TypeOrmModule.forFeature([
      User,
      Role,
      Permission,
      Subscription,
      BillingPlan,
      Invoice,
      Payment,
      Notification,
      AuditLog,
      CloudResource,
    ]),
  ],
  providers: [DatabaseService, RedisService],
  exports: [DatabaseService, RedisService, TypeOrmModule],
})
export class DatabaseModule {}
