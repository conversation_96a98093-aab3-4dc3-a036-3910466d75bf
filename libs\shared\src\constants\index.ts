/**
 * Shared constants for CloudForge Platform
 * Enterprise-grade cloud services platform
 */

// Application constants
export const APP_NAME = 'CloudForge Platform';
export const APP_VERSION = '1.0.0';
export const API_VERSION = 'v1';
export const API_PREFIX = `api/${API_VERSION}`;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// Default pagination
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;
export const DEFAULT_PAGE = 1;

// Cache TTL (in seconds)
export const CACHE_TTL = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400, // 24 hours
} as const;

// Rate limiting
export const RATE_LIMIT = {
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  MAX_REQUESTS: 100,
  AUTH_WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  AUTH_MAX_REQUESTS: 5,
} as const;

// JWT Configuration
export const JWT = {
  DEFAULT_EXPIRES_IN: '24h',
  REFRESH_EXPIRES_IN: '7d',
  ALGORITHM: 'HS256',
  ISSUER: 'cloudforge-platform',
  AUDIENCE: 'cloudforge-users',
} as const;

// User roles and permissions
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  MODERATOR: 'moderator',
  VIEWER: 'viewer',
  BILLING_ADMIN: 'billing_admin',
} as const;

export const PERMISSIONS = {
  // User permissions
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_MANAGE: 'user:manage',

  // Billing permissions
  BILLING_READ: 'billing:read',
  BILLING_MANAGE: 'billing:manage',
  BILLING_ADMIN: 'billing:admin',

  // Notification permissions
  NOTIFICATION_READ: 'notification:read',
  NOTIFICATION_SEND: 'notification:send',
  NOTIFICATION_MANAGE: 'notification:manage',

  // Monitoring permissions
  MONITORING_READ: 'monitoring:read',
  MONITORING_MANAGE: 'monitoring:manage',

  // System permissions
  SYSTEM_READ: 'system:read',
  SYSTEM_MANAGE: 'system:manage',
  SYSTEM_ADMIN: 'system:admin',
} as const;

// Default role permissions
export const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: [
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.BILLING_ADMIN,
    PERMISSIONS.NOTIFICATION_MANAGE,
    PERMISSIONS.MONITORING_MANAGE,
    PERMISSIONS.SYSTEM_ADMIN,
  ],
  [USER_ROLES.USER]: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.BILLING_READ,
    PERMISSIONS.NOTIFICATION_READ,
  ],
  [USER_ROLES.MODERATOR]: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.NOTIFICATION_SEND,
    PERMISSIONS.MONITORING_READ,
  ],
  [USER_ROLES.VIEWER]: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.BILLING_READ,
    PERMISSIONS.NOTIFICATION_READ,
    PERMISSIONS.MONITORING_READ,
  ],
  [USER_ROLES.BILLING_ADMIN]: [
    PERMISSIONS.USER_READ,
    PERMISSIONS.BILLING_ADMIN,
    PERMISSIONS.NOTIFICATION_READ,
  ],
} as const;

// Subscription plans
export const SUBSCRIPTION_PLANS = {
  FREE: 'free',
  BASIC: 'basic',
  PROFESSIONAL: 'professional',
  ENTERPRISE: 'enterprise',
} as const;

export const PLAN_LIMITS = {
  [SUBSCRIPTION_PLANS.FREE]: {
    users: 5,
    storage: 1024, // 1GB in MB
    apiCalls: 1000,
    resources: 2,
  },
  [SUBSCRIPTION_PLANS.BASIC]: {
    users: 25,
    storage: 10240, // 10GB in MB
    apiCalls: 10000,
    resources: 10,
  },
  [SUBSCRIPTION_PLANS.PROFESSIONAL]: {
    users: 100,
    storage: 102400, // 100GB in MB
    apiCalls: 100000,
    resources: 50,
  },
  [SUBSCRIPTION_PLANS.ENTERPRISE]: {
    users: -1, // unlimited
    storage: -1, // unlimited
    apiCalls: -1, // unlimited
    resources: -1, // unlimited
  },
} as const;

// Notification types
export const NOTIFICATION_TYPES = {
  EMAIL: 'email',
  SMS: 'sms',
  PUSH: 'push',
  WEBHOOK: 'webhook',
  IN_APP: 'in_app',
} as const;

export const NOTIFICATION_TEMPLATES = {
  WELCOME: 'welcome',
  EMAIL_VERIFICATION: 'email_verification',
  PASSWORD_RESET: 'password_reset',
  SUBSCRIPTION_CREATED: 'subscription_created',
  SUBSCRIPTION_CANCELLED: 'subscription_cancelled',
  PAYMENT_SUCCESS: 'payment_success',
  PAYMENT_FAILED: 'payment_failed',
  RESOURCE_CREATED: 'resource_created',
  RESOURCE_DELETED: 'resource_deleted',
  SECURITY_ALERT: 'security_alert',
} as const;

// Event types
export const EVENT_TYPES = {
  USER_CREATED: 'user.created',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted',
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  SUBSCRIPTION_CREATED: 'subscription.created',
  SUBSCRIPTION_UPDATED: 'subscription.updated',
  SUBSCRIPTION_CANCELLED: 'subscription.cancelled',
  PAYMENT_COMPLETED: 'payment.completed',
  PAYMENT_FAILED: 'payment.failed',
  RESOURCE_CREATED: 'resource.created',
  RESOURCE_UPDATED: 'resource.updated',
  RESOURCE_DELETED: 'resource.deleted',
  NOTIFICATION_SENT: 'notification.sent',
  SYSTEM_ERROR: 'system.error',
  SECURITY_BREACH: 'security.breach',
} as const;

// File upload limits
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/plain',
    'text/csv',
    'application/json',
  ],
  AVATAR_MAX_SIZE: 2 * 1024 * 1024, // 2MB
  AVATAR_ALLOWED_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ],
} as const;

// Validation patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_-]{3,20}$/,
  PHONE: /^\+?[1-9]\d{1,14}$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  NOT_FOUND: 'Resource not found',
  VALIDATION_FAILED: 'Validation failed',
  INTERNAL_ERROR: 'Internal server error',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
  INVALID_CREDENTIALS: 'Invalid credentials',
  EMAIL_ALREADY_EXISTS: 'Email already exists',
  USERNAME_ALREADY_EXISTS: 'Username already exists',
  WEAK_PASSWORD: 'Password does not meet security requirements',
  INVALID_EMAIL_FORMAT: 'Invalid email format',
  INVALID_TOKEN: 'Invalid or expired token',
  ACCOUNT_DISABLED: 'Account is disabled',
  EMAIL_NOT_VERIFIED: 'Email address not verified',
  SUBSCRIPTION_REQUIRED: 'Active subscription required',
  PLAN_LIMIT_EXCEEDED: 'Plan limit exceeded',
  PAYMENT_REQUIRED: 'Payment required',
  RESOURCE_LIMIT_EXCEEDED: 'Resource limit exceeded',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  PASSWORD_RESET_SENT: 'Password reset email sent',
  PASSWORD_UPDATED: 'Password updated successfully',
  EMAIL_VERIFIED: 'Email verified successfully',
  SUBSCRIPTION_CREATED: 'Subscription created successfully',
  SUBSCRIPTION_UPDATED: 'Subscription updated successfully',
  SUBSCRIPTION_CANCELLED: 'Subscription cancelled successfully',
  PAYMENT_SUCCESS: 'Payment processed successfully',
  NOTIFICATION_SENT: 'Notification sent successfully',
  RESOURCE_CREATED: 'Resource created successfully',
  RESOURCE_UPDATED: 'Resource updated successfully',
  RESOURCE_DELETED: 'Resource deleted successfully',
} as const;

// Database constants
export const DATABASE = {
  DEFAULT_POOL_SIZE: 10,
  CONNECTION_TIMEOUT: 30000,
  QUERY_TIMEOUT: 10000,
  MIGRATION_TABLE: 'migrations',
  SEED_TABLE: 'seeds',
} as const;

// Redis constants
export const REDIS = {
  DEFAULT_DB: 0,
  SESSION_DB: 1,
  CACHE_DB: 2,
  QUEUE_DB: 3,
  KEY_PREFIX: 'cloudforge:',
  SESSION_PREFIX: 'session:',
  CACHE_PREFIX: 'cache:',
  LOCK_PREFIX: 'lock:',
} as const;

// Monitoring constants
export const MONITORING = {
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  METRICS_COLLECTION_INTERVAL: 10000, // 10 seconds
  LOG_RETENTION_DAYS: 30,
  ALERT_COOLDOWN: 300000, // 5 minutes
} as const;

// Security constants
export const SECURITY = {
  BCRYPT_ROUNDS: 12,
  SESSION_TIMEOUT: 3600000, // 1 hour
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 900000, // 15 minutes
  PASSWORD_HISTORY_COUNT: 5,
  MFA_CODE_LENGTH: 6,
  MFA_CODE_EXPIRY: 300000, // 5 minutes
} as const;

// Environment constants
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
  TEST: 'test',
} as const;

// Service ports
export const SERVICE_PORTS = {
  API_GATEWAY: 3000,
  AUTH_SERVICE: 3001,
  USER_SERVICE: 3002,
  BILLING_SERVICE: 3003,
  NOTIFICATION_SERVICE: 3004,
  MONITORING_SERVICE: 3005,
  ADMIN_DASHBOARD: 3010,
  USER_PORTAL: 3011,
} as const;

// External service timeouts
export const TIMEOUTS = {
  HTTP_REQUEST: 30000, // 30 seconds
  DATABASE_QUERY: 10000, // 10 seconds
  REDIS_OPERATION: 5000, // 5 seconds
  EMAIL_SEND: 15000, // 15 seconds
  SMS_SEND: 10000, // 10 seconds
  WEBHOOK_CALL: 30000, // 30 seconds
} as const;
