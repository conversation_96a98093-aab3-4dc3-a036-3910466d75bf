// CloudForge Platform Database Schema
// Created by <PERSON><PERSON>
// Production-ready schema for transcendent platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  username          String    @unique
  firstName         String?
  lastName          String?
  avatar            String?
  passwordHash      String
  emailVerified     <PERSON>olean   @default(false)
  isActive          Boolean   @default(true)
  role              UserRole  @default(USER)
  
  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  organization      Organization? @relation(fields: [organizationId], references: [id])
  organizationId    String?
  
  sessions          Session[]
  apiKeys           ApiKey[]
  auditLogs         AuditLog[]
  aiInteractions    AIInteraction[]
  projects          Project[]
  
  @@map("users")
}

model Organization {
  id                String    @id @default(cuid())
  name              String
  slug              String    @unique
  domain            String?
  logo              String?
  plan              PlanType  @default(STARTER)
  isActive          Boolean   @default(true)
  
  // Billing
  stripeCustomerId  String?
  subscriptionId    String?
  
  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  // Relations
  users             User[]
  projects          Project[]
  apiKeys           ApiKey[]
  usageMetrics      UsageMetric[]
  
  @@map("organizations")
}

model Session {
  id                String    @id @default(cuid())
  userId            String
  token             String    @unique
  refreshToken      String?   @unique
  expiresAt         DateTime
  ipAddress         String?
  userAgent         String?
  isActive          Boolean   @default(true)
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

model ApiKey {
  id                String    @id @default(cuid())
  name              String
  key               String    @unique
  hashedKey         String
  userId            String
  organizationId    String?
  
  permissions       String[]  // JSON array of permissions
  rateLimit         Int       @default(1000)
  isActive          Boolean   @default(true)
  lastUsedAt        DateTime?
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  expiresAt         DateTime?
  
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization      Organization? @relation(fields: [organizationId], references: [id])
  
  @@map("api_keys")
}

// AI & Processing
model AIInteraction {
  id                String    @id @default(cuid())
  userId            String
  type              AIType
  prompt            String
  response          String?
  model             String
  tokens            Int?
  cost              Decimal?  @db.Decimal(10, 6)
  processingTime    Int?      // milliseconds
  status            ProcessingStatus @default(PENDING)
  
  metadata          Json?     // Additional metadata
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  user              User      @relation(fields: [userId], references: [id])
  
  @@map("ai_interactions")
}

model Project {
  id                String    @id @default(cuid())
  name              String
  description       String?
  userId            String
  organizationId    String?
  
  settings          Json?     // Project configuration
  isActive          Boolean   @default(true)
  
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  user              User      @relation(fields: [userId], references: [id])
  organization      Organization? @relation(fields: [organizationId], references: [id])
  
  deployments       Deployment[]
  
  @@map("projects")
}

model Deployment {
  id                String    @id @default(cuid())
  projectId         String
  version           String
  status            DeploymentStatus @default(PENDING)
  environment       Environment @default(DEVELOPMENT)
  
  config            Json?     // Deployment configuration
  logs              String?
  
  deployedAt        DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  project           Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@map("deployments")
}

// Monitoring & Analytics
model UsageMetric {
  id                String    @id @default(cuid())
  organizationId    String
  metricType        MetricType
  value             Decimal   @db.Decimal(15, 6)
  unit              String
  timestamp         DateTime  @default(now())
  
  metadata          Json?
  
  organization      Organization @relation(fields: [organizationId], references: [id])
  
  @@map("usage_metrics")
}

model AuditLog {
  id                String    @id @default(cuid())
  userId            String?
  action            String
  resource          String
  resourceId        String?
  
  oldValues         Json?
  newValues         Json?
  ipAddress         String?
  userAgent         String?
  
  createdAt         DateTime  @default(now())
  
  user              User?     @relation(fields: [userId], references: [id])
  
  @@map("audit_logs")
}

model SystemHealth {
  id                String    @id @default(cuid())
  service           String
  status            HealthStatus @default(HEALTHY)
  responseTime      Int?      // milliseconds
  errorRate         Decimal?  @db.Decimal(5, 4)
  
  metrics           Json?     // Additional health metrics
  
  timestamp         DateTime  @default(now())
  
  @@map("system_health")
}

// Enums
enum UserRole {
  SUPER_ADMIN
  ADMIN
  USER
  VIEWER
}

enum PlanType {
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

enum AIType {
  TEXT_GENERATION
  CODE_GENERATION
  ANALYSIS
  TRANSLATION
  SUMMARIZATION
  QUESTION_ANSWERING
  CREATIVE_WRITING
  PROBLEM_SOLVING
}

enum ProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum DeploymentStatus {
  PENDING
  BUILDING
  DEPLOYING
  DEPLOYED
  FAILED
  CANCELLED
}

enum Environment {
  DEVELOPMENT
  STAGING
  PRODUCTION
}

enum MetricType {
  API_CALLS
  AI_TOKENS
  STORAGE_USAGE
  BANDWIDTH
  PROCESSING_TIME
  ERROR_COUNT
  USER_SESSIONS
}

enum HealthStatus {
  HEALTHY
  DEGRADED
  UNHEALTHY
  UNKNOWN
}
