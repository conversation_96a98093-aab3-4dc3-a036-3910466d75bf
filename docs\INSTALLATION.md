# Installation Guide

## Prerequisites

- Docker & Docker Compose
- Node.js (v18+)
- Terraform
- kubectl & Kubernetes cluster

## Steps

1. Clone the repository.
2. Install dependencies:
   ```
   cd backend && npm install
   cd ../frontend && npm install
   ```
3. Build and run locally:
   ```
   docker-compose up --build
   ```
4. Deploy to Kubernetes:
   ```
   ./scripts/deploy.sh
   ```
5. Access the admin panel at `http://localhost:3000`
