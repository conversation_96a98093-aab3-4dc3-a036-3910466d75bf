# CloudForge Platform - Production Dockerfile
# Created by <PERSON><PERSON>
# Multi-stage build for optimal performance and security

# Stage 1: Build Backend
FROM node:18-alpine AS backend-builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Generate Prisma client
RUN npx prisma generate

# Copy source code
COPY src ./src
COPY tsconfig*.json ./
COPY nest-cli.json ./

# Build application
RUN npm run build

# Stage 2: Build Frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copy frontend package files
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci && npm cache clean --force

# Copy frontend source
COPY frontend ./

# Build frontend
RUN npm run build

# Stage 3: Production Runtime
FROM node:18-alpine AS production

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S cloudforge && \
    adduser -S cloudforge -u 1001

WORKDIR /app

# Copy built backend
COPY --from=backend-builder --chown=cloudforge:cloudforge /app/dist ./dist
COPY --from=backend-builder --chown=cloudforge:cloudforge /app/node_modules ./node_modules
COPY --from=backend-builder --chown=cloudforge:cloudforge /app/prisma ./prisma

# Copy built frontend
COPY --from=frontend-builder --chown=cloudforge:cloudforge /app/frontend/.next ./frontend/.next
COPY --from=frontend-builder --chown=cloudforge:cloudforge /app/frontend/public ./frontend/public
COPY --from=frontend-builder --chown=cloudforge:cloudforge /app/frontend/node_modules ./frontend/node_modules

# Copy configuration files
COPY --chown=cloudforge:cloudforge package*.json ./
COPY --chown=cloudforge:cloudforge .env.production ./.env

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV HOST=0.0.0.0

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/api/v1/health || exit 1

# Switch to non-root user
USER cloudforge

# Expose port
EXPOSE 3000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Start application
CMD ["node", "dist/main.js"]

# Metadata
LABEL maintainer="Marwan El-Qaouti"
LABEL description="CloudForge Platform - Transcendent Excellence at €0.001/user/month"
LABEL version="1.0.0"
LABEL org.opencontainers.image.title="CloudForge Platform"
LABEL org.opencontainers.image.description="The most advanced enterprise platform ever created"
LABEL org.opencontainers.image.authors="Marwan El-Qaouti"
LABEL org.opencontainers.image.vendor="CloudForge Platform"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.url="https://cloudforge.com"
LABEL org.opencontainers.image.documentation="https://docs.cloudforge.com"
LABEL org.opencontainers.image.source="https://github.com/cloudforge/platform"
