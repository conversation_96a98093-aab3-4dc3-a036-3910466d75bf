import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
    'X-Powered-By': 'CloudForge Platform - Transcendent Excellence',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('cloudforge_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add API key if available
    const apiKey = localStorage.getItem('cloudforge_api_key');
    if (apiKey) {
      config.headers['X-API-Key'] = apiKey;
    }

    // Add request timestamp
    config.headers['X-Request-Time'] = new Date().toISOString();

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (token refresh)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('cloudforge_refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          if (response.data.success) {
            const newToken = response.data.data.accessToken;
            localStorage.setItem('cloudforge_token', newToken);
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return apiClient(originalRequest);
          }
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('cloudforge_token');
        localStorage.removeItem('cloudforge_refresh_token');
        localStorage.removeItem('cloudforge_user');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors
    const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
    
    // Don't show toast for certain errors
    const silentErrors = [401, 403];
    if (!silentErrors.includes(error.response?.status)) {
      toast.error(errorMessage);
    }

    return Promise.reject(error);
  }
);

// API Response Types
interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  [key: string]: any;
}

// Authentication API
export const authApi = {
  login: (credentials: { email: string; password: string }): Promise<ApiResponse> =>
    apiClient.post('/auth/login', credentials),

  register: (userData: {
    email: string;
    password: string;
    username: string;
    firstName?: string;
    lastName?: string;
  }): Promise<ApiResponse> =>
    apiClient.post('/auth/register', userData),

  logout: (): Promise<ApiResponse> =>
    apiClient.post('/auth/logout'),

  refreshToken: (data: { refreshToken: string }): Promise<ApiResponse> =>
    apiClient.post('/auth/refresh', data),

  getProfile: (): Promise<ApiResponse> =>
    apiClient.get('/auth/profile'),

  updateProfile: (userData: any): Promise<ApiResponse> =>
    apiClient.put('/users/me', userData),

  updatePassword: (data: { currentPassword: string; newPassword: string }): Promise<ApiResponse> =>
    apiClient.put('/users/me/password', data),

  getSessions: (): Promise<ApiResponse> =>
    apiClient.get('/auth/sessions'),

  revokeSession: (sessionId: string): Promise<ApiResponse> =>
    apiClient.delete(`/auth/sessions/${sessionId}`),

  createApiKey: (data: { name: string; permissions?: string[] }): Promise<ApiResponse> =>
    apiClient.post('/auth/api-key', data),
};

// AI API
export const aiApi = {
  processRequest: (data: {
    prompt: string;
    type: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  }): Promise<ApiResponse> =>
    apiClient.post('/ai/process', data),

  solveProblem: (data: { problem: string }): Promise<ApiResponse> =>
    apiClient.post('/ai/solve-problem', data),

  generateCode: (data: { requirements: string; language: string }): Promise<ApiResponse> =>
    apiClient.post('/ai/generate-code', data),

  analyzeData: (data: { data: string }): Promise<ApiResponse> =>
    apiClient.post('/ai/analyze-data', data),

  createContent: (data: { topic: string; contentType: string }): Promise<ApiResponse> =>
    apiClient.post('/ai/create-content', data),

  getUsageStats: (days?: number): Promise<ApiResponse> =>
    apiClient.get('/ai/usage-stats', { params: { days } }),

  getModelPerformance: (): Promise<ApiResponse> =>
    apiClient.get('/ai/model-performance'),

  getCapabilities: (): Promise<ApiResponse> =>
    apiClient.get('/ai/capabilities'),
};

// Users API
export const usersApi = {
  getAll: (params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse> =>
    apiClient.get('/users', { params }),

  getById: (id: string): Promise<ApiResponse> =>
    apiClient.get(`/users/${id}`),

  getMe: (): Promise<ApiResponse> =>
    apiClient.get('/users/me'),

  create: (userData: any): Promise<ApiResponse> =>
    apiClient.post('/users', userData),

  update: (id: string, userData: any): Promise<ApiResponse> =>
    apiClient.put(`/users/${id}`, userData),

  deactivate: (id: string): Promise<ApiResponse> =>
    apiClient.delete(`/users/${id}/deactivate`),

  reactivate: (id: string): Promise<ApiResponse> =>
    apiClient.put(`/users/${id}/reactivate`),

  getApiKeys: (): Promise<ApiResponse> =>
    apiClient.get('/users/me/api-keys'),

  revokeApiKey: (keyId: string): Promise<ApiResponse> =>
    apiClient.delete(`/users/me/api-keys/${keyId}`),
};

// Organizations API
export const organizationsApi = {
  getAll: (params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse> =>
    apiClient.get('/organizations', { params }),

  getById: (id: string): Promise<ApiResponse> =>
    apiClient.get(`/organizations/${id}`),

  create: (orgData: any): Promise<ApiResponse> =>
    apiClient.post('/organizations', orgData),

  update: (id: string, orgData: any): Promise<ApiResponse> =>
    apiClient.put(`/organizations/${id}`, orgData),

  deactivate: (id: string): Promise<ApiResponse> =>
    apiClient.delete(`/organizations/${id}/deactivate`),

  reactivate: (id: string): Promise<ApiResponse> =>
    apiClient.put(`/organizations/${id}/reactivate`),

  getUsageStats: (id: string, days?: number): Promise<ApiResponse> =>
    apiClient.get(`/organizations/${id}/usage-stats`, { params: { days } }),

  updatePlan: (id: string, plan: string): Promise<ApiResponse> =>
    apiClient.put(`/organizations/${id}/plan`, { plan }),

  getMembers: (id: string): Promise<ApiResponse> =>
    apiClient.get(`/organizations/${id}/members`),

  removeMember: (id: string, userId: string): Promise<ApiResponse> =>
    apiClient.delete(`/organizations/${id}/members/${userId}`),
};

// Projects API
export const projectsApi = {
  getAll: (params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse> =>
    apiClient.get('/projects', { params }),

  getById: (id: string): Promise<ApiResponse> =>
    apiClient.get(`/projects/${id}`),

  create: (projectData: any): Promise<ApiResponse> =>
    apiClient.post('/projects', projectData),

  update: (id: string, projectData: any): Promise<ApiResponse> =>
    apiClient.put(`/projects/${id}`, projectData),

  delete: (id: string): Promise<ApiResponse> =>
    apiClient.delete(`/projects/${id}`),

  getDeployments: (id: string): Promise<ApiResponse> =>
    apiClient.get(`/projects/${id}/deployments`),

  deploy: (id: string, deploymentData: any): Promise<ApiResponse> =>
    apiClient.post(`/projects/${id}/deploy`, deploymentData),
};

// Analytics API
export const analyticsApi = {
  getDashboard: (): Promise<ApiResponse> =>
    apiClient.get('/analytics/dashboard'),

  getUsageMetrics: (params?: { period?: string; metric?: string }): Promise<ApiResponse> =>
    apiClient.get('/analytics/usage-metrics', { params }),

  getCostAnalysis: (params?: { period?: string }): Promise<ApiResponse> =>
    apiClient.get('/analytics/cost-analysis', { params }),

  getPerformanceMetrics: (): Promise<ApiResponse> =>
    apiClient.get('/analytics/performance'),

  getUserActivity: (params?: { userId?: string; days?: number }): Promise<ApiResponse> =>
    apiClient.get('/analytics/user-activity', { params }),
};

// System API
export const systemApi = {
  getHealth: (): Promise<ApiResponse> =>
    apiClient.get('/health'),

  getMetrics: (): Promise<ApiResponse> =>
    apiClient.get('/metrics'),

  getStatus: (): Promise<ApiResponse> =>
    apiClient.get('/status'),
};

export default apiClient;
