# CloudForge Platform - Enterprise Demo Environment
# Optimized for executive demonstrations and enterprise evaluations

version: '3.8'

services:
  # Database Services with Demo Data
  postgres-demo:
    image: postgres:15-alpine
    container_name: cloudforge-demo-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: cloudforge_demo
      POSTGRES_USER: cloudforge_demo
      POSTGRES_PASSWORD: demo_secure_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_demo_data:/var/lib/postgresql/data
      - ./demo-data/postgres/init:/docker-entrypoint-initdb.d
      - ./demo-data/postgres/sample-data.sql:/docker-entrypoint-initdb.d/99-sample-data.sql
    ports:
      - "5433:5432"
    networks:
      - cloudforge-demo-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cloudforge_demo -d cloudforge_demo"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis-demo:
    image: redis:7-alpine
    container_name: cloudforge-demo-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass demo_redis_password
    volumes:
      - redis_demo_data:/data
      - ./demo-data/redis/demo-cache.rdb:/data/dump.rdb
    ports:
      - "6380:6379"
    networks:
      - cloudforge-demo-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Core Demo Services
  api-gateway-demo:
    build:
      context: ../
      dockerfile: apps/api-gateway/Dockerfile
      target: demo
    container_name: cloudforge-demo-api-gateway
    restart: unless-stopped
    ports:
      - "3100:3000"
    environment:
      NODE_ENV: demo
      API_PORT: 3000
      DATABASE_HOST: postgres-demo
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge_demo
      DATABASE_PASSWORD: demo_secure_password
      DATABASE_NAME: cloudforge_demo
      REDIS_HOST: redis-demo
      REDIS_PORT: 6379
      REDIS_PASSWORD: demo_redis_password
      JWT_SECRET: demo-jwt-secret-for-presentation-only
      DEMO_MODE: true
      DEMO_METRICS_ENABLED: true
    volumes:
      - ./demo-data/logs:/app/logs
    networks:
      - cloudforge-demo-network
    depends_on:
      postgres-demo:
        condition: service_healthy
      redis-demo:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  auth-service-demo:
    build:
      context: ../
      dockerfile: apps/auth-service/Dockerfile
      target: demo
    container_name: cloudforge-demo-auth-service
    restart: unless-stopped
    ports:
      - "3101:3001"
    environment:
      NODE_ENV: demo
      API_PORT: 3001
      DATABASE_HOST: postgres-demo
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge_demo
      DATABASE_PASSWORD: demo_secure_password
      DATABASE_NAME: cloudforge_demo
      REDIS_HOST: redis-demo
      REDIS_PORT: 6379
      REDIS_PASSWORD: demo_redis_password
      JWT_SECRET: demo-jwt-secret-for-presentation-only
      DEMO_MODE: true
      DEMO_USERS_ENABLED: true
    networks:
      - cloudforge-demo-network
    depends_on:
      postgres-demo:
        condition: service_healthy
      redis-demo:
        condition: service_healthy

  user-service-demo:
    build:
      context: ../
      dockerfile: apps/user-service/Dockerfile
      target: demo
    container_name: cloudforge-demo-user-service
    restart: unless-stopped
    ports:
      - "3102:3002"
    environment:
      NODE_ENV: demo
      API_PORT: 3002
      DATABASE_HOST: postgres-demo
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge_demo
      DATABASE_PASSWORD: demo_secure_password
      DATABASE_NAME: cloudforge_demo
      REDIS_HOST: redis-demo
      REDIS_PORT: 6379
      REDIS_PASSWORD: demo_redis_password
      DEMO_MODE: true
      DEMO_BULK_OPERATIONS: true
    networks:
      - cloudforge-demo-network
    depends_on:
      postgres-demo:
        condition: service_healthy

  billing-service-demo:
    build:
      context: ../
      dockerfile: apps/billing-service/Dockerfile
      target: demo
    container_name: cloudforge-demo-billing-service
    restart: unless-stopped
    ports:
      - "3103:3003"
    environment:
      NODE_ENV: demo
      API_PORT: 3003
      DATABASE_HOST: postgres-demo
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge_demo
      DATABASE_PASSWORD: demo_secure_password
      DATABASE_NAME: cloudforge_demo
      REDIS_HOST: redis-demo
      REDIS_PORT: 6379
      REDIS_PASSWORD: demo_redis_password
      STRIPE_SECRET_KEY: sk_test_demo_key_for_presentation
      DEMO_MODE: true
      DEMO_TRANSACTIONS: true
    networks:
      - cloudforge-demo-network
    depends_on:
      postgres-demo:
        condition: service_healthy

  notification-service-demo:
    build:
      context: ../
      dockerfile: apps/notification-service/Dockerfile
      target: demo
    container_name: cloudforge-demo-notification-service
    restart: unless-stopped
    ports:
      - "3104:3004"
    environment:
      NODE_ENV: demo
      API_PORT: 3004
      DATABASE_HOST: postgres-demo
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge_demo
      DATABASE_PASSWORD: demo_secure_password
      DATABASE_NAME: cloudforge_demo
      REDIS_HOST: redis-demo
      REDIS_PORT: 6379
      REDIS_PASSWORD: demo_redis_password
      SENDGRID_API_KEY: demo_sendgrid_key
      DEMO_MODE: true
      DEMO_NOTIFICATIONS: true
    networks:
      - cloudforge-demo-network
    depends_on:
      postgres-demo:
        condition: service_healthy

  monitoring-service-demo:
    build:
      context: ../
      dockerfile: apps/monitoring-service/Dockerfile
      target: demo
    container_name: cloudforge-demo-monitoring-service
    restart: unless-stopped
    ports:
      - "3105:3005"
    environment:
      NODE_ENV: demo
      API_PORT: 3005
      DATABASE_HOST: postgres-demo
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge_demo
      DATABASE_PASSWORD: demo_secure_password
      DATABASE_NAME: cloudforge_demo
      REDIS_HOST: redis-demo
      REDIS_PORT: 6379
      REDIS_PASSWORD: demo_redis_password
      DEMO_MODE: true
      DEMO_METRICS_SIMULATION: true
    networks:
      - cloudforge-demo-network
    depends_on:
      postgres-demo:
        condition: service_healthy

  # Frontend Demo Application
  admin-dashboard-demo:
    build:
      context: ../
      dockerfile: apps/admin-dashboard/Dockerfile
      target: demo
    container_name: cloudforge-demo-admin-dashboard
    restart: unless-stopped
    ports:
      - "3110:3010"
    environment:
      NODE_ENV: demo
      REACT_APP_API_URL: http://localhost:3100
      REACT_APP_WS_URL: ws://localhost:3100
      REACT_APP_DEMO_MODE: true
      REACT_APP_DEMO_BANNER: true
    volumes:
      - ./demo-data/frontend/assets:/app/public/demo-assets
    networks:
      - cloudforge-demo-network
    depends_on:
      - api-gateway-demo

  # Enhanced Monitoring Stack for Demo
  prometheus-demo:
    image: prom/prometheus:latest
    container_name: cloudforge-demo-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./demo-data/prometheus/prometheus-demo.yml:/etc/prometheus/prometheus.yml
      - ./demo-data/prometheus/demo-rules:/etc/prometheus/rules
      - prometheus_demo_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - cloudforge-demo-network

  grafana-demo:
    image: grafana/grafana:latest
    container_name: cloudforge-demo-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: demo
      GF_SECURITY_ADMIN_PASSWORD: demo_password
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
      GF_DASHBOARDS_DEFAULT_HOME_DASHBOARD_PATH: /var/lib/grafana/dashboards/enterprise-overview.json
    volumes:
      - grafana_demo_data:/var/lib/grafana
      - ./demo-data/grafana/provisioning:/etc/grafana/provisioning
      - ./demo-data/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - cloudforge-demo-network
    depends_on:
      - prometheus-demo

  # Load Generator for Demo
  demo-load-generator:
    image: alpine/curl:latest
    container_name: cloudforge-demo-load-generator
    restart: unless-stopped
    command: >
      sh -c "
        while true; do
          curl -s http://api-gateway-demo:3000/health > /dev/null
          curl -s http://api-gateway-demo:3000/api/v1/users?page=1&limit=10 > /dev/null
          curl -s http://api-gateway-demo:3000/api/v1/billing/subscriptions > /dev/null
          curl -s http://api-gateway-demo:3000/api/v1/monitoring/metrics > /dev/null
          sleep 2
        done
      "
    networks:
      - cloudforge-demo-network
    depends_on:
      - api-gateway-demo

  # Demo Data Seeder
  demo-data-seeder:
    build:
      context: ./demo-data/seeder
      dockerfile: Dockerfile
    container_name: cloudforge-demo-data-seeder
    environment:
      DATABASE_HOST: postgres-demo
      DATABASE_PORT: 5432
      DATABASE_USERNAME: cloudforge_demo
      DATABASE_PASSWORD: demo_secure_password
      DATABASE_NAME: cloudforge_demo
      REDIS_HOST: redis-demo
      REDIS_PORT: 6379
      REDIS_PASSWORD: demo_redis_password
      DEMO_USERS_COUNT: 1000
      DEMO_TRANSACTIONS_COUNT: 5000
      DEMO_NOTIFICATIONS_COUNT: 2000
    networks:
      - cloudforge-demo-network
    depends_on:
      postgres-demo:
        condition: service_healthy
      redis-demo:
        condition: service_healthy
    profiles:
      - seeder

volumes:
  postgres_demo_data:
    driver: local
  redis_demo_data:
    driver: local
  prometheus_demo_data:
    driver: local
  grafana_demo_data:
    driver: local

networks:
  cloudforge-demo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
