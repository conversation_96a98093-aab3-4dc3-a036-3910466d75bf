/**
 * CloudForge Platform - Database Seed
 * Creates initial data for development and demo purposes
 * Created by <PERSON><PERSON>
 */

import { PrismaClient, UserRole, PlanType } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting CloudForge Platform database seeding...');

  // Create default organization
  const organization = await prisma.organization.upsert({
    where: { slug: 'cloudforge-platform' },
    update: {},
    create: {
      name: 'CloudForge Platform',
      slug: 'cloudforge-platform',
      domain: 'cloudforge-platform.com',
      plan: PlanType.ENTERPRISE,
      isActive: true,
    },
  });

  console.log('✅ Created organization:', organization.name);

  // Create admin user
  const adminPassword = await bcrypt.hash('CloudForge2024!', 12);
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      firstName: 'CloudForge',
      lastName: 'Admin',
      passwordHash: adminPassword,
      emailVerified: true,
      isActive: true,
      role: UserRole.ADMIN,
      organizationId: organization.id,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create demo users
  const demoUsers = [
    {
      email: '<EMAIL>',
      username: 'manager',
      firstName: 'Demo',
      lastName: 'Manager',
      role: UserRole.MANAGER,
    },
    {
      email: '<EMAIL>',
      username: 'user',
      firstName: 'Demo',
      lastName: 'User',
      role: UserRole.USER,
    },
    {
      email: '<EMAIL>',
      username: 'viewer',
      firstName: 'Demo',
      lastName: 'Viewer',
      role: UserRole.VIEWER,
    },
  ];

  const demoPassword = await bcrypt.hash('Demo2024!', 12);
  
  for (const userData of demoUsers) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        passwordHash: demoPassword,
        emailVerified: true,
        isActive: true,
        organizationId: organization.id,
      },
    });
    console.log('✅ Created demo user:', user.email);
  }

  // Create demo projects
  const projects = [
    {
      name: 'AI Analytics Dashboard',
      description: 'Advanced AI-powered analytics dashboard with quantum processing',
      status: 'ACTIVE',
    },
    {
      name: 'Quantum Computing Engine',
      description: 'Quantum processing engine with 1M+ qubits for enterprise applications',
      status: 'ACTIVE',
    },
    {
      name: 'Consciousness AI Module',
      description: 'AI consciousness engine with 95% transcendent level capabilities',
      status: 'DEVELOPMENT',
    },
  ];

  for (const projectData of projects) {
    const project = await prisma.project.upsert({
      where: { name: projectData.name },
      update: {},
      create: {
        ...projectData,
        organizationId: organization.id,
        userId: adminUser.id,
      },
    });
    console.log('✅ Created project:', project.name);
  }

  // Create API keys
  const apiKey = await prisma.apiKey.upsert({
    where: { name: 'Demo API Key' },
    update: {},
    create: {
      name: 'Demo API Key',
      key: 'cf_demo_' + Math.random().toString(36).substring(2, 15),
      hashedKey: await bcrypt.hash('demo_key_hash', 12),
      userId: adminUser.id,
      organizationId: organization.id,
      permissions: ['read', 'write', 'admin'],
      rateLimit: 10000,
      isActive: true,
    },
  });

  console.log('✅ Created API key:', apiKey.name);

  // Create usage metrics
  const currentDate = new Date();
  const usageMetrics = [];

  for (let i = 0; i < 30; i++) {
    const date = new Date(currentDate);
    date.setDate(date.getDate() - i);

    const metric = await prisma.usageMetric.create({
      data: {
        organizationId: organization.id,
        date: date,
        apiCalls: Math.floor(Math.random() * 10000) + 1000,
        dataProcessed: Math.floor(Math.random() * 1000) + 100, // GB
        activeUsers: Math.floor(Math.random() * 1000) + 100,
        cost: Math.random() * 100 + 10, // USD
      },
    });

    usageMetrics.push(metric);
  }

  console.log('✅ Created usage metrics for last 30 days');

  // Create AI interactions
  const aiInteractions = [
    {
      type: 'CHAT',
      prompt: 'Analyze user behavior patterns for optimization',
      response: 'Based on quantum analysis, users show 95% engagement with AI features',
      model: 'cloudforge-quantum-ai-v1',
      tokensUsed: 150,
      processingTime: 0.8,
    },
    {
      type: 'ANALYSIS',
      prompt: 'Generate performance insights for dashboard',
      response: 'System operating at 99.9% efficiency with quantum acceleration active',
      model: 'cloudforge-consciousness-ai-v1',
      tokensUsed: 200,
      processingTime: 1.2,
    },
    {
      type: 'PREDICTION',
      prompt: 'Forecast user growth for next quarter',
      response: 'Predicted growth: 750M users by Q4 2024 with 94% confidence',
      model: 'cloudforge-predictive-ai-v1',
      tokensUsed: 300,
      processingTime: 2.1,
    },
  ];

  for (const interactionData of aiInteractions) {
    const interaction = await prisma.aIInteraction.create({
      data: {
        ...interactionData,
        userId: adminUser.id,
      },
    });
    console.log('✅ Created AI interaction:', interaction.type);
  }

  // Create audit logs
  const auditLogs = [
    {
      action: 'USER_LOGIN',
      resource: 'auth',
      resourceId: adminUser.id,
      details: { ip: '127.0.0.1', userAgent: 'CloudForge Admin Dashboard' },
    },
    {
      action: 'PROJECT_CREATED',
      resource: 'project',
      resourceId: 'project_1',
      details: { projectName: 'AI Analytics Dashboard' },
    },
    {
      action: 'API_KEY_GENERATED',
      resource: 'api_key',
      resourceId: apiKey.id,
      details: { keyName: 'Demo API Key' },
    },
  ];

  for (const logData of auditLogs) {
    const log = await prisma.auditLog.create({
      data: {
        ...logData,
        userId: adminUser.id,
        details: logData.details,
      },
    });
    console.log('✅ Created audit log:', log.action);
  }

  console.log('');
  console.log('🎉 CloudForge Platform database seeding completed!');
  console.log('');
  console.log('📊 Created:');
  console.log('   • 1 Organization (CloudForge Platform)');
  console.log('   • 4 Users (1 admin, 3 demo users)');
  console.log('   • 3 Demo Projects');
  console.log('   • 1 API Key');
  console.log('   • 30 Days of Usage Metrics');
  console.log('   • 3 AI Interactions');
  console.log('   • 3 Audit Logs');
  console.log('');
  console.log('🔑 Login Credentials:');
  console.log('   Admin: <EMAIL> / CloudForge2024!');
  console.log('   Manager: <EMAIL> / Demo2024!');
  console.log('   User: <EMAIL> / Demo2024!');
  console.log('   Viewer: <EMAIL> / Demo2024!');
  console.log('');
  console.log('🚀 Ready for demo and development!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
