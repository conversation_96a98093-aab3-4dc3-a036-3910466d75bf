/**
 * CloudForge Platform - Auth Service (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { LoginDto, RegisterDto, RefreshTokenDto } from './dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly authServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.authServiceUrl = this.configService.get<string>('services.authService.url');
  }

  async login(loginDto: LoginDto) {
    try {
      this.logger.log(`Proxying login request for user: ${loginDto.email}`);
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.authServiceUrl}/auth/login`, loginDto)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Login request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async register(registerDto: RegisterDto) {
    try {
      this.logger.log(`Proxying registration request for user: ${registerDto.email}`);
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.authServiceUrl}/auth/register`, registerDto)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Registration request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async refresh(refreshTokenDto: RefreshTokenDto) {
    try {
      this.logger.log('Proxying token refresh request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.authServiceUrl}/auth/refresh`, refreshTokenDto)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Token refresh request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async logout() {
    try {
      this.logger.log('Proxying logout request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.authServiceUrl}/auth/logout`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Logout request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getProfile() {
    try {
      this.logger.log('Proxying get profile request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.authServiceUrl}/auth/me`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get profile request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async googleAuth() {
    try {
      this.logger.log('Proxying Google OAuth request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.authServiceUrl}/auth/oauth/google`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Google OAuth request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async googleAuthCallback() {
    try {
      this.logger.log('Proxying Google OAuth callback');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.authServiceUrl}/auth/oauth/google/callback`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Google OAuth callback failed', error.response?.data || error.message);
      throw error;
    }
  }

  async githubAuth() {
    try {
      this.logger.log('Proxying GitHub OAuth request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.authServiceUrl}/auth/oauth/github`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('GitHub OAuth request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async githubAuthCallback() {
    try {
      this.logger.log('Proxying GitHub OAuth callback');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.authServiceUrl}/auth/oauth/github/callback`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('GitHub OAuth callback failed', error.response?.data || error.message);
      throw error;
    }
  }
}
