/**
 * CloudForge Platform - Permission Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { Role } from './role.entity';

@Entity('permissions')
@Index(['name'], { unique: true })
@Index(['resource', 'action'], { unique: true })
@Index(['resource'])
@Index(['action'])
@Index(['isSystem'])
export class Permission extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Permission name (unique identifier)',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Resource this permission applies to',
  })
  resource: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Action this permission allows',
  })
  action: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Permission description',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Human-readable display name',
  })
  displayName?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a system permission (cannot be deleted)',
  })
  isSystem: boolean;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Permission category for grouping',
  })
  category?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Permission priority/order for display',
  })
  priority: number;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional permission constraints or conditions',
  })
  constraints?: Record<string, any>;

  // Relationships
  @ManyToMany(() => Role, role => role.permissions)
  roles: Role[];

  // Hooks
  @BeforeInsert()
  @BeforeUpdate()
  normalizeName(): void {
    if (this.name) {
      this.name = this.name.toLowerCase().trim();
    }
    if (this.resource) {
      this.resource = this.resource.toLowerCase().trim();
    }
    if (this.action) {
      this.action = this.action.toLowerCase().trim();
    }
  }

  @BeforeInsert()
  generateName(): void {
    if (!this.name && this.resource && this.action) {
      this.name = `${this.resource}:${this.action}`;
    }
  }

  @BeforeInsert()
  setDefaults(): void {
    if (!this.displayName) {
      this.displayName = this.generateDisplayName();
    }
    
    if (!this.category) {
      this.category = this.capitalizeWords(this.resource);
    }
  }

  // Methods
  getRoleCount(): number {
    return this.roles?.length || 0;
  }

  canBeDeleted(): boolean {
    return !this.isSystem && this.getRoleCount() === 0;
  }

  matches(resource: string, action: string): boolean {
    return this.resource === resource.toLowerCase() && this.action === action.toLowerCase();
  }

  private generateDisplayName(): string {
    const resourceName = this.capitalizeWords(this.resource);
    const actionName = this.capitalizeWords(this.action);
    
    const actionMap: Record<string, string> = {
      create: 'Create',
      read: 'View',
      update: 'Edit',
      delete: 'Delete',
      manage: 'Manage',
      execute: 'Execute',
      list: 'List',
      view: 'View',
      edit: 'Edit',
      remove: 'Remove',
    };

    const mappedAction = actionMap[this.action] || actionName;
    return `${mappedAction} ${resourceName}`;
  }

  private capitalizeWords(str: string): string {
    return str
      .split(/[-_\s]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  updateConstraint(key: string, value: any): void {
    if (!this.constraints) {
      this.constraints = {};
    }
    this.constraints[key] = value;
  }

  getConstraint(key: string, defaultValue?: any): any {
    return this.constraints?.[key] ?? defaultValue;
  }

  hasConstraint(key: string): boolean {
    return this.constraints && this.constraints[key] !== undefined;
  }

  removeConstraint(key: string): void {
    if (this.constraints && this.constraints[key] !== undefined) {
      delete this.constraints[key];
    }
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    
    // Add computed fields
    obj.roleCount = this.getRoleCount();
    obj.canBeDeleted = this.canBeDeleted();
    
    return obj;
  }
}
