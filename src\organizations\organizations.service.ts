import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { Organization, PlanType } from '@prisma/client';

interface CreateOrganizationDto {
  name: string;
  slug: string;
  domain?: string;
  logo?: string;
  plan?: PlanType;
}

interface UpdateOrganizationDto {
  name?: string;
  domain?: string;
  logo?: string;
  plan?: PlanType;
}

@Injectable()
export class OrganizationsService {
  private readonly logger = new Logger(OrganizationsService.name);

  constructor(private prisma: PrismaService) {}

  async findAll(page: number = 1, limit: number = 10, search?: string) {
    const searchFields = ['name', 'slug', 'domain'];
    
    const result = await this.prisma.searchWithPagination(
      'organization',
      search,
      searchFields,
      page,
      limit,
      { createdAt: 'desc' }
    );

    return result;
  }

  async findById(id: string): Promise<Organization> {
    const organization = await this.prisma.organization.findUnique({
      where: { id },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            username: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            users: true,
            projects: true,
            apiKeys: true,
            usageMetrics: true,
          },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization;
  }

  async findBySlug(slug: string): Promise<Organization | null> {
    return this.prisma.organization.findUnique({
      where: { slug },
      include: { users: true },
    });
  }

  async create(createOrganizationDto: CreateOrganizationDto): Promise<Organization> {
    // Check if organization with slug already exists
    const existingOrg = await this.prisma.organization.findUnique({
      where: { slug: createOrganizationDto.slug },
    });

    if (existingOrg) {
      throw new BadRequestException('Organization with this slug already exists');
    }

    const organization = await this.prisma.organization.create({
      data: {
        ...createOrganizationDto,
        plan: createOrganizationDto.plan || PlanType.STARTER,
      },
    });

    this.logger.log(`✅ Organization created: ${organization.name}`);
    return organization;
  }

  async update(id: string, updateOrganizationDto: UpdateOrganizationDto): Promise<Organization> {
    const existingOrg = await this.prisma.organization.findUnique({
      where: { id },
    });

    if (!existingOrg) {
      throw new NotFoundException('Organization not found');
    }

    const organization = await this.prisma.organization.update({
      where: { id },
      data: updateOrganizationDto,
    });

    this.logger.log(`✅ Organization updated: ${organization.name}`);
    return organization;
  }

  async deactivate(id: string): Promise<void> {
    const organization = await this.prisma.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    await this.prisma.organization.update({
      where: { id },
      data: { isActive: false },
    });

    // Deactivate all users in the organization
    await this.prisma.user.updateMany({
      where: { organizationId: id },
      data: { isActive: false },
    });

    this.logger.log(`✅ Organization deactivated: ${organization.name}`);
  }

  async reactivate(id: string): Promise<void> {
    const organization = await this.prisma.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    await this.prisma.organization.update({
      where: { id },
      data: { isActive: true },
    });

    this.logger.log(`✅ Organization reactivated: ${organization.name}`);
  }

  async getUsageStats(id: string, days: number = 30) {
    const organization = await this.prisma.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get AI usage stats
    const aiStats = await this.prisma.aIInteraction.aggregate({
      where: {
        user: { organizationId: id },
        createdAt: { gte: startDate },
        status: 'COMPLETED',
      },
      _sum: {
        tokens: true,
        cost: true,
      },
      _count: true,
      _avg: {
        processingTime: true,
      },
    });

    // Get user count
    const userCount = await this.prisma.user.count({
      where: {
        organizationId: id,
        isActive: true,
      },
    });

    // Get project count
    const projectCount = await this.prisma.project.count({
      where: {
        organizationId: id,
        isActive: true,
      },
    });

    // Calculate cost efficiency
    const totalCost = aiStats._sum.cost || 0;
    const costPerUser = userCount > 0 ? totalCost / userCount : 0;

    return {
      organization: {
        id: organization.id,
        name: organization.name,
        plan: organization.plan,
        isActive: organization.isActive,
      },
      usage: {
        totalRequests: aiStats._count,
        totalTokens: aiStats._sum.tokens || 0,
        totalCost,
        averageProcessingTime: aiStats._avg.processingTime || 0,
        period: `${days} days`,
      },
      metrics: {
        activeUsers: userCount,
        activeProjects: projectCount,
        costPerUser,
        costPerUserPerMonth: costPerUser * (30 / days),
      },
      costEfficiency: {
        targetCostPerUser: 0.001, // €0.001/month
        actualCostPerUser: costPerUser * (30 / days),
        savings: 'Up to 99.5% vs competitors',
        roi: '50,000% within 6 months',
      },
    };
  }

  async updatePlan(id: string, plan: PlanType): Promise<Organization> {
    const organization = await this.prisma.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const updatedOrg = await this.prisma.organization.update({
      where: { id },
      data: { plan },
    });

    this.logger.log(`✅ Organization plan updated: ${organization.name} -> ${plan}`);
    return updatedOrg;
  }

  async getMembers(id: string) {
    const organization = await this.prisma.organization.findUnique({
      where: { id },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            username: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true,
            createdAt: true,
            lastLoginAt: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization.users;
  }

  async removeMember(organizationId: string, userId: string): Promise<void> {
    const user = await this.prisma.user.findFirst({
      where: {
        id: userId,
        organizationId,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found in organization');
    }

    await this.prisma.user.update({
      where: { id: userId },
      data: { organizationId: null },
    });

    this.logger.log(`✅ User removed from organization: ${user.email}`);
  }
}
