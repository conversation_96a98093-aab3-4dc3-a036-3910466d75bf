/**
 * CloudForge Platform - API Hook
 * Custom hook for making API calls with authentication and error handling
 * Created by <PERSON><PERSON>
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';

// Types
interface ApiResponse<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
  skipAuth?: boolean;
  skipErrorNotification?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api/v1';

// API Client Class
class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private getAuthToken(): string | null {
    return localStorage.getItem('cloudforge_token');
  }

  private getHeaders(options: ApiOptions = {}): Record<string, string> {
    const headers = { ...this.defaultHeaders, ...options.headers };
    
    if (!options.skipAuth) {
      const token = this.getAuthToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }
    
    return headers;
  }

  async request<T = any>(endpoint: string, options: ApiOptions = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      method: options.method || 'GET',
      headers: this.getHeaders(options),
    };

    if (options.body && options.method !== 'GET') {
      config.body = JSON.stringify(options.body);
    }

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        if (response.status === 401) {
          // Token expired, redirect to login
          localStorage.removeItem('cloudforge_token');
          localStorage.removeItem('cloudforge_refresh_token');
          localStorage.removeItem('cloudforge_user');
          window.location.href = '/login';
          throw new Error('Authentication required');
        }
        
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  // Convenience methods
  get<T = any>(endpoint: string, options?: Omit<ApiOptions, 'method'>): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  post<T = any>(endpoint: string, body?: any, options?: Omit<ApiOptions, 'method' | 'body'>): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body });
  }

  put<T = any>(endpoint: string, body?: any, options?: Omit<ApiOptions, 'method' | 'body'>): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body });
  }

  patch<T = any>(endpoint: string, body?: any, options?: Omit<ApiOptions, 'method' | 'body'>): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body });
  }

  delete<T = any>(endpoint: string, options?: Omit<ApiOptions, 'method'>): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Main useApi hook
export function useApi<T = any>(
  endpoint: string | null,
  options: ApiOptions = {}
): ApiResponse<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { showError } = useNotification();

  const fetchData = useCallback(async () => {
    if (!endpoint) return;

    setLoading(true);
    setError(null);

    try {
      const result = await apiClient.request<T>(endpoint, options);
      setData(result);
      
      if (options.onSuccess) {
        options.onSuccess(result);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      
      if (!options.skipErrorNotification) {
        showError(errorMessage, 'API Error');
      }
      
      if (options.onError) {
        options.onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  }, [endpoint, options, showError]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
}

// Specialized hooks for common API patterns
export function useApiMutation<T = any>(
  endpoint: string,
  options: ApiOptions = {}
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { showError, showSuccess } = useNotification();

  const mutate = useCallback(async (body?: any): Promise<T | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiClient.request<T>(endpoint, { ...options, body });
      
      if (options.onSuccess) {
        options.onSuccess(result);
      } else if (!options.skipErrorNotification) {
        showSuccess('Operation completed successfully');
      }
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      
      if (!options.skipErrorNotification) {
        showError(errorMessage, 'Operation Failed');
      }
      
      if (options.onError) {
        options.onError(errorMessage);
      }
      
      return null;
    } finally {
      setLoading(false);
    }
  }, [endpoint, options, showError, showSuccess]);

  return { mutate, loading, error };
}

// Hook for paginated data
export function usePaginatedApi<T = any>(
  endpoint: string,
  options: ApiOptions & { page?: number; limit?: number } = {}
) {
  const { page = 1, limit = 10, ...apiOptions } = options;
  const paginatedEndpoint = `${endpoint}?page=${page}&limit=${limit}`;
  
  return useApi<{
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>(paginatedEndpoint, apiOptions);
}

// Hook for real-time data with polling
export function usePollingApi<T = any>(
  endpoint: string,
  interval: number = 5000,
  options: ApiOptions = {}
) {
  const apiResponse = useApi<T>(endpoint, options);

  useEffect(() => {
    if (!endpoint) return;

    const intervalId = setInterval(() => {
      apiResponse.refetch();
    }, interval);

    return () => clearInterval(intervalId);
  }, [endpoint, interval, apiResponse.refetch]);

  return apiResponse;
}

// Export types
export type { ApiResponse, ApiOptions };
export default useApi;
