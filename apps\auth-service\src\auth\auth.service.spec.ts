import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { RolesService } from '../roles/roles.service';
import { User } from '../users/entities/user.entity';
import { RefreshToken } from './entities/refresh-token.entity';
import { PasswordReset } from './entities/password-reset.entity';
import { UnauthorizedException } from '@nestjs/common';

describe('AuthService', () => {
  let service: AuthService;
  let userRepository: Repository<User>;
  let refreshTokenRepository: Repository<RefreshToken>;
  let passwordResetRepository: Repository<PasswordReset>;
  let jwtService: JwtService;
  let usersService: UsersService;
  let rolesService: RolesService;

  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    password: 'hashedPassword',
    firstName: 'John',
    lastName: 'Doe',
    isActive: true,
    emailVerified: true,
    roles: [{ id: '1', name: 'user', permissions: [] }],
    lastLoginAt: null,
  };

  const mockUserRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockRefreshTokenRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    delete: jest.fn(),
  };

  const mockPasswordResetRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  const mockUsersService = {
    findByEmail: jest.fn(),
  };

  const mockRolesService = {
    findByName: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(RefreshToken),
          useValue: mockRefreshTokenRepository,
        },
        {
          provide: getRepositoryToken(PasswordReset),
          useValue: mockPasswordResetRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: RolesService,
          useValue: mockRolesService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    refreshTokenRepository = module.get<Repository<RefreshToken>>(getRepositoryToken(RefreshToken));
    passwordResetRepository = module.get<Repository<PasswordReset>>(getRepositoryToken(PasswordReset));
    jwtService = module.get<JwtService>(JwtService);
    usersService = module.get<UsersService>(UsersService);
    rolesService = module.get<RolesService>(RolesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('register', () => {
    const registerDto = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'John',
      lastName: 'Doe',
    };

    it('should successfully register a new user', async () => {
      const defaultRole = { id: '1', name: 'user', permissions: [] };
      const hashedPassword = 'hashedPassword';
      const accessToken = 'accessToken';
      const refreshToken = 'refreshToken';

      mockUserRepository.findOne.mockResolvedValue(null);
      mockRolesService.findByName.mockResolvedValue(defaultRole);
      mockUserRepository.create.mockReturnValue({ ...mockUser, password: hashedPassword });
      mockUserRepository.save.mockResolvedValue({ ...mockUser, password: hashedPassword });
      mockJwtService.sign.mockReturnValue(accessToken);
      mockRefreshTokenRepository.create.mockReturnValue({});
      mockRefreshTokenRepository.save.mockResolvedValue({});

      jest.spyOn(bcrypt, 'hash').mockResolvedValue(hashedPassword as never);

      const result = await service.register(registerDto);

      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
        }),
        accessToken,
        refreshToken: expect.any(String),
        expiresIn: 24 * 60 * 60,
      });

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { email: registerDto.email },
      });
      expect(bcrypt.hash).toHaveBeenCalledWith(registerDto.password, 12);
      expect(mockUserRepository.save).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException if user already exists', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      await expect(service.register(registerDto)).rejects.toThrow(UnauthorizedException);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { email: registerDto.email },
      });
    });
  });

  describe('login', () => {
    const loginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should successfully login with valid credentials', async () => {
      const accessToken = 'accessToken';
      const refreshToken = 'refreshToken';

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.save.mockResolvedValue(mockUser);
      mockJwtService.sign.mockReturnValue(accessToken);
      mockRefreshTokenRepository.create.mockReturnValue({});
      mockRefreshTokenRepository.save.mockResolvedValue({});

      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);

      const result = await service.login(loginDto);

      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
        }),
        accessToken,
        refreshToken: expect.any(String),
        expiresIn: 24 * 60 * 60,
      });

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { email: loginDto.email },
        relations: ['roles', 'roles.permissions'],
      });
      expect(bcrypt.compare).toHaveBeenCalledWith(loginDto.password, mockUser.password);
    });

    it('should return null for invalid credentials', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false as never);

      const result = await service.login(loginDto);

      expect(result).toBeNull();
    });

    it('should return null for non-existent user', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const result = await service.login(loginDto);

      expect(result).toBeNull();
    });

    it('should return null for inactive user', async () => {
      const inactiveUser = { ...mockUser, isActive: false };
      mockUserRepository.findOne.mockResolvedValue(inactiveUser);

      const result = await service.login(loginDto);

      expect(result).toBeNull();
    });
  });

  describe('refreshToken', () => {
    const refreshTokenString = 'refreshToken123';

    it('should successfully refresh token with valid refresh token', async () => {
      const tokenEntity = {
        token: refreshTokenString,
        user: mockUser,
        expiresAt: new Date(Date.now() + 86400000), // 1 day from now
      };
      const newAccessToken = 'newAccessToken';

      mockRefreshTokenRepository.findOne.mockResolvedValue(tokenEntity);
      mockRefreshTokenRepository.remove.mockResolvedValue({});
      mockJwtService.sign.mockReturnValue(newAccessToken);
      mockRefreshTokenRepository.create.mockReturnValue({});
      mockRefreshTokenRepository.save.mockResolvedValue({});

      const result = await service.refreshToken(refreshTokenString);

      expect(result).toEqual({
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
        }),
        accessToken: newAccessToken,
        refreshToken: expect.any(String),
        expiresIn: 24 * 60 * 60,
      });

      expect(mockRefreshTokenRepository.findOne).toHaveBeenCalledWith({
        where: { token: refreshTokenString },
        relations: ['user', 'user.roles', 'user.roles.permissions'],
      });
      expect(mockRefreshTokenRepository.remove).toHaveBeenCalledWith(tokenEntity);
    });

    it('should return null for invalid refresh token', async () => {
      mockRefreshTokenRepository.findOne.mockResolvedValue(null);

      const result = await service.refreshToken(refreshTokenString);

      expect(result).toBeNull();
    });

    it('should return null for expired refresh token', async () => {
      const expiredTokenEntity = {
        token: refreshTokenString,
        user: mockUser,
        expiresAt: new Date(Date.now() - 86400000), // 1 day ago
      };

      mockRefreshTokenRepository.findOne.mockResolvedValue(expiredTokenEntity);

      const result = await service.refreshToken(refreshTokenString);

      expect(result).toBeNull();
    });
  });

  describe('logout', () => {
    it('should successfully logout user', async () => {
      const userId = '1';
      mockRefreshTokenRepository.delete.mockResolvedValue({});

      await service.logout(userId);

      expect(mockRefreshTokenRepository.delete).toHaveBeenCalledWith({
        user: { id: userId },
      });
    });
  });

  describe('changePassword', () => {
    const userId = '1';
    const currentPassword = 'currentPassword';
    const newPassword = 'newPassword';

    it('should successfully change password with valid current password', async () => {
      const hashedNewPassword = 'hashedNewPassword';

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockUserRepository.update.mockResolvedValue({});
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      jest.spyOn(bcrypt, 'hash').mockResolvedValue(hashedNewPassword as never);

      const result = await service.changePassword(userId, currentPassword, newPassword);

      expect(result).toBe(true);
      expect(bcrypt.compare).toHaveBeenCalledWith(currentPassword, mockUser.password);
      expect(bcrypt.hash).toHaveBeenCalledWith(newPassword, 12);
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, {
        password: hashedNewPassword,
      });
    });

    it('should return false for invalid current password', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false as never);

      const result = await service.changePassword(userId, currentPassword, newPassword);

      expect(result).toBe(false);
      expect(mockUserRepository.update).not.toHaveBeenCalled();
    });

    it('should return false for non-existent user', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const result = await service.changePassword(userId, currentPassword, newPassword);

      expect(result).toBe(false);
    });
  });

  describe('validateUser', () => {
    it('should return sanitized user for valid active user', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.validateUser(mockUser.id);

      expect(result).toEqual(
        expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
        })
      );
      expect(result.password).toBeUndefined();
    });

    it('should return null for inactive user', async () => {
      const inactiveUser = { ...mockUser, isActive: false };
      mockUserRepository.findOne.mockResolvedValue(inactiveUser);

      const result = await service.validateUser(mockUser.id);

      expect(result).toBeNull();
    });

    it('should return null for non-existent user', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const result = await service.validateUser(mockUser.id);

      expect(result).toBeNull();
    });
  });
});
