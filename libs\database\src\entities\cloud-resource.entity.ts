/**
 * CloudForge Platform - Cloud Resource Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { User } from './user.entity';

export enum ResourceType {
  VM = 'vm',
  STORAGE = 'storage',
  NETWORK = 'network',
  DATABASE = 'database',
  CONTAINER = 'container',
  FUNCTION = 'function',
}

export enum ResourceStatus {
  RUNNING = 'running',
  STOPPED = 'stopped',
  PENDING = 'pending',
  ERROR = 'error',
  TERMINATED = 'terminated',
}

@Entity('cloud_resources')
@Index(['userId'])
@Index(['type'])
@Index(['status'])
@Index(['region'])
@Index(['name'])
export class CloudResource extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'User who owns this resource',
  })
  userId: string;

  @Column({
    type: 'enum',
    enum: ResourceType,
    comment: 'Type of cloud resource',
  })
  type: ResourceType;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Resource name',
  })
  name: string;

  @Column({
    type: 'enum',
    enum: ResourceStatus,
    default: ResourceStatus.PENDING,
    comment: 'Current resource status',
  })
  status: ResourceStatus;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Cloud region',
  })
  region: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'External resource ID',
  })
  externalId?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Resource configuration',
  })
  configuration?: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Current resource usage metrics',
  })
  usage?: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Resource cost information',
  })
  cost?: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Resource metadata',
  })
  resourceMetadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  // Methods
  isRunning(): boolean {
    return this.status === ResourceStatus.RUNNING;
  }

  isStopped(): boolean {
    return this.status === ResourceStatus.STOPPED;
  }

  isError(): boolean {
    return this.status === ResourceStatus.ERROR;
  }

  start(): void {
    if (this.isStopped()) {
      this.status = ResourceStatus.PENDING;
    }
  }

  stop(): void {
    if (this.isRunning()) {
      this.status = ResourceStatus.PENDING;
    }
  }

  terminate(): void {
    this.status = ResourceStatus.TERMINATED;
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    obj.isRunning = this.isRunning();
    obj.isStopped = this.isStopped();
    obj.isError = this.isError();
    return obj;
  }
}
