/**
 * CloudForge Platform - HTTP Exception Filter
 * Enterprise-grade cloud services platform
 */

import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiResponse } from '@cloudforge/shared';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status: number;
    let message: string;
    let errors: string[] = [];

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const responseObj = exceptionResponse as any;
        message = responseObj.message || exception.message;
        
        if (Array.isArray(responseObj.message)) {
          errors = responseObj.message;
          message = 'Validation failed';
        } else if (responseObj.error) {
          message = responseObj.error;
        }
      } else {
        message = exception.message;
      }
    } else if (exception instanceof Error) {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = 'Internal server error';
      
      // Log the actual error for debugging
      this.logger.error(
        `Internal server error: ${exception.message}`,
        exception.stack,
        `${request.method} ${request.url}`
      );
    } else {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = 'Unknown error occurred';
      
      this.logger.error(
        `Unknown error: ${JSON.stringify(exception)}`,
        undefined,
        `${request.method} ${request.url}`
      );
    }

    // Create standardized error response
    const errorResponse: ApiResponse = {
      success: false,
      message,
      errors: errors.length > 0 ? errors : [message],
      timestamp: new Date().toISOString(),
      path: request.url,
      statusCode: status,
    };

    // Add stack trace in development
    if (process.env.NODE_ENV === 'development' && exception instanceof Error) {
      (errorResponse as any).stack = exception.stack;
    }

    // Log the error (except for client errors)
    if (status >= 500) {
      this.logger.error(
        `HTTP ${status} Error`,
        JSON.stringify({
          message,
          errors,
          path: request.url,
          method: request.method,
          userAgent: request.get('User-Agent'),
          ip: request.ip,
        }),
        exception instanceof Error ? exception.stack : undefined
      );
    } else if (status >= 400) {
      this.logger.warn(
        `HTTP ${status} Client Error`,
        JSON.stringify({
          message,
          path: request.url,
          method: request.method,
          ip: request.ip,
        })
      );
    }

    response.status(status).json(errorResponse);
  }
}
