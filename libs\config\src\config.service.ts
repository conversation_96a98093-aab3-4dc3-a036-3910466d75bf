import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { DatabaseConfig } from './interfaces/database-config.interface';
import { JwtConfig } from './interfaces/jwt-config.interface';
import { RedisConfig } from './interfaces/redis-config.interface';
import { EmailConfig } from './interfaces/email-config.interface';
import { AwsConfig } from './interfaces/aws-config.interface';
import { AppConfig } from './interfaces/app-config.interface';

@Injectable()
export class CloudForgeConfigService {
  constructor(private readonly configService: NestConfigService) {}

  get app(): AppConfig {
    return {
      name: this.configService.get<string>('APP_NAME', 'CloudForge Platform'),
      version: this.configService.get<string>('APP_VERSION', '1.0.0'),
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      port: this.configService.get<number>('PORT', 3000),
      globalPrefix: this.configService.get<string>('GLOBAL_PREFIX', 'api/v1'),
      corsOrigin: this.configService.get<string>('CORS_ORIGIN', '*'),
      timezone: this.configService.get<string>('TZ', 'UTC'),
    };
  }

  get database(): DatabaseConfig {
    return {
      type: 'postgres',
      host: this.configService.get<string>('DB_HOST', 'localhost'),
      port: this.configService.get<number>('DB_PORT', 5432),
      username: this.configService.get<string>('DB_USERNAME', 'postgres'),
      password: this.configService.get<string>('DB_PASSWORD', 'password'),
      database: this.configService.get<string>('DB_NAME', 'cloudforge'),
      synchronize: this.configService.get<boolean>('DB_SYNCHRONIZE', false),
      logging: this.configService.get<boolean>('DB_LOGGING', false),
      ssl: this.configService.get<boolean>('DB_SSL', false),
      maxConnections: this.configService.get<number>('DB_MAX_CONNECTIONS', 100),
      acquireTimeout: this.configService.get<number>('DB_ACQUIRE_TIMEOUT', 60000),
      timeout: this.configService.get<number>('DB_TIMEOUT', 60000),
    };
  }

  get jwt(): JwtConfig {
    return {
      secret: this.configService.get<string>('JWT_SECRET', 'cloudforge-secret-key'),
      expiresIn: this.configService.get<string>('JWT_EXPIRES_IN', '24h'),
      refreshExpiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d'),
      issuer: this.configService.get<string>('JWT_ISSUER', 'cloudforge-platform'),
      audience: this.configService.get<string>('JWT_AUDIENCE', 'cloudforge-users'),
      algorithm: this.configService.get<string>('JWT_ALGORITHM', 'HS256') as any,
    };
  }

  get redis(): RedisConfig {
    return {
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      database: this.configService.get<number>('REDIS_DATABASE', 0),
      keyPrefix: this.configService.get<string>('REDIS_KEY_PREFIX', 'cloudforge:'),
      ttl: this.configService.get<number>('REDIS_TTL', 3600),
      maxRetriesPerRequest: this.configService.get<number>('REDIS_MAX_RETRIES', 3),
      retryDelayOnFailover: this.configService.get<number>('REDIS_RETRY_DELAY', 100),
    };
  }

  get email(): EmailConfig {
    return {
      host: this.configService.get<string>('SMTP_HOST', 'localhost'),
      port: this.configService.get<number>('SMTP_PORT', 587),
      secure: this.configService.get<boolean>('SMTP_SECURE', false),
      username: this.configService.get<string>('SMTP_USERNAME'),
      password: this.configService.get<string>('SMTP_PASSWORD'),
      from: this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
      fromName: this.configService.get<string>('SMTP_FROM_NAME', 'CloudForge Platform'),
      replyTo: this.configService.get<string>('SMTP_REPLY_TO'),
      templateDir: this.configService.get<string>('EMAIL_TEMPLATE_DIR', './templates'),
    };
  }

  get aws(): AwsConfig {
    return {
      region: this.configService.get<string>('AWS_REGION', 'us-east-1'),
      accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY'),
      s3: {
        bucket: this.configService.get<string>('AWS_S3_BUCKET'),
        region: this.configService.get<string>('AWS_S3_REGION', 'us-east-1'),
        endpoint: this.configService.get<string>('AWS_S3_ENDPOINT'),
      },
      ses: {
        region: this.configService.get<string>('AWS_SES_REGION', 'us-east-1'),
        fromEmail: this.configService.get<string>('AWS_SES_FROM_EMAIL'),
      },
      sns: {
        region: this.configService.get<string>('AWS_SNS_REGION', 'us-east-1'),
        topicArn: this.configService.get<string>('AWS_SNS_TOPIC_ARN'),
      },
    };
  }

  get security() {
    return {
      bcryptRounds: this.configService.get<number>('BCRYPT_ROUNDS', 12),
      rateLimitTtl: this.configService.get<number>('RATE_LIMIT_TTL', 60),
      rateLimitMax: this.configService.get<number>('RATE_LIMIT_MAX', 100),
      sessionSecret: this.configService.get<string>('SESSION_SECRET', 'cloudforge-session-secret'),
      encryptionKey: this.configService.get<string>('ENCRYPTION_KEY'),
      allowedOrigins: this.configService.get<string>('ALLOWED_ORIGINS', '*').split(','),
    };
  }

  get monitoring() {
    return {
      enableMetrics: this.configService.get<boolean>('ENABLE_METRICS', true),
      enableTracing: this.configService.get<boolean>('ENABLE_TRACING', true),
      enableLogging: this.configService.get<boolean>('ENABLE_LOGGING', true),
      logLevel: this.configService.get<string>('LOG_LEVEL', 'info'),
      metricsPort: this.configService.get<number>('METRICS_PORT', 9090),
      tracingEndpoint: this.configService.get<string>('TRACING_ENDPOINT'),
      logFormat: this.configService.get<string>('LOG_FORMAT', 'json'),
    };
  }

  get payment() {
    return {
      stripe: {
        secretKey: this.configService.get<string>('STRIPE_SECRET_KEY'),
        publishableKey: this.configService.get<string>('STRIPE_PUBLISHABLE_KEY'),
        webhookSecret: this.configService.get<string>('STRIPE_WEBHOOK_SECRET'),
        apiVersion: this.configService.get<string>('STRIPE_API_VERSION', '2023-10-16'),
      },
      paypal: {
        clientId: this.configService.get<string>('PAYPAL_CLIENT_ID'),
        clientSecret: this.configService.get<string>('PAYPAL_CLIENT_SECRET'),
        environment: this.configService.get<string>('PAYPAL_ENVIRONMENT', 'sandbox'),
      },
    };
  }

  get features() {
    return {
      enableRegistration: this.configService.get<boolean>('ENABLE_REGISTRATION', true),
      enableEmailVerification: this.configService.get<boolean>('ENABLE_EMAIL_VERIFICATION', true),
      enableMfa: this.configService.get<boolean>('ENABLE_MFA', true),
      enableAuditLog: this.configService.get<boolean>('ENABLE_AUDIT_LOG', true),
      enableNotifications: this.configService.get<boolean>('ENABLE_NOTIFICATIONS', true),
      enableFileUpload: this.configService.get<boolean>('ENABLE_FILE_UPLOAD', true),
      maxFileSize: this.configService.get<number>('MAX_FILE_SIZE', 10485760), // 10MB
      allowedFileTypes: this.configService.get<string>('ALLOWED_FILE_TYPES', 'jpg,jpeg,png,pdf,doc,docx').split(','),
    };
  }

  // Helper methods
  isDevelopment(): boolean {
    return this.app.environment === 'development';
  }

  isProduction(): boolean {
    return this.app.environment === 'production';
  }

  isTest(): boolean {
    return this.app.environment === 'test';
  }

  get<T = any>(key: string, defaultValue?: T): T {
    return this.configService.get<T>(key, defaultValue);
  }
}
