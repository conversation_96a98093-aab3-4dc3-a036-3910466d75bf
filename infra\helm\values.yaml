# CloudForge Platform - Helm Values
# Enterprise-grade cloud services platform

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""
  environment: production
  version: "1.0.0"
  
# Image configuration
image:
  registry: docker.io
  repository: cloudforge
  tag: "1.0.0"
  pullPolicy: IfNotPresent
  pullSecrets: []

# API Gateway configuration
apiGateway:
  enabled: true
  replicaCount: 3
  image:
    repository: cloudforge/api-gateway
    tag: "1.0.0"
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  nodeSelector: {}
  tolerations: []
  affinity: {}

# Auth Service configuration
authService:
  enabled: true
  replicaCount: 2
  image:
    repository: cloudforge/auth-service
    tag: "1.0.0"
  service:
    type: ClusterIP
    port: 3001
    targetPort: 3001
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70

# User Service configuration
userService:
  enabled: true
  replicaCount: 2
  image:
    repository: cloudforge/user-service
    tag: "1.0.0"
  service:
    type: ClusterIP
    port: 3002
    targetPort: 3002
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70

# Billing Service configuration
billingService:
  enabled: true
  replicaCount: 2
  image:
    repository: cloudforge/billing-service
    tag: "1.0.0"
  service:
    type: ClusterIP
    port: 3003
    targetPort: 3003
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70

# Notification Service configuration
notificationService:
  enabled: true
  replicaCount: 2
  image:
    repository: cloudforge/notification-service
    tag: "1.0.0"
  service:
    type: ClusterIP
    port: 3004
    targetPort: 3004
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70

# Monitoring Service configuration
monitoringService:
  enabled: true
  replicaCount: 1
  image:
    repository: cloudforge/monitoring-service
    tag: "1.0.0"
  service:
    type: ClusterIP
    port: 3005
    targetPort: 3005
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi

# Admin Dashboard configuration
adminDashboard:
  enabled: true
  replicaCount: 2
  image:
    repository: cloudforge/admin-dashboard
    tag: "1.0.0"
  service:
    type: ClusterIP
    port: 3010
    targetPort: 3010
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: api.cloudforge.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: cloudforge-api-gateway
            port: 3000
    - host: admin.cloudforge.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: cloudforge-admin-dashboard
            port: 3010
  tls:
    - secretName: cloudforge-api-tls
      hosts:
        - api.cloudforge.com
    - secretName: cloudforge-admin-tls
      hosts:
        - admin.cloudforge.com

# PostgreSQL configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: "cloudforge_postgres_password"
    username: "cloudforge_app"
    password: "cloudforge_app_password"
    database: "cloudforge_platform"
  primary:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: ""
    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: true
    password: "cloudforge_redis_password"
  master:
    persistence:
      enabled: true
      size: 8Gi
      storageClass: ""
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Prometheus configuration
prometheus:
  enabled: true
  server:
    persistentVolume:
      enabled: true
      size: 50Gi
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
  alertmanager:
    enabled: true
    persistentVolume:
      enabled: true
      size: 2Gi

# Grafana configuration
grafana:
  enabled: true
  adminPassword: "cloudforge_grafana_password"
  persistence:
    enabled: true
    size: 10Gi
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 512Mi

# Service Monitor for Prometheus
serviceMonitor:
  enabled: true
  namespace: monitoring
  labels:
    app: cloudforge-platform

# Pod Disruption Budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Security Context
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  fsGroup: 1001

# Network Policy
networkPolicy:
  enabled: true
  ingress:
    enabled: true
  egress:
    enabled: true

# RBAC
rbac:
  create: true
  serviceAccountName: cloudforge-platform

# ConfigMap and Secrets
config:
  # Application configuration
  app:
    name: "CloudForge Platform"
    version: "1.0.0"
    environment: "production"
    logLevel: "info"
    
  # Database configuration
  database:
    host: "cloudforge-postgresql"
    port: 5432
    name: "cloudforge_platform"
    ssl: true
    poolSize: 10
    timeout: 30000
    
  # Redis configuration
  redis:
    host: "cloudforge-redis-master"
    port: 6379
    db: 0
    keyPrefix: "cloudforge:"
    
  # External services
  services:
    stripe:
      enabled: true
    sendgrid:
      enabled: true
    aws:
      enabled: true

# Secrets (these should be overridden in production)
secrets:
  jwtSecret: "your-jwt-secret-here"
  encryptionKey: "your-encryption-key-here"
  stripeSecretKey: "your-stripe-secret-key"
  sendgridApiKey: "your-sendgrid-api-key"
  awsAccessKeyId: "your-aws-access-key-id"
  awsSecretAccessKey: "your-aws-secret-access-key"
