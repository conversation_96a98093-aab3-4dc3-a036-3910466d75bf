# CloudForge Platform - Enterprise Kubernetes Deployment

## 🎯 Enterprise Kubernetes Architecture

### Production-Grade Cluster Configuration

```yaml
# enterprise-cluster-config.yml
apiVersion: v1
kind: Namespace
metadata:
  name: cloudforge-enterprise
  labels:
    security-level: "enterprise"
    compliance: "sox-pci-gdpr"
    environment: "production"
    cost-center: "platform-infrastructure"

---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: enterprise-resource-quota
  namespace: cloudforge-enterprise
spec:
  hard:
    requests.cpu: "100"
    requests.memory: "200Gi"
    limits.cpu: "200"
    limits.memory: "400Gi"
    persistentvolumeclaims: "50"
    services: "20"
    secrets: "100"
    configmaps: "100"

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: enterprise-network-policy
  namespace: cloudforge-enterprise
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: "cloudforge-enterprise"
    - namespaceSelector:
        matchLabels:
          name: "monitoring"
    - namespaceSelector:
        matchLabels:
          name: "ingress-nginx"
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: "cloudforge-enterprise"
  - to:
    - namespaceSelector:
        matchLabels:
          name: "kube-system"
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
```

### High-Availability Database Deployment

```yaml
# postgresql-ha-deployment.yml
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgresql-enterprise
  namespace: cloudforge-enterprise
spec:
  instances: 3
  
  postgresql:
    parameters:
      max_connections: "400"
      shared_buffers: "1GB"
      effective_cache_size: "3GB"
      maintenance_work_mem: "256MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"
      work_mem: "8MB"
      min_wal_size: "2GB"
      max_wal_size: "8GB"
      
  bootstrap:
    initdb:
      database: cloudforge_enterprise
      owner: cloudforge_app
      secret:
        name: postgresql-credentials
      
  storage:
    size: 500Gi
    storageClass: "enterprise-ssd"
    
  monitoring:
    enabled: true
    
  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://cloudforge-backups/postgresql"
      s3Credentials:
        accessKeyId:
          name: backup-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: backup-credentials
          key: SECRET_ACCESS_KEY
      wal:
        retention: "7d"
      data:
        retention: "30d"
        
  resources:
    requests:
      memory: "4Gi"
      cpu: "2"
    limits:
      memory: "8Gi"
      cpu: "4"
      
  affinity:
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
          - key: cnpg.io/cluster
            operator: In
            values:
            - postgresql-enterprise
        topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-credentials
  namespace: cloudforge-enterprise
type: Opaque
data:
  username: Y2xvdWRmb3JnZV9hcHA=  # cloudforge_app
  password: <base64-encoded-secure-password>
```

### Redis Cluster for Enterprise Caching

```yaml
# redis-enterprise-cluster.yml
apiVersion: redis.redis.opstreelabs.in/v1beta1
kind: RedisCluster
metadata:
  name: redis-enterprise
  namespace: cloudforge-enterprise
spec:
  clusterSize: 6
  
  redisExporter:
    enabled: true
    image: oliver006/redis_exporter:latest
    
  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: "enterprise-ssd"
        resources:
          requests:
            storage: 100Gi
            
  resources:
    requests:
      cpu: "500m"
      memory: "2Gi"
    limits:
      cpu: "1"
      memory: "4Gi"
      
  securityContext:
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
    
  affinity:
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app: redis-enterprise
        topologyKey: kubernetes.io/hostname
        
  tolerations:
  - key: "redis-dedicated"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
```

### API Gateway Enterprise Deployment

```yaml
# api-gateway-enterprise.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway-enterprise
  namespace: cloudforge-enterprise
  labels:
    app: api-gateway
    tier: frontend
    version: enterprise
spec:
  replicas: 10
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 3
      maxUnavailable: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
        tier: frontend
        version: enterprise
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: api-gateway
        image: cloudforge/api-gateway:enterprise-v1.0.0
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: CLUSTER_MODE
          value: "enterprise"
        - name: DATABASE_HOST
          value: "postgresql-enterprise-rw"
        - name: DATABASE_PORT
          value: "5432"
        - name: DATABASE_NAME
          value: "cloudforge_enterprise"
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: password
        - name: REDIS_HOST
          value: "redis-enterprise"
        - name: REDIS_PORT
          value: "6379"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secrets
              key: secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: encryption-keys
              key: primary
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: tls-certs
          mountPath: /app/certs
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: api-gateway-config
      - name: tls-certs
        secret:
          secretName: api-gateway-tls
      - name: logs
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - api-gateway
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "api-dedicated"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
  namespace: cloudforge-enterprise
  labels:
    app: api-gateway
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
spec:
  type: LoadBalancer
  selector:
    app: api-gateway
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  - name: https
    port: 443
    targetPort: 3000
    protocol: TCP
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: cloudforge-enterprise
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway-enterprise
  minReplicas: 10
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: requests_per_second
      target:
        type: AverageValue
        averageValue: "1000"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 5
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

### Enterprise Ingress Configuration

```yaml
# enterprise-ingress.yml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cloudforge-enterprise-ingress
  namespace: cloudforge-enterprise
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "enterprise-ca-issuer"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains";
      more_set_headers "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;";
spec:
  tls:
  - hosts:
    - api.enterprise.cloudforge.com
    - admin.enterprise.cloudforge.com
    secretName: cloudforge-enterprise-tls
  rules:
  - host: api.enterprise.cloudforge.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 80
  - host: admin.enterprise.cloudforge.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: admin-dashboard-service
            port:
              number: 80
```

### Enterprise Monitoring Stack

```yaml
# enterprise-monitoring.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-enterprise-config
  namespace: cloudforge-enterprise
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'cloudforge-enterprise'
        environment: 'production'
    
    rule_files:
    - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          - alertmanager:9093
    
    scrape_configs:
    - job_name: 'kubernetes-apiservers'
      kubernetes_sd_configs:
      - role: endpoints
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https
    
    - job_name: 'cloudforge-services'
      kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
          - cloudforge-enterprise
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus-enterprise
  namespace: cloudforge-enterprise
spec:
  replicas: 2
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.40.0
        args:
        - '--config.file=/etc/prometheus/prometheus.yml'
        - '--storage.tsdb.path=/prometheus/'
        - '--web.console.libraries=/etc/prometheus/console_libraries'
        - '--web.console.templates=/etc/prometheus/consoles'
        - '--storage.tsdb.retention.time=30d'
        - '--web.enable-lifecycle'
        - '--web.enable-admin-api'
        - '--storage.tsdb.wal-compression'
        ports:
        - containerPort: 9090
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        - name: prometheus-storage
          mountPath: /prometheus/
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-enterprise-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-storage-pvc
```

This enterprise Kubernetes deployment provides:

1. **High Availability**: Multi-replica deployments with anti-affinity rules
2. **Security**: Network policies, security contexts, and RBAC
3. **Scalability**: Horizontal pod autoscaling and resource quotas
4. **Monitoring**: Comprehensive Prometheus monitoring setup
5. **Data Persistence**: Enterprise-grade storage with backup strategies
6. **Load Balancing**: Enterprise load balancer integration
7. **SSL/TLS**: Automated certificate management
8. **Compliance**: Security headers and audit logging

The configuration is designed for enterprise environments requiring €60M value proposition with bank-grade security and reliability.
