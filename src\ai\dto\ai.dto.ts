import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsO<PERSON>al, IsNumber, Min, Max } from 'class-validator';
import { AIType } from '@prisma/client';

export class ProcessAIRequestDto {
  @ApiProperty({
    description: 'The prompt or input for AI processing',
    example: 'Generate a comprehensive business plan for a tech startup',
  })
  @IsString()
  prompt: string;

  @ApiProperty({
    description: 'Type of AI processing required',
    enum: AIType,
    example: AIType.TEXT_GENERATION,
  })
  @IsEnum(AIType)
  type: AIType;

  @ApiProperty({
    description: 'AI model to use',
    example: 'gpt-4',
    required: false,
  })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiProperty({
    description: 'Maximum number of tokens to generate',
    example: 2000,
    required: false,
    minimum: 1,
    maximum: 4000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4000)
  maxTokens?: number;

  @ApiProperty({
    description: 'Temperature for response creativity (0.0 to 1.0)',
    example: 0.7,
    required: false,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  temperature?: number;
}

export class SolveProblemDto {
  @ApiProperty({
    description: 'Complex problem to solve',
    example: 'How can we achieve sustainable energy for the entire planet while maintaining economic growth?',
  })
  @IsString()
  problem: string;
}

export class GenerateCodeDto {
  @ApiProperty({
    description: 'Code requirements and specifications',
    example: 'Create a REST API for user authentication with JWT tokens, password hashing, and rate limiting',
  })
  @IsString()
  requirements: string;

  @ApiProperty({
    description: 'Programming language',
    example: 'TypeScript',
  })
  @IsString()
  language: string;
}

export class AnalyzeDataDto {
  @ApiProperty({
    description: 'Data to analyze (can be JSON, CSV, or text format)',
    example: '{"sales": [100, 150, 200, 180, 220], "months": ["Jan", "Feb", "Mar", "Apr", "May"]}',
  })
  @IsString()
  data: string;
}

export class CreateContentDto {
  @ApiProperty({
    description: 'Topic for content creation',
    example: 'The future of artificial intelligence in healthcare',
  })
  @IsString()
  topic: string;

  @ApiProperty({
    description: 'Type of content to create',
    example: 'blog post',
  })
  @IsString()
  contentType: string;
}
