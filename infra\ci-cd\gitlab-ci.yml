# CloudForge Platform - GitLab CI/CD Pipeline
# Enterprise-grade CI/CD configuration for production deployment

stages:
  - validate
  - security-scan
  - test
  - build
  - security-test
  - deploy-staging
  - integration-test
  - deploy-production
  - post-deploy

variables:
  DOCKER_REGISTRY: "cloudforge.azurecr.io"
  KUBERNETES_NAMESPACE_STAGING: "cloudforge-staging"
  KUBERNETES_NAMESPACE_PRODUCTION: "cloudforge-production"
  HELM_CHART_VERSION: "1.0.0"
  SONAR_PROJECT_KEY: "cloudforge-platform"
  
# Global before_script
before_script:
  - echo "Starting CloudForge Platform CI/CD Pipeline"
  - export COMMIT_SHA=${CI_COMMIT_SHA:0:8}
  - export IMAGE_TAG="${CI_COMMIT_REF_NAME}-${COMMIT_SHA}"

# ==================== VALIDATION STAGE ====================

code-quality:
  stage: validate
  image: node:18-alpine
  script:
    - npm ci
    - npm run lint
    - npm run format:check
    - npm run type-check
  artifacts:
    reports:
      junit: reports/lint-results.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

dependency-check:
  stage: validate
  image: node:18-alpine
  script:
    - npm ci
    - npm audit --audit-level=high
    - npx license-checker --onlyAllow 'MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC'
  artifacts:
    reports:
      dependency_scanning: gl-dependency-scanning-report.json
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

terraform-validate:
  stage: validate
  image: hashicorp/terraform:1.6
  script:
    - cd infra/terraform/aws
    - terraform init -backend=false
    - terraform validate
    - terraform fmt -check
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - changes:
        - infra/terraform/**/*

# ==================== SECURITY SCAN STAGE ====================

sast-scan:
  stage: security-scan
  image: securecodewarrior/gitlab-sast:latest
  script:
    - /analyzer run
  artifacts:
    reports:
      sast: gl-sast-report.json
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

secret-detection:
  stage: security-scan
  image: registry.gitlab.com/gitlab-org/security-products/analyzers/secrets:latest
  script:
    - /analyzer run
  artifacts:
    reports:
      secret_detection: gl-secret-detection-report.json
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"

sonarqube-check:
  stage: security-scan
  image: sonarsource/sonar-scanner-cli:latest
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
      -Dsonar.projectKey=$SONAR_PROJECT_KEY
      -Dsonar.sources=.
      -Dsonar.host.url=$SONAR_HOST_URL
      -Dsonar.login=$SONAR_TOKEN
      -Dsonar.qualitygate.wait=true
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

# ==================== TEST STAGE ====================

unit-tests:
  stage: test
  image: node:18-alpine
  services:
    - postgres:15-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: cloudforge_test
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    DATABASE_URL: "**************************************************/cloudforge_test"
    REDIS_URL: "redis://redis:6379"
  script:
    - npm ci
    - npm run test:unit
    - npm run test:coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      junit: reports/unit-test-results.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

integration-tests:
  stage: test
  image: node:18-alpine
  services:
    - postgres:15-alpine
    - redis:7-alpine
  variables:
    POSTGRES_DB: cloudforge_test
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    DATABASE_URL: "**************************************************/cloudforge_test"
    REDIS_URL: "redis://redis:6379"
  script:
    - npm ci
    - npm run test:integration
  artifacts:
    reports:
      junit: reports/integration-test-results.xml
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

# ==================== BUILD STAGE ====================

build-images:
  stage: build
  image: docker:24-dind
  services:
    - docker:24-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - echo $DOCKER_REGISTRY_PASSWORD | docker login $DOCKER_REGISTRY -u $DOCKER_REGISTRY_USER --password-stdin
  script:
    - |
      # Build all microservice images
      services=("api-gateway" "auth-service" "user-service" "billing-service" "notification-service" "monitoring-service" "admin-dashboard")
      
      for service in "${services[@]}"; do
        echo "Building $service..."
        docker build -t $DOCKER_REGISTRY/cloudforge/$service:$IMAGE_TAG -f apps/$service/Dockerfile .
        docker push $DOCKER_REGISTRY/cloudforge/$service:$IMAGE_TAG
        
        # Tag as latest for main branch
        if [ "$CI_COMMIT_BRANCH" == "main" ]; then
          docker tag $DOCKER_REGISTRY/cloudforge/$service:$IMAGE_TAG $DOCKER_REGISTRY/cloudforge/$service:latest
          docker push $DOCKER_REGISTRY/cloudforge/$service:latest
        fi
      done
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

# ==================== SECURITY TEST STAGE ====================

container-scanning:
  stage: security-test
  image: registry.gitlab.com/gitlab-org/security-products/analyzers/container-scanning:latest
  variables:
    CS_IMAGE: $DOCKER_REGISTRY/cloudforge/api-gateway:$IMAGE_TAG
  script:
    - /analyzer run
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
  dependencies:
    - build-images
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"

# ==================== DEPLOY STAGING STAGE ====================

deploy-staging:
  stage: deploy-staging
  image: alpine/helm:3.12.0
  environment:
    name: staging
    url: https://staging.cloudforge.com
  before_script:
    - apk add --no-cache curl
    - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    - chmod +x kubectl && mv kubectl /usr/local/bin/
    - echo $KUBE_CONFIG | base64 -d > ~/.kube/config
  script:
    - |
      # Update Helm values with new image tags
      helm upgrade --install cloudforge-staging ./infra/helm/cloudforge-platform \
        --namespace $KUBERNETES_NAMESPACE_STAGING \
        --create-namespace \
        --values ./infra/helm/cloudforge-platform/values-staging.yaml \
        --set global.imageTag=$IMAGE_TAG \
        --set apiGateway.image.tag=$IMAGE_TAG \
        --set authService.image.tag=$IMAGE_TAG \
        --set userService.image.tag=$IMAGE_TAG \
        --set billingService.image.tag=$IMAGE_TAG \
        --set notificationService.image.tag=$IMAGE_TAG \
        --set monitoringService.image.tag=$IMAGE_TAG \
        --set adminDashboard.image.tag=$IMAGE_TAG \
        --wait --timeout=10m
      
      # Verify deployment
      kubectl get pods -n $KUBERNETES_NAMESPACE_STAGING
      kubectl rollout status deployment/api-gateway -n $KUBERNETES_NAMESPACE_STAGING
  dependencies:
    - build-images
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "main"

# ==================== INTEGRATION TEST STAGE ====================

e2e-tests:
  stage: integration-test
  image: cypress/included:12.17.0
  variables:
    CYPRESS_BASE_URL: "https://staging.cloudforge.com"
  script:
    - npm ci
    - npm run test:e2e
  artifacts:
    when: always
    paths:
      - cypress/videos/
      - cypress/screenshots/
    reports:
      junit: cypress/results/test-results.xml
  dependencies:
    - deploy-staging
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "main"

load-testing:
  stage: integration-test
  image: loadimpact/k6:latest
  script:
    - k6 run --out json=load-test-results.json tests/load/load-test.js
  artifacts:
    reports:
      performance: load-test-results.json
  dependencies:
    - deploy-staging
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# ==================== DEPLOY PRODUCTION STAGE ====================

deploy-production:
  stage: deploy-production
  image: alpine/helm:3.12.0
  environment:
    name: production
    url: https://cloudforge.com
  before_script:
    - apk add --no-cache curl
    - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    - chmod +x kubectl && mv kubectl /usr/local/bin/
    - echo $KUBE_CONFIG_PROD | base64 -d > ~/.kube/config
  script:
    - |
      # Production deployment with blue-green strategy
      helm upgrade --install cloudforge-production ./infra/helm/cloudforge-platform \
        --namespace $KUBERNETES_NAMESPACE_PRODUCTION \
        --create-namespace \
        --values ./infra/helm/cloudforge-platform/values-production.yaml \
        --set global.imageTag=$IMAGE_TAG \
        --set apiGateway.image.tag=$IMAGE_TAG \
        --set authService.image.tag=$IMAGE_TAG \
        --set userService.image.tag=$IMAGE_TAG \
        --set billingService.image.tag=$IMAGE_TAG \
        --set notificationService.image.tag=$IMAGE_TAG \
        --set monitoringService.image.tag=$IMAGE_TAG \
        --set adminDashboard.image.tag=$IMAGE_TAG \
        --wait --timeout=15m
      
      # Verify production deployment
      kubectl get pods -n $KUBERNETES_NAMESPACE_PRODUCTION
      kubectl rollout status deployment/api-gateway -n $KUBERNETES_NAMESPACE_PRODUCTION
      
      # Run smoke tests
      curl -f https://api.cloudforge.com/health || exit 1
  dependencies:
    - e2e-tests
    - load-testing
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
      allow_failure: false

# ==================== POST-DEPLOY STAGE ====================

production-smoke-tests:
  stage: post-deploy
  image: curlimages/curl:latest
  script:
    - |
      # Health check endpoints
      curl -f https://api.cloudforge.com/health
      curl -f https://admin.cloudforge.com/health
      
      # Basic API functionality
      response=$(curl -s -o /dev/null -w "%{http_code}" https://api.cloudforge.com/v1/status)
      if [ $response -ne 200 ]; then
        echo "API health check failed with status $response"
        exit 1
      fi
      
      echo "Production smoke tests passed"
  dependencies:
    - deploy-production
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

notify-deployment:
  stage: post-deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      # Notify Slack about successful deployment
      curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚀 CloudForge Platform successfully deployed to production!\n\nCommit: $CI_COMMIT_SHA\nBranch: $CI_COMMIT_BRANCH\nPipeline: $CI_PIPELINE_URL\"}" \
        $SLACK_WEBHOOK_URL
      
      # Update deployment status in monitoring
      curl -X POST -H 'Content-Type: application/json' \
        -d "{\"deployment_id\":\"$CI_PIPELINE_ID\",\"status\":\"success\",\"environment\":\"production\",\"version\":\"$IMAGE_TAG\"}" \
        https://monitoring.cloudforge.com/api/deployments
  dependencies:
    - production-smoke-tests
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: on_success

# ==================== CLEANUP ====================

cleanup-staging:
  stage: post-deploy
  image: alpine/helm:3.12.0
  before_script:
    - apk add --no-cache curl
    - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    - chmod +x kubectl && mv kubectl /usr/local/bin/
    - echo $KUBE_CONFIG | base64 -d > ~/.kube/config
  script:
    - |
      # Keep only last 3 releases in staging
      helm history cloudforge-staging -n $KUBERNETES_NAMESPACE_STAGING --max 10 | tail -n +4 | awk '{print $1}' | while read revision; do
        helm delete cloudforge-staging --revision $revision -n $KUBERNETES_NAMESPACE_STAGING || true
      done
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: on_success
  allow_failure: true
