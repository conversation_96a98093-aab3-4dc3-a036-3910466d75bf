/**
 * CloudForge Platform - API Gateway
 * Enterprise-grade cloud services platform
 * 
 * Main entry point for the API Gateway service
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { RateLimitGuard } from './common/guards/rate-limit.guard';

async function bootstrap(): Promise<void> {
  const logger = new Logger('Bootstrap');
  
  try {
    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Get configuration service
    const configService = app.get(ConfigService);
    const port = configService.get<number>('API_PORT', 3000);
    const environment = configService.get<string>('NODE_ENV', 'development');
    const apiPrefix = configService.get<string>('API_PREFIX', 'api/v1');

    // Security middleware
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    }));

    // Compression middleware
    app.use(compression());

    // CORS configuration
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', 'http://localhost:3001'),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    // Global prefix
    app.setGlobalPrefix(apiPrefix);

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
        validationError: {
          target: false,
          value: false,
        },
      })
    );

    // Global filters
    app.useGlobalFilters(new HttpExceptionFilter());

    // Global interceptors
    app.useGlobalInterceptors(
      new LoggingInterceptor(),
      new TransformInterceptor()
    );

    // Global guards
    app.useGlobalGuards(new RateLimitGuard());

    // Swagger documentation setup
    if (environment !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('CloudForge Platform API')
        .setDescription('Enterprise-grade cloud services platform API documentation')
        .setVersion('1.0.0')
        .setContact(
          'CloudForge Engineering Team',
          'https://cloudforge.com',
          '<EMAIL>'
        )
        .setLicense('MIT', 'https://opensource.org/licenses/MIT')
        .addServer(`http://localhost:${port}`, 'Development server')
        .addServer('https://api.cloudforge.com', 'Production server')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
          },
          'JWT-auth'
        )
        .addOAuth2(
          {
            type: 'oauth2',
            flows: {
              authorizationCode: {
                authorizationUrl: '/auth/oauth/google',
                tokenUrl: '/auth/oauth/google/callback',
                scopes: {
                  'read:profile': 'Read user profile',
                  'write:profile': 'Write user profile',
                },
              },
            },
          },
          'OAuth2'
        )
        .addTag('Authentication', 'User authentication and authorization')
        .addTag('Users', 'User management operations')
        .addTag('Billing', 'Subscription and payment management')
        .addTag('Notifications', 'Notification and messaging services')
        .addTag('Monitoring', 'System monitoring and health checks')
        .addTag('Admin', 'Administrative operations')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          displayRequestDuration: true,
          docExpansion: 'none',
          filter: true,
          showRequestHeaders: true,
          tryItOutEnabled: true,
        },
        customSiteTitle: 'CloudForge Platform API Documentation',
        customfavIcon: '/favicon.ico',
        customCss: `
          .swagger-ui .topbar { display: none; }
          .swagger-ui .info .title { color: #1976d2; }
        `,
      });

      logger.log(`📚 Swagger documentation available at: http://localhost:${port}/api/docs`);
    }

    // Health check endpoint
    app.use('/health', (req, res) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment,
        service: 'api-gateway',
        dependencies: {
          database: 'healthy', // This would be checked in a real implementation
          redis: 'healthy',
          external: 'healthy',
        },
      });
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.log('SIGTERM received, shutting down gracefully...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('SIGINT received, shutting down gracefully...');
      await app.close();
      process.exit(0);
    });

    // Start the server
    await app.listen(port, '0.0.0.0');
    
    logger.log(`🚀 CloudForge Platform API Gateway is running on: http://localhost:${port}`);
    logger.log(`🌍 Environment: ${environment}`);
    logger.log(`📡 API Prefix: /${apiPrefix}`);
    logger.log(`🔒 Security: Helmet, CORS, Rate Limiting enabled`);
    logger.log(`📊 Monitoring: Health checks, Logging, Metrics enabled`);
    
    if (environment !== 'production') {
      logger.log(`📖 API Documentation: http://localhost:${port}/api/docs`);
    }

  } catch (error) {
    logger.error('Failed to start the application', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  const logger = new Logger('UnhandledRejection');
  logger.error('Unhandled Promise Rejection:', reason);
  // In production, you might want to exit the process
  // process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  const logger = new Logger('UncaughtException');
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

bootstrap();
