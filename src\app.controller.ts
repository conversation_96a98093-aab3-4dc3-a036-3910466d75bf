import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';

@ApiTags('app')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({
    summary: 'Get application info',
    description: 'Returns basic information about CloudForge Platform',
  })
  @ApiResponse({
    status: 200,
    description: 'Application information retrieved successfully',
    schema: {
      example: {
        success: true,
        data: {
          name: 'CloudForge Platform',
          version: '1.0.0',
          description: 'Transcendent Excellence at €0.001/user/month',
          creator: '<PERSON><PERSON>',
          status: 'operational',
          uptime: '99.999%',
          costPerUser: '€0.001/month',
        },
        message: 'Welcome to CloudForge Platform',
      },
    },
  })
  getHello() {
    return this.appService.getAppInfo();
  }

  @Get('status')
  @ApiOperation({
    summary: 'Get system status',
    description: 'Returns current system status and health information',
  })
  @ApiResponse({
    status: 200,
    description: 'System status retrieved successfully',
  })
  getStatus() {
    return this.appService.getSystemStatus();
  }
}
