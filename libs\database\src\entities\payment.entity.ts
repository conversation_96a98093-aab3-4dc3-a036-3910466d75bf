/**
 * CloudForge Platform - Payment Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { User } from './user.entity';
import { Invoice } from './invoice.entity';

export enum PaymentStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled',
}

export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  PAYPAL = 'paypal',
  STRIPE = 'stripe',
}

@Entity('payments')
@Index(['userId'])
@Index(['invoiceId'])
@Index(['status'])
@Index(['paymentMethod'])
@Index(['externalId'])
export class Payment extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'User who made this payment',
  })
  userId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Related invoice',
  })
  invoiceId?: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: 'Payment amount',
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 3,
    default: 'USD',
    comment: 'Currency code',
  })
  currency: string;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
    comment: 'Payment status',
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
    comment: 'Payment method used',
  })
  paymentMethod: PaymentMethod;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'External payment ID',
  })
  externalId?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Transaction reference',
  })
  transactionId?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When payment was processed',
  })
  processedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Failure reason if payment failed',
  })
  failureReason?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Payment metadata',
  })
  paymentMetadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Invoice)
  @JoinColumn({ name: 'invoiceId' })
  invoice?: Invoice;

  // Methods
  isCompleted(): boolean {
    return this.status === PaymentStatus.COMPLETED;
  }

  isFailed(): boolean {
    return this.status === PaymentStatus.FAILED;
  }

  markAsCompleted(): void {
    this.status = PaymentStatus.COMPLETED;
    this.processedAt = new Date();
  }

  markAsFailed(reason: string): void {
    this.status = PaymentStatus.FAILED;
    this.failureReason = reason;
    this.processedAt = new Date();
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    obj.isCompleted = this.isCompleted();
    obj.isFailed = this.isFailed();
    return obj;
  }
}
