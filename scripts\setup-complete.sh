#!/bin/bash

# CloudForge Platform - Complete Setup Script
# Sets up the entire platform for development and demo
# Created by <PERSON><PERSON>

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Banner
echo -e "${PURPLE}"
cat << "EOF"
   _____ _                 _ ______                    
  / ____| |               | |  ____|                   
 | |    | | ___  _   _  __| | |__ ___  _ __ __ _  ___  
 | |    | |/ _ \| | | |/ _` |  __/ _ \| '__/ _` |/ _ \ 
 | |____| | (_) | |_| | (_| | | | (_) | | | (_| |  __/ 
  \_____|_|\___/ \__,_|\__,_|_|  \___/|_|  \__, |\___| 
                                           __/ |      
                                          |___/       
EOF
echo -e "${NC}"
echo -e "${CYAN}🚀 CloudForge Platform - Complete Setup${NC}"
echo -e "${CYAN}👨‍💻 Created by Marwan El-Qaouti${NC}"
echo ""

# Check if running from project root
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Please run this script from the project root directory${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Starting CloudForge Platform setup...${NC}"
echo ""

# Step 1: Install dependencies
echo -e "${YELLOW}📦 Step 1: Installing dependencies...${NC}"
echo "Installing root dependencies..."
npm install

echo "Installing backend dependencies..."
npm install

echo "Installing frontend dependencies..."
cd apps/admin-dashboard
npm install
cd ../..

echo "Installing quantum-core dependencies..."
cd apps/quantum-core
npm install
cd ../..

echo -e "${GREEN}✅ Dependencies installed${NC}"
echo ""

# Step 2: Setup environment variables
echo -e "${YELLOW}⚙️ Step 2: Setting up environment variables...${NC}"

# Backend environment
if [ ! -f ".env" ]; then
    echo "Creating backend .env file..."
    cp .env.example .env
    echo -e "${GREEN}✅ Created backend .env file${NC}"
else
    echo -e "${YELLOW}⚠️ Backend .env file already exists${NC}"
fi

# Frontend environment
if [ ! -f "apps/admin-dashboard/.env.local" ]; then
    echo "Creating frontend .env.local file..."
    cp apps/admin-dashboard/.env.example apps/admin-dashboard/.env.local
    echo -e "${GREEN}✅ Created frontend .env.local file${NC}"
else
    echo -e "${YELLOW}⚠️ Frontend .env.local file already exists${NC}"
fi

echo ""

# Step 3: Setup database
echo -e "${YELLOW}🗄️ Step 3: Setting up database...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Start database services
echo "Starting database services..."
docker-compose up -d postgres redis

# Wait for database to be ready
echo "Waiting for database to be ready..."
sleep 10

# Generate Prisma client
echo "Generating Prisma client..."
npx prisma generate

# Run database migrations
echo "Running database migrations..."
npx prisma migrate dev --name init

# Seed database
echo "Seeding database with demo data..."
npx prisma db seed

echo -e "${GREEN}✅ Database setup completed${NC}"
echo ""

# Step 4: Build applications
echo -e "${YELLOW}🔨 Step 4: Building applications...${NC}"

echo "Building backend..."
npm run build

echo "Building frontend..."
cd apps/admin-dashboard
npm run build
cd ../..

echo -e "${GREEN}✅ Applications built successfully${NC}"
echo ""

# Step 5: Setup monitoring
echo -e "${YELLOW}📊 Step 5: Setting up monitoring...${NC}"

# Start monitoring services
echo "Starting monitoring services..."
docker-compose up -d prometheus grafana

echo -e "${GREEN}✅ Monitoring services started${NC}"
echo ""

# Step 6: Run tests
echo -e "${YELLOW}🧪 Step 6: Running tests...${NC}"

echo "Running backend tests..."
npm run test

echo "Running frontend tests..."
cd apps/admin-dashboard
npm run test -- --run
cd ../..

echo -e "${GREEN}✅ Tests completed${NC}"
echo ""

# Step 7: Start development servers
echo -e "${YELLOW}🚀 Step 7: Starting development servers...${NC}"

# Start backend in background
echo "Starting backend server..."
npm run start:dev &
BACKEND_PID=$!

# Wait for backend to start
sleep 5

# Start frontend in background
echo "Starting frontend server..."
cd apps/admin-dashboard
npm run dev &
FRONTEND_PID=$!
cd ../..

# Wait for servers to start
sleep 10

echo -e "${GREEN}✅ Development servers started${NC}"
echo ""

# Final summary
echo -e "${PURPLE}🎉 CloudForge Platform Setup Complete!${NC}"
echo ""
echo -e "${CYAN}📊 SETUP SUMMARY:${NC}"
echo -e "   ✅ Dependencies installed"
echo -e "   ✅ Environment variables configured"
echo -e "   ✅ Database setup and seeded"
echo -e "   ✅ Applications built"
echo -e "   ✅ Monitoring services started"
echo -e "   ✅ Tests passed"
echo -e "   ✅ Development servers running"
echo ""

echo -e "${CYAN}🌐 ACCESS URLS:${NC}"
echo -e "   🖥️  Frontend:     http://localhost:3000"
echo -e "   🔧 Backend API:   http://localhost:3001"
echo -e "   📚 API Docs:      http://localhost:3001/api/docs"
echo -e "   📊 Grafana:       http://localhost:3001 (admin/cloudforge_grafana_admin)"
echo -e "   🔍 Prometheus:    http://localhost:9090"
echo ""

echo -e "${CYAN}🔑 LOGIN CREDENTIALS:${NC}"
echo -e "   👑 Admin:    <EMAIL> / CloudForge2024!"
echo -e "   👔 Manager:  <EMAIL> / Demo2024!"
echo -e "   👤 User:     <EMAIL> / Demo2024!"
echo -e "   👁️  Viewer:   <EMAIL> / Demo2024!"
echo ""

echo -e "${CYAN}🛠️ USEFUL COMMANDS:${NC}"
echo -e "   📊 View logs:        docker-compose logs -f"
echo -e "   🗄️ Database studio:  npx prisma studio"
echo -e "   🔄 Restart services: docker-compose restart"
echo -e "   🛑 Stop all:         docker-compose down"
echo ""

echo -e "${YELLOW}⚠️ IMPORTANT NOTES:${NC}"
echo -e "   • Backend running on PID: $BACKEND_PID"
echo -e "   • Frontend running on PID: $FRONTEND_PID"
echo -e "   • Use 'kill $BACKEND_PID $FRONTEND_PID' to stop servers"
echo -e "   • Check .env files for configuration"
echo -e "   • Database is seeded with demo data"
echo ""

echo -e "${GREEN}🏆 CloudForge Platform is ready for development and demo!${NC}"
echo -e "${PURPLE}💎 Created by Marwan El-Qaouti - The Ultimate Achievement${NC}"
echo ""

# Keep script running to show final status
echo -e "${BLUE}ℹ️ Press Ctrl+C to stop all services and exit${NC}"

# Trap Ctrl+C to cleanup
trap 'echo -e "\n${YELLOW}🛑 Stopping services...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose down; echo -e "${GREEN}✅ All services stopped${NC}"; exit 0' INT

# Wait for user to stop
wait
