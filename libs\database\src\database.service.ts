/**
 * CloudForge Platform - Database Service
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';

@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  async onModuleInit(): Promise<void> {
    try {
      if (!this.dataSource.isInitialized) {
        await this.dataSource.initialize();
      }
      
      this.logger.log('Database connection established successfully');
      
      // Log connection info
      this.logger.log(`Connected to: ${this.dataSource.options.type}://${this.dataSource.options.host}:${this.dataSource.options.port}/${this.dataSource.options.database}`);
      
    } catch (error) {
      this.logger.error('Failed to connect to database', error);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      if (this.dataSource.isInitialized) {
        await this.dataSource.destroy();
        this.logger.log('Database connection closed');
      }
    } catch (error) {
      this.logger.error('Error closing database connection', error);
    }
  }

  /**
   * Get the data source instance
   */
  getDataSource(): DataSource {
    return this.dataSource;
  }

  /**
   * Check if database is connected
   */
  isConnected(): boolean {
    return this.dataSource.isInitialized;
  }

  /**
   * Run database migrations
   */
  async runMigrations(): Promise<void> {
    try {
      this.logger.log('Running database migrations...');
      await this.dataSource.runMigrations();
      this.logger.log('Database migrations completed successfully');
    } catch (error) {
      this.logger.error('Failed to run database migrations', error);
      throw error;
    }
  }

  /**
   * Revert last migration
   */
  async revertLastMigration(): Promise<void> {
    try {
      this.logger.log('Reverting last migration...');
      await this.dataSource.undoLastMigration();
      this.logger.log('Last migration reverted successfully');
    } catch (error) {
      this.logger.error('Failed to revert last migration', error);
      throw error;
    }
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<any[]> {
    try {
      const migrations = await this.dataSource.showMigrations();
      return migrations;
    } catch (error) {
      this.logger.error('Failed to get migration status', error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query
   */
  async query(sql: string, parameters?: any[]): Promise<any> {
    try {
      return await this.dataSource.query(sql, parameters);
    } catch (error) {
      this.logger.error('Failed to execute query', error);
      throw error;
    }
  }

  /**
   * Start a database transaction
   */
  async transaction<T>(
    runInTransaction: (manager: any) => Promise<T>
  ): Promise<T> {
    return this.dataSource.transaction(runInTransaction);
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: any;
  }> {
    try {
      // Simple query to check database connectivity
      await this.dataSource.query('SELECT 1');
      
      return {
        status: 'healthy',
        details: {
          connected: true,
          database: this.dataSource.options.database,
          host: this.dataSource.options.host,
          port: this.dataSource.options.port,
        },
      };
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          error: error.message,
        },
      };
    }
  }

  /**
   * Get database statistics
   */
  async getStatistics(): Promise<any> {
    try {
      const stats = await this.dataSource.query(`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation
        FROM pg_stats 
        WHERE schemaname = 'public'
        LIMIT 10
      `);

      const tableStats = await this.dataSource.query(`
        SELECT 
          schemaname,
          tablename,
          n_tup_ins as inserts,
          n_tup_upd as updates,
          n_tup_del as deletes
        FROM pg_stat_user_tables
      `);

      return {
        columnStats: stats,
        tableStats: tableStats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get database statistics', error);
      throw error;
    }
  }

  /**
   * Backup database
   */
  async createBackup(backupName?: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const name = backupName || `backup_${timestamp}`;
      
      this.logger.log(`Creating database backup: ${name}`);
      
      // In a real implementation, you would use pg_dump or similar
      // This is a placeholder for the backup logic
      
      this.logger.log(`Database backup created: ${name}`);
      return name;
    } catch (error) {
      this.logger.error('Failed to create database backup', error);
      throw error;
    }
  }

  /**
   * Restore database from backup
   */
  async restoreBackup(backupName: string): Promise<void> {
    try {
      this.logger.log(`Restoring database from backup: ${backupName}`);
      
      // In a real implementation, you would use pg_restore or similar
      // This is a placeholder for the restore logic
      
      this.logger.log(`Database restored from backup: ${backupName}`);
    } catch (error) {
      this.logger.error('Failed to restore database backup', error);
      throw error;
    }
  }
}
