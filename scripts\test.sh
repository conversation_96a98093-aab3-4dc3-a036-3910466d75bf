#!/bin/bash

# CloudForge Platform - Comprehensive Testing Script
# Enterprise-grade cloud services platform

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_TYPE="${1:-all}"
COVERAGE_THRESHOLD=90
PARALLEL_JOBS=4

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    exit 1
}

# Setup test environment
setup_test_env() {
    log_info "Setting up test environment..."
    
    cd "$PROJECT_ROOT"
    
    # Install dependencies if not already installed
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing dependencies..."
        npm install
    fi
    
    # Start test database and Redis
    log_info "Starting test infrastructure..."
    docker-compose -f docker-compose.test.yml up -d postgres redis
    
    # Wait for services to be ready
    sleep 10
    
    # Run database migrations for test environment
    log_info "Running test database migrations..."
    NODE_ENV=test npm run db:migrate
    
    log_success "Test environment setup completed"
}

# Run unit tests
run_unit_tests() {
    log_info "Running unit tests..."
    
    cd "$PROJECT_ROOT"
    
    # Run Jest unit tests with coverage
    npm run test:unit -- \
        --coverage \
        --coverageReporters=text \
        --coverageReporters=lcov \
        --coverageReporters=html \
        --coverageThreshold="{\"global\":{\"branches\":$COVERAGE_THRESHOLD,\"functions\":$COVERAGE_THRESHOLD,\"lines\":$COVERAGE_THRESHOLD,\"statements\":$COVERAGE_THRESHOLD}}" \
        --maxWorkers=$PARALLEL_JOBS \
        --passWithNoTests
    
    # Check coverage threshold
    if [[ -f "coverage/lcov.info" ]]; then
        log_success "Unit tests completed with coverage report"
    else
        log_warning "Coverage report not generated"
    fi
}

# Run integration tests
run_integration_tests() {
    log_info "Running integration tests..."
    
    cd "$PROJECT_ROOT"
    
    # Start all services for integration testing
    docker-compose up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be ready..."
    sleep 60
    
    # Run integration tests
    npm run test:integration -- \
        --maxWorkers=$PARALLEL_JOBS \
        --testTimeout=30000 \
        --passWithNoTests
    
    log_success "Integration tests completed"
}

# Run end-to-end tests
run_e2e_tests() {
    log_info "Running end-to-end tests..."
    
    cd "$PROJECT_ROOT"
    
    # Ensure services are running
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 60
    
    # Run Cypress E2E tests
    if command -v cypress >/dev/null 2>&1; then
        npm run test:e2e:headless
    else
        log_warning "Cypress not found, skipping E2E tests"
    fi
    
    log_success "End-to-end tests completed"
}

# Run load tests
run_load_tests() {
    log_info "Running load tests..."
    
    cd "$PROJECT_ROOT"
    
    # Ensure services are running
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 60
    
    # Run k6 load tests
    if command -v k6 >/dev/null 2>&1; then
        k6 run tests/load/api-load-test.js
        k6 run tests/load/auth-load-test.js
        k6 run tests/load/user-load-test.js
    else
        log_warning "k6 not found, skipping load tests"
    fi
    
    log_success "Load tests completed"
}

# Run security tests
run_security_tests() {
    log_info "Running security tests..."
    
    cd "$PROJECT_ROOT"
    
    # Run npm audit
    log_info "Running npm security audit..."
    npm audit --audit-level=moderate || log_warning "npm audit found vulnerabilities"
    
    # Run Snyk security scan if available
    if command -v snyk >/dev/null 2>&1; then
        log_info "Running Snyk security scan..."
        snyk test || log_warning "Snyk found vulnerabilities"
    else
        log_warning "Snyk not found, skipping security scan"
    fi
    
    # Run Semgrep if available
    if command -v semgrep >/dev/null 2>&1; then
        log_info "Running Semgrep static analysis..."
        semgrep --config=auto . || log_warning "Semgrep found issues"
    else
        log_warning "Semgrep not found, skipping static analysis"
    fi
    
    # Run TruffleHog for secrets detection if available
    if command -v trufflehog >/dev/null 2>&1; then
        log_info "Running TruffleHog secrets detection..."
        trufflehog filesystem . --no-verification || log_warning "TruffleHog found potential secrets"
    else
        log_warning "TruffleHog not found, skipping secrets detection"
    fi
    
    log_success "Security tests completed"
}

# Run linting and formatting checks
run_lint_tests() {
    log_info "Running linting and formatting checks..."
    
    cd "$PROJECT_ROOT"
    
    # Run ESLint
    log_info "Running ESLint..."
    npm run lint
    
    # Check Prettier formatting
    log_info "Checking Prettier formatting..."
    npm run format:check
    
    # Run TypeScript compiler check
    log_info "Running TypeScript compiler check..."
    npx tsc --noEmit
    
    log_success "Linting and formatting checks completed"
}

# Generate test reports
generate_reports() {
    log_info "Generating test reports..."
    
    cd "$PROJECT_ROOT"
    
    # Create reports directory
    mkdir -p reports
    
    # Copy coverage reports
    if [[ -d "coverage" ]]; then
        cp -r coverage reports/
        log_info "Coverage report available at: reports/coverage/index.html"
    fi
    
    # Generate test summary
    cat > reports/test-summary.md << EOF
# CloudForge Platform - Test Summary

## Test Results

- **Unit Tests**: $(if [[ "$TEST_TYPE" == "all" ]] || [[ "$TEST_TYPE" == "unit" ]]; then echo "✅ Passed"; else echo "⏭️ Skipped"; fi)
- **Integration Tests**: $(if [[ "$TEST_TYPE" == "all" ]] || [[ "$TEST_TYPE" == "integration" ]]; then echo "✅ Passed"; else echo "⏭️ Skipped"; fi)
- **E2E Tests**: $(if [[ "$TEST_TYPE" == "all" ]] || [[ "$TEST_TYPE" == "e2e" ]]; then echo "✅ Passed"; else echo "⏭️ Skipped"; fi)
- **Load Tests**: $(if [[ "$TEST_TYPE" == "all" ]] || [[ "$TEST_TYPE" == "load" ]]; then echo "✅ Passed"; else echo "⏭️ Skipped"; fi)
- **Security Tests**: $(if [[ "$TEST_TYPE" == "all" ]] || [[ "$TEST_TYPE" == "security" ]]; then echo "✅ Passed"; else echo "⏭️ Skipped"; fi)
- **Linting**: $(if [[ "$TEST_TYPE" == "all" ]] || [[ "$TEST_TYPE" == "lint" ]]; then echo "✅ Passed"; else echo "⏭️ Skipped"; fi)

## Coverage

- **Target**: ${COVERAGE_THRESHOLD}%
- **Achieved**: $(if [[ -f "coverage/coverage-summary.json" ]]; then cat coverage/coverage-summary.json | grep -o '"lines":{"total":[0-9]*,"covered":[0-9]*,"skipped":[0-9]*,"pct":[0-9.]*}' | grep -o '"pct":[0-9.]*' | cut -d':' -f2; else echo "N/A"; fi)%

## Generated Reports

- Coverage Report: \`reports/coverage/index.html\`
- Test Results: Available in console output

---

Generated on: $(date)
EOF
    
    log_success "Test reports generated in reports/ directory"
}

# Cleanup test environment
cleanup_test_env() {
    log_info "Cleaning up test environment..."
    
    cd "$PROJECT_ROOT"
    
    # Stop test containers
    docker-compose -f docker-compose.test.yml down --remove-orphans
    docker-compose down --remove-orphans
    
    log_success "Test environment cleanup completed"
}

# Main test function
main() {
    log_info "Starting CloudForge Platform test suite..."
    log_info "Test type: $TEST_TYPE"
    log_info "Coverage threshold: $COVERAGE_THRESHOLD%"
    
    # Set trap for cleanup
    trap cleanup_test_env EXIT
    
    # Setup test environment
    setup_test_env
    
    # Run tests based on type
    case $TEST_TYPE in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "load")
            run_load_tests
            ;;
        "security")
            run_security_tests
            ;;
        "lint")
            run_lint_tests
            ;;
        "all")
            run_lint_tests
            run_unit_tests
            run_integration_tests
            run_e2e_tests
            run_load_tests
            run_security_tests
            ;;
        *)
            error_exit "Unknown test type: $TEST_TYPE. Valid options: unit, integration, e2e, load, security, lint, all"
            ;;
    esac
    
    # Generate reports
    generate_reports
    
    log_success "All tests completed successfully!"
    
    # Display summary
    echo ""
    log_info "Test Summary:"
    echo "  Test type: $TEST_TYPE"
    echo "  Coverage threshold: $COVERAGE_THRESHOLD%"
    echo "  Reports: reports/"
    echo "  Coverage: reports/coverage/index.html"
}

# Show help
show_help() {
    echo "CloudForge Platform - Test Script"
    echo ""
    echo "Usage: $0 [TEST_TYPE] [OPTIONS]"
    echo ""
    echo "Test Types:"
    echo "  unit         Run unit tests only"
    echo "  integration  Run integration tests only"
    echo "  e2e          Run end-to-end tests only"
    echo "  load         Run load tests only"
    echo "  security     Run security tests only"
    echo "  lint         Run linting and formatting checks only"
    echo "  all          Run all tests (default)"
    echo ""
    echo "Environment Variables:"
    echo "  COVERAGE_THRESHOLD  Coverage threshold percentage (default: 90)"
    echo "  PARALLEL_JOBS       Number of parallel test jobs (default: 4)"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run all tests"
    echo "  $0 unit             # Run unit tests only"
    echo "  $0 security         # Run security tests only"
    echo "  COVERAGE_THRESHOLD=95 $0 unit  # Run unit tests with 95% coverage"
}

# Parse command line arguments
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# Run main function
main
