#!/bin/bash

set -e

echo "🚀 Starting deployment process..."

# Configuration
ENVIRONMENT=${1:-production}
NAMESPACE="enterprise-platform"
IMAGE_TAG=${2:-latest}

echo "📋 Deployment Configuration:"
echo "  Environment: $ENVIRONMENT"
echo "  Namespace: $NAMESPACE"
echo "  Image Tag: $IMAGE_TAG"

# Pre-deployment checks
echo "🔍 Running pre-deployment checks..."
npm run lint
npm run test
npm run build

# Build Docker image
echo "🐳 Building Docker image..."
docker build -t enterprise-platform:$IMAGE_TAG .

# Deploy to Kubernetes
echo "☸️  Deploying to Kubernetes..."
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Apply configurations
kubectl apply -f infra/k8s/ -n $NAMESPACE

# Update deployment with new image
kubectl set image deployment/api-deployment api=enterprise-platform:$IMAGE_TAG -n $NAMESPACE

# Wait for rollout
echo "⏳ Waiting for deployment to complete..."
kubectl rollout status deployment/api-deployment -n $NAMESPACE --timeout=300s

# Verify deployment
echo "✅ Verifying deployment..."
kubectl get pods -n $NAMESPACE
kubectl get services -n $NAMESPACE

# Run health check
echo "🏥 Running health check..."
sleep 10
SERVICE_IP=$(kubectl get service api-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$SERVICE_IP" ]; then
    SERVICE_IP=$(kubectl get service api-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
fi

curl -f http://$SERVICE_IP/health || {
    echo "❌ Health check failed"
    exit 1
}

echo "🎉 Deployment completed successfully!"
echo "📊 Access your application:"
echo "  API: http://$SERVICE_IP"
echo "  Docs: http://$SERVICE_IP/docs"