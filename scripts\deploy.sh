#!/bin/bash

# CloudForge Platform - Production Deployment Script
# Enterprise-grade cloud services platform

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${ENVIRONMENT:-production}"
NAMESPACE="${NAMESPACE:-cloudforge}"
DOCKER_REGISTRY="${DOCKER_REGISTRY:-}"
VERSION="${VERSION:-latest}"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    exit 1
}

echo "🚀 Starting CloudForge Platform deployment..."
echo "📋 Deployment Configuration:"
echo "  Environment: $ENVIRONMENT"

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if required tools are installed
    command -v docker >/dev/null 2>&1 || error_exit "Docker is required but not installed"
    command -v kubectl >/dev/null 2>&1 || error_exit "kubectl is required but not installed"
    command -v helm >/dev/null 2>&1 || error_exit "Helm is required but not installed"

    # Check if .env file exists
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        log_warning ".env file not found, using .env.example"
        cp "$PROJECT_ROOT/.env.example" "$PROJECT_ROOT/.env"
    fi

    log_success "Prerequisites check completed"
}

# Build Docker images
build_images() {
    log_info "Building Docker images..."

    cd "$PROJECT_ROOT"

    # Build all service images
    services=("api-gateway" "auth-service" "user-service" "billing-service" "notification-service" "monitoring-service" "admin-dashboard")

    for service in "${services[@]}"; do
        log_info "Building $service..."

        if [[ -n "$DOCKER_REGISTRY" ]]; then
            image_name="$DOCKER_REGISTRY/cloudforge-$service:$VERSION"
        else
            image_name="cloudforge-$service:$VERSION"
        fi

        docker build \
            -f "apps/$service/Dockerfile" \
            -t "$image_name" \
            --target production \
            . || error_exit "Failed to build $service"

        # Push to registry if specified
        if [[ -n "$DOCKER_REGISTRY" ]]; then
            log_info "Pushing $service to registry..."
            docker push "$image_name" || error_exit "Failed to push $service"
        fi
    done

    log_success "Docker images built successfully"
}

# Deploy with Docker Compose (for development/staging)
deploy_docker_compose() {
    log_info "Deploying with Docker Compose..."

    cd "$PROJECT_ROOT"

    # Stop existing services
    docker-compose down --remove-orphans

    # Build and start services
    docker-compose build
    docker-compose up -d

    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30

    log_success "Docker Compose deployment completed"
}

# Main deployment function
main() {
    log_info "Starting CloudForge Platform deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"

    # Determine deployment type
    if [[ "$ENVIRONMENT" == "production" ]] || [[ "$ENVIRONMENT" == "staging" ]]; then
        DEPLOYMENT_TYPE="kubernetes"
    else
        DEPLOYMENT_TYPE="docker-compose"
    fi

    log_info "Deployment type: $DEPLOYMENT_TYPE"

    # Run deployment steps
    check_prerequisites
    build_images

    if [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        log_info "Kubernetes deployment not implemented in this version"
        log_info "Using Docker Compose instead..."
        deploy_docker_compose
    else
        deploy_docker_compose
    fi

    log_success "CloudForge Platform deployment completed successfully!"

    # Display access information
    echo ""
    log_info "Access Information:"
    echo "  API Gateway: http://localhost:3000"
    echo "  Admin Dashboard: http://localhost:3010"
    echo "  Swagger Docs: http://localhost:3000/api/docs"
    echo "  Grafana: http://localhost:3000 (admin/cloudforge_grafana_password)"
    echo "  Prometheus: http://localhost:9090"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -r|--registry)
            DOCKER_REGISTRY="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -e, --environment    Deployment environment (development, staging, production)"
            echo "  -v, --version        Version tag for Docker images"
            echo "  -r, --registry       Docker registry URL"
            echo "  -n, --namespace      Kubernetes namespace"
            echo "  -h, --help           Show this help message"
            exit 0
            ;;
        *)
            error_exit "Unknown option: $1"
            ;;
    esac
done

# Run main function
main
echo "  Namespace: $NAMESPACE"
echo "  Image Tag: $IMAGE_TAG"

# Pre-deployment checks
echo "🔍 Running pre-deployment checks..."
npm run lint
npm run test
npm run build

# Build Docker image
echo "🐳 Building Docker image..."
docker build -t enterprise-platform:$IMAGE_TAG .

# Deploy to Kubernetes
echo "☸️  Deploying to Kubernetes..."
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Apply configurations
kubectl apply -f infra/k8s/ -n $NAMESPACE

# Update deployment with new image
kubectl set image deployment/api-deployment api=enterprise-platform:$IMAGE_TAG -n $NAMESPACE

# Wait for rollout
echo "⏳ Waiting for deployment to complete..."
kubectl rollout status deployment/api-deployment -n $NAMESPACE --timeout=300s

# Verify deployment
echo "✅ Verifying deployment..."
kubectl get pods -n $NAMESPACE
kubectl get services -n $NAMESPACE

# Run health check
echo "🏥 Running health check..."
sleep 10
SERVICE_IP=$(kubectl get service api-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$SERVICE_IP" ]; then
    SERVICE_IP=$(kubectl get service api-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
fi

curl -f http://$SERVICE_IP/health || {
    echo "❌ Health check failed"
    exit 1
}

echo "🎉 Deployment completed successfully!"
echo "📊 Access your application:"
echo "  API: http://$SERVICE_IP"
echo "  Docs: http://$SERVICE_IP/docs"