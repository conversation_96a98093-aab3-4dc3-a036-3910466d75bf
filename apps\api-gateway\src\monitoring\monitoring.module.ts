/**
 * CloudForge Platform - Monitoring Module (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { MonitoringController } from './monitoring.controller';
import { MonitoringService } from './monitoring.service';

@Module({
  imports: [HttpModule],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
