/**
 * CloudForge Platform - Proxy Service (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ProxyService {
  private readonly logger = new Logger(ProxyService.name);
  private readonly serviceRoutes: Map<string, string>;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    // Initialize service routing table
    this.serviceRoutes = new Map([
      ['auth', this.configService.get<string>('services.authService.url')],
      ['users', this.configService.get<string>('services.userService.url')],
      ['billing', this.configService.get<string>('services.billingService.url')],
      ['notifications', this.configService.get<string>('services.notificationService.url')],
      ['monitoring', this.configService.get<string>('services.monitoringService.url')],
    ]);
  }

  async proxyRequest(req: Request, res: Response): Promise<void> {
    try {
      const path = req.path.replace('/proxy/', '');
      const segments = path.split('/');
      const serviceName = segments[0];
      const servicePath = segments.slice(1).join('/');

      // Get target service URL
      const serviceUrl = this.serviceRoutes.get(serviceName);
      if (!serviceUrl) {
        throw new HttpException(
          `Service '${serviceName}' not found`,
          HttpStatus.NOT_FOUND
        );
      }

      // Build target URL
      const targetUrl = `${serviceUrl}/${servicePath}`;
      
      this.logger.log(`Proxying ${req.method} request to: ${targetUrl}`);

      // Prepare headers (exclude host and connection headers)
      const headers = { ...req.headers };
      delete headers.host;
      delete headers.connection;
      delete headers['content-length'];

      // Make the proxied request
      const response = await firstValueFrom(
        this.httpService.request({
          method: req.method as any,
          url: targetUrl,
          data: req.body,
          headers,
          params: req.query,
          timeout: 30000, // 30 seconds timeout
        })
      );

      // Forward response headers
      Object.entries(response.headers).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'transfer-encoding') {
          res.setHeader(key, value as string);
        }
      });

      // Send response
      res.status(response.status).json(response.data);

    } catch (error) {
      this.logger.error('Proxy request failed', error.response?.data || error.message);

      if (error.response) {
        // Forward error response from target service
        res.status(error.response.status).json(error.response.data);
      } else if (error.code === 'ECONNREFUSED') {
        // Service unavailable
        res.status(HttpStatus.SERVICE_UNAVAILABLE).json({
          success: false,
          message: 'Service temporarily unavailable',
          timestamp: new Date().toISOString(),
          path: req.path,
          statusCode: HttpStatus.SERVICE_UNAVAILABLE,
        });
      } else {
        // Generic error
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Proxy request failed',
          timestamp: new Date().toISOString(),
          path: req.path,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        });
      }
    }
  }
}
