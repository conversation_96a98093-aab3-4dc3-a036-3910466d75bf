/**
 * CloudForge Platform - Notifications Service (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);
  private readonly notificationServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.notificationServiceUrl = this.configService.get<string>('services.notificationService.url');
  }

  async getNotifications(query: any) {
    try {
      this.logger.log('Proxying get notifications request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.notificationServiceUrl}/notifications`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get notifications request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async sendNotification(notificationData: any) {
    try {
      this.logger.log('Proxying send notification request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.notificationServiceUrl}/notifications`, notificationData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Send notification request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async markAsRead(id: string) {
    try {
      this.logger.log(`Proxying mark as read request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.notificationServiceUrl}/notifications/${id}/read`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Mark as read request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async markAllAsRead() {
    try {
      this.logger.log('Proxying mark all as read request');
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.notificationServiceUrl}/notifications/read-all`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Mark all as read request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async deleteNotification(id: string) {
    try {
      this.logger.log(`Proxying delete notification request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.delete(`${this.notificationServiceUrl}/notifications/${id}`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Delete notification request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getTemplates() {
    try {
      this.logger.log('Proxying get templates request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.notificationServiceUrl}/templates`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get templates request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async createTemplate(templateData: any) {
    try {
      this.logger.log('Proxying create template request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.notificationServiceUrl}/templates`, templateData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Create template request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getPreferences() {
    try {
      this.logger.log('Proxying get preferences request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.notificationServiceUrl}/preferences`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get preferences request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async updatePreferences(preferencesData: any) {
    try {
      this.logger.log('Proxying update preferences request');
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.notificationServiceUrl}/preferences`, preferencesData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Update preferences request failed', error.response?.data || error.message);
      throw error;
    }
  }
}
