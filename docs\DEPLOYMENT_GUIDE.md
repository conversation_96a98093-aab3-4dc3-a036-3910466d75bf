# CloudForge Platform - Deployment Guide

## 🚀 Overview

This guide provides comprehensive instructions for deploying CloudForge Platform in various environments, from local development to production-grade Kubernetes clusters.

## 📋 Prerequisites

### System Requirements
- **CPU**: 4+ cores (8+ recommended for production)
- **Memory**: 8GB RAM minimum (16GB+ recommended for production)
- **Storage**: 50GB available disk space
- **Network**: Stable internet connection for external dependencies

### Required Software
- **Docker**: 20.0+ with Docker Compose 2.0+
- **Node.js**: 18.0+ (for local development)
- **Kubernetes**: 1.24+ (for production deployment)
- **Helm**: 3.8+ (for Kubernetes deployment)
- **kubectl**: Compatible with your Kubernetes version

### Optional Tools
- **Terraform**: 1.0+ (for infrastructure provisioning)
- **Git**: For source code management
- **Make**: For build automation

## 🏠 Local Development Deployment

### Quick Start (Recommended)

1. **Clone the Repository**
   ```bash
   git clone https://github.com/cloudforge/platform.git
   cd cloudforge-platform
   ```

2. **Run Setup Script**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **Start Services**
   ```bash
   docker-compose up -d
   ```

4. **Verify Deployment**
   ```bash
   # Check service health
   curl http://localhost:3000/health
   
   # Access applications
   # Admin Dashboard: https://admin.cloudforge.local
   # API Gateway: https://api.cloudforge.local
   # Grafana: http://localhost:3000
   ```

### Manual Setup

1. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Install Dependencies**
   ```bash
   npm install
   
   # Install service dependencies
   for service in apps/*/; do
     (cd "$service" && npm install)
   done
   ```

3. **Generate SSL Certificates**
   ```bash
   mkdir -p infra/docker/ssl
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout infra/docker/ssl/cloudforge.key \
     -out infra/docker/ssl/cloudforge.crt \
     -subj "/C=US/ST=CA/L=SF/O=CloudForge/CN=cloudforge.local"
   ```

4. **Start Infrastructure Services**
   ```bash
   docker-compose up -d postgres redis
   ```

5. **Run Database Migrations**
   ```bash
   npm run migration:run
   ```

6. **Start Application Services**
   ```bash
   # Start all services
   npm run dev
   
   # Or start individual services
   npm run dev:api-gateway
   npm run dev:auth-service
   npm run dev:admin-dashboard
   ```

### Development Tools

```bash
# Build all services
npm run build

# Run tests
npm run test

# Run linting
npm run lint

# Format code
npm run format

# Generate API documentation
npm run docs:generate
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Prerequisites
- AWS CLI configured with appropriate permissions
- EKS cluster or EC2 instances
- RDS PostgreSQL instance
- ElastiCache Redis cluster
- S3 bucket for file storage

#### Using EKS (Recommended)

1. **Create EKS Cluster**
   ```bash
   # Using eksctl
   eksctl create cluster \
     --name cloudforge-platform \
     --region us-west-2 \
     --nodes 3 \
     --node-type m5.large \
     --managed
   ```

2. **Configure kubectl**
   ```bash
   aws eks update-kubeconfig \
     --region us-west-2 \
     --name cloudforge-platform
   ```

3. **Deploy with Helm**
   ```bash
   # Add required Helm repositories
   helm repo add bitnami https://charts.bitnami.com/bitnami
   helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
   helm repo update
   
   # Deploy CloudForge Platform
   helm install cloudforge-platform ./infra/helm \
     --namespace cloudforge-platform \
     --create-namespace \
     --values ./infra/helm/values-aws.yaml \
     --set global.environment=production \
     --set postgresql.auth.password="$(openssl rand -base64 32)" \
     --set redis.auth.password="$(openssl rand -base64 32)"
   ```

#### Using EC2 with Docker Swarm

1. **Prepare EC2 Instances**
   ```bash
   # Install Docker on all nodes
   curl -fsSL https://get.docker.com | sh
   sudo usermod -aG docker $USER
   
   # Initialize swarm on manager node
   docker swarm init --advertise-addr <MANAGER-IP>
   
   # Join worker nodes
   docker swarm join --token <TOKEN> <MANAGER-IP>:2377
   ```

2. **Deploy Stack**
   ```bash
   # Deploy production stack
   docker stack deploy -c docker-compose.prod.yml cloudforge
   ```

### Google Cloud Platform (GCP)

#### Using GKE

1. **Create GKE Cluster**
   ```bash
   gcloud container clusters create cloudforge-platform \
     --zone us-central1-a \
     --num-nodes 3 \
     --machine-type n1-standard-2 \
     --enable-autoscaling \
     --min-nodes 1 \
     --max-nodes 10
   ```

2. **Configure kubectl**
   ```bash
   gcloud container clusters get-credentials cloudforge-platform \
     --zone us-central1-a
   ```

3. **Deploy Application**
   ```bash
   helm install cloudforge-platform ./infra/helm \
     --namespace cloudforge-platform \
     --create-namespace \
     --values ./infra/helm/values-gcp.yaml
   ```

### Microsoft Azure

#### Using AKS

1. **Create AKS Cluster**
   ```bash
   az aks create \
     --resource-group cloudforge-rg \
     --name cloudforge-platform \
     --node-count 3 \
     --node-vm-size Standard_D2s_v3 \
     --enable-addons monitoring \
     --generate-ssh-keys
   ```

2. **Configure kubectl**
   ```bash
   az aks get-credentials \
     --resource-group cloudforge-rg \
     --name cloudforge-platform
   ```

3. **Deploy Application**
   ```bash
   helm install cloudforge-platform ./infra/helm \
     --namespace cloudforge-platform \
     --create-namespace \
     --values ./infra/helm/values-azure.yaml
   ```

## 🐳 Production Deployment with Kubernetes

### Preparation

1. **Create Namespace**
   ```bash
   kubectl create namespace cloudforge-platform
   ```

2. **Create Secrets**
   ```bash
   # Database credentials
   kubectl create secret generic postgres-credentials \
     --from-literal=username=cloudforge \
     --from-literal=password="$(openssl rand -base64 32)" \
     --namespace cloudforge-platform
   
   # Redis credentials
   kubectl create secret generic redis-credentials \
     --from-literal=password="$(openssl rand -base64 32)" \
     --namespace cloudforge-platform
   
   # JWT secrets
   kubectl create secret generic jwt-secrets \
     --from-literal=secret="$(openssl rand -base64 64)" \
     --from-literal=refresh-secret="$(openssl rand -base64 64)" \
     --namespace cloudforge-platform
   
   # External service credentials
   kubectl create secret generic external-services \
     --from-literal=stripe-secret-key="sk_live_..." \
     --from-literal=sendgrid-api-key="SG...." \
     --from-literal=aws-access-key-id="AKIA..." \
     --from-literal=aws-secret-access-key="..." \
     --namespace cloudforge-platform
   ```

3. **Create ConfigMaps**
   ```bash
   kubectl create configmap app-config \
     --from-file=./config/production.yaml \
     --namespace cloudforge-platform
   ```

### Deployment Options

#### Option 1: Helm Deployment (Recommended)

```bash
# Install with custom values
helm install cloudforge-platform ./infra/helm \
  --namespace cloudforge-platform \
  --values ./infra/helm/values-production.yaml \
  --set global.environment=production \
  --set global.version=1.0.0 \
  --set ingress.hosts[0].host=api.yourdomain.com \
  --set ingress.hosts[1].host=admin.yourdomain.com \
  --timeout=10m \
  --wait
```

#### Option 2: kubectl Deployment

```bash
# Apply Kubernetes manifests
kubectl apply -f infra/kubernetes/namespace.yaml
kubectl apply -f infra/kubernetes/configmaps/
kubectl apply -f infra/kubernetes/secrets/
kubectl apply -f infra/kubernetes/deployments/
kubectl apply -f infra/kubernetes/services/
kubectl apply -f infra/kubernetes/ingress/
```

#### Option 3: Automated Deployment Script

```bash
# Use deployment script
./scripts/deploy.sh -e production -v 1.0.0 deploy
```

### Post-Deployment Verification

1. **Check Pod Status**
   ```bash
   kubectl get pods -n cloudforge-platform
   kubectl get services -n cloudforge-platform
   kubectl get ingress -n cloudforge-platform
   ```

2. **Verify Health Checks**
   ```bash
   # Check API Gateway health
   curl https://api.yourdomain.com/health
   
   # Check individual service health
   kubectl exec -n cloudforge-platform deployment/api-gateway -- curl localhost:3000/health
   ```

3. **Monitor Logs**
   ```bash
   # View logs for all services
   kubectl logs -n cloudforge-platform -l app.kubernetes.io/name=cloudforge-platform --tail=100
   
   # View specific service logs
   kubectl logs -n cloudforge-platform deployment/api-gateway -f
   ```

## 🔧 Configuration Management

### Environment Variables

Key configuration variables for production:

```bash
# Application
NODE_ENV=production
LOG_LEVEL=info

# Database
DATABASE_HOST=postgres.yourdomain.com
DATABASE_SSL=true
DATABASE_POOL_SIZE=20

# Security
JWT_SECRET=your-secure-jwt-secret
ENCRYPTION_KEY=your-32-character-key

# External Services
STRIPE_SECRET_KEY=sk_live_...
SENDGRID_API_KEY=SG....
AWS_ACCESS_KEY_ID=AKIA...
```

### Scaling Configuration

```bash
# Horizontal Pod Autoscaler
kubectl autoscale deployment api-gateway \
  --cpu-percent=70 \
  --min=3 \
  --max=10 \
  -n cloudforge-platform

# Manual scaling
kubectl scale deployment api-gateway \
  --replicas=5 \
  -n cloudforge-platform
```

### SSL/TLS Configuration

```bash
# Install cert-manager for automatic SSL
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Create ClusterIssuer for Let's Encrypt
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

## 📊 Monitoring Setup

### Prometheus and Grafana

```bash
# Install monitoring stack
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add grafana https://grafana.github.io/helm-charts

# Install Prometheus
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --values ./infra/helm/prometheus-values.yaml

# Install Grafana
helm install grafana grafana/grafana \
  --namespace monitoring \
  --values ./infra/helm/grafana-values.yaml
```

### Log Aggregation

```bash
# Install ELK stack (optional)
helm repo add elastic https://helm.elastic.co

helm install elasticsearch elastic/elasticsearch \
  --namespace logging \
  --create-namespace

helm install kibana elastic/kibana \
  --namespace logging

helm install filebeat elastic/filebeat \
  --namespace logging
```

## 🔄 Backup and Disaster Recovery

### Database Backup

```bash
# Automated backup script
kubectl create cronjob postgres-backup \
  --image=postgres:15 \
  --schedule="0 2 * * *" \
  --restart=OnFailure \
  -- /bin/sh -c "pg_dump -h postgres -U cloudforge cloudforge_platform | gzip > /backup/backup-$(date +%Y%m%d).sql.gz"
```

### Application Backup

```bash
# Backup Kubernetes resources
kubectl get all,configmaps,secrets,pvc -n cloudforge-platform -o yaml > cloudforge-backup.yaml

# Backup Helm release
helm get values cloudforge-platform -n cloudforge-platform > helm-values-backup.yaml
```

## 🚨 Troubleshooting

### Common Issues

1. **Pod Startup Issues**
   ```bash
   kubectl describe pod <pod-name> -n cloudforge-platform
   kubectl logs <pod-name> -n cloudforge-platform
   ```

2. **Database Connection Issues**
   ```bash
   kubectl exec -it deployment/api-gateway -n cloudforge-platform -- nc -zv postgres 5432
   ```

3. **Service Discovery Issues**
   ```bash
   kubectl get endpoints -n cloudforge-platform
   kubectl get services -n cloudforge-platform
   ```

### Performance Optimization

```bash
# Check resource usage
kubectl top pods -n cloudforge-platform
kubectl top nodes

# Optimize resource limits
kubectl patch deployment api-gateway -n cloudforge-platform -p '{"spec":{"template":{"spec":{"containers":[{"name":"api-gateway","resources":{"limits":{"cpu":"1000m","memory":"1Gi"},"requests":{"cpu":"500m","memory":"512Mi"}}}]}}}}'
```

## 📞 Support

For deployment support:
- **Documentation**: [docs.cloudforge.com](https://docs.cloudforge.com)
- **Support Email**: <EMAIL>
- **Community**: [GitHub Discussions](https://github.com/cloudforge/platform/discussions)
