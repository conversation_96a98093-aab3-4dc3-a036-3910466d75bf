/**
 * CloudForge Platform - Billing Plan Entity
 * Enterprise-grade cloud services platform
 */

import {
  Entity,
  Column,
  Index,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { BaseEntity } from '../base/base.entity';
import { Subscription } from './subscription.entity';

export enum BillingInterval {
  MONTH = 'month',
  YEAR = 'year',
  WEEK = 'week',
  DAY = 'day',
}

@Entity('billing_plans')
@Index(['name'], { unique: true })
@Index(['isActive'])
@Index(['price'])
@Index(['interval'])
@Index(['priority'])
export class BillingPlan extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Plan name (unique identifier)',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Plan description',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Human-readable display name',
  })
  displayName?: string;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: 'Plan price',
  })
  price: number;

  @Column({
    type: 'varchar',
    length: 3,
    default: 'USD',
    comment: 'Currency code',
  })
  currency: string;

  @Column({
    type: 'enum',
    enum: BillingInterval,
    default: BillingInterval.MONTH,
    comment: 'Billing interval',
  })
  interval: BillingInterval;

  @Column({
    type: 'int',
    default: 1,
    comment: 'Number of intervals between billings',
  })
  intervalCount: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Trial period in days',
  })
  trialPeriodDays?: number;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this plan is active and available',
  })
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a free plan',
  })
  isFree: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this plan is featured/recommended',
  })
  isFeatured: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display priority (higher = shown first)',
  })
  priority: number;

  @Column({
    type: 'varchar',
    length: 7,
    nullable: true,
    comment: 'Color code for UI display',
  })
  color?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Icon name for UI display',
  })
  icon?: string;

  @Column({
    type: 'text',
    array: true,
    nullable: true,
    comment: 'List of plan features',
  })
  features?: string[];

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Usage limits for this plan',
  })
  limits?: Record<string, number>;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'External plan ID (e.g., Stripe price ID)',
  })
  externalId?: string;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional plan configuration',
  })
  config?: Record<string, any>;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Plan metadata',
  })
  planMetadata?: Record<string, any>;

  // Relationships
  @OneToMany(() => Subscription, subscription => subscription.plan)
  subscriptions: Subscription[];

  // Hooks
  @BeforeInsert()
  @BeforeUpdate()
  normalizeName(): void {
    if (this.name) {
      this.name = this.name.toLowerCase().trim();
    }
  }

  @BeforeInsert()
  setDefaults(): void {
    if (!this.displayName) {
      this.displayName = this.capitalizeWords(this.name);
    }
    
    if (this.price === 0) {
      this.isFree = true;
    }
  }

  // Methods
  getSubscriptionCount(): number {
    return this.subscriptions?.length || 0;
  }

  getActiveSubscriptionCount(): number {
    return this.subscriptions?.filter(sub => sub.isActive()).length || 0;
  }

  getMonthlyPrice(): number {
    switch (this.interval) {
      case BillingInterval.YEAR:
        return this.price / 12;
      case BillingInterval.WEEK:
        return this.price * 4.33; // Average weeks per month
      case BillingInterval.DAY:
        return this.price * 30.44; // Average days per month
      case BillingInterval.MONTH:
      default:
        return this.price;
    }
  }

  getYearlyPrice(): number {
    switch (this.interval) {
      case BillingInterval.MONTH:
        return this.price * 12;
      case BillingInterval.WEEK:
        return this.price * 52;
      case BillingInterval.DAY:
        return this.price * 365;
      case BillingInterval.YEAR:
      default:
        return this.price;
    }
  }

  getFormattedPrice(): string {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: this.currency,
    });
    
    return formatter.format(this.price);
  }

  hasFeature(feature: string): boolean {
    return this.features?.includes(feature) || false;
  }

  addFeature(feature: string): void {
    if (!this.features) {
      this.features = [];
    }
    
    if (!this.hasFeature(feature)) {
      this.features.push(feature);
    }
  }

  removeFeature(feature: string): void {
    if (this.features) {
      this.features = this.features.filter(f => f !== feature);
    }
  }

  getLimit(resource: string): number {
    return this.limits?.[resource] ?? 0;
  }

  setLimit(resource: string, limit: number): void {
    if (!this.limits) {
      this.limits = {};
    }
    this.limits[resource] = limit;
  }

  removeLimit(resource: string): void {
    if (this.limits && this.limits[resource] !== undefined) {
      delete this.limits[resource];
    }
  }

  hasUnlimitedResource(resource: string): boolean {
    return this.getLimit(resource) === -1;
  }

  canBeDeleted(): boolean {
    return this.getActiveSubscriptionCount() === 0;
  }

  calculateSavings(comparedToPlan: BillingPlan): number {
    const thisYearlyPrice = this.getYearlyPrice();
    const comparedYearlyPrice = comparedToPlan.getYearlyPrice();
    
    return comparedYearlyPrice - thisYearlyPrice;
  }

  calculateSavingsPercentage(comparedToPlan: BillingPlan): number {
    const savings = this.calculateSavings(comparedToPlan);
    const comparedYearlyPrice = comparedToPlan.getYearlyPrice();
    
    if (comparedYearlyPrice === 0) return 0;
    
    return (savings / comparedYearlyPrice) * 100;
  }

  private capitalizeWords(str: string): string {
    return str
      .split(/[-_\s]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  updateConfig(key: string, value: any): void {
    if (!this.config) {
      this.config = {};
    }
    this.config[key] = value;
  }

  getConfig(key: string, defaultValue?: any): any {
    return this.config?.[key] ?? defaultValue;
  }

  toJSON(): Record<string, any> {
    const obj = super.toJSON();
    
    // Add computed fields
    obj.subscriptionCount = this.getSubscriptionCount();
    obj.activeSubscriptionCount = this.getActiveSubscriptionCount();
    obj.monthlyPrice = this.getMonthlyPrice();
    obj.yearlyPrice = this.getYearlyPrice();
    obj.formattedPrice = this.getFormattedPrice();
    obj.canBeDeleted = this.canBeDeleted();
    
    return obj;
  }
}
