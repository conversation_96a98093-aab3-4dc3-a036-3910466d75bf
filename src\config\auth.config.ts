import { registerAs } from '@nestjs/config';

export default registerAs('auth', () => ({
  jwtSecret: process.env.JWT_SECRET || 'cloudforge-jwt-secret',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
  jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
  sessionTimeout: parseInt(process.env.SESSION_TIMEOUT, 10) || 86400000, // 24 hours
  maxSessions: parseInt(process.env.MAX_SESSIONS_PER_USER, 10) || 5,
  apiKeyPrefix: process.env.API_KEY_PREFIX || 'cf_',
  apiKeyLength: parseInt(process.env.API_KEY_LENGTH, 10) || 32,
}));
