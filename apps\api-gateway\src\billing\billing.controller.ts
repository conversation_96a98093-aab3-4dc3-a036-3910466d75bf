/**
 * CloudForge Platform - Billing Controller (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { BillingService } from './billing.service';

@ApiTags('Billing')
@ApiBearerAuth('JWT-auth')
@Controller('billing')
export class BillingController {
  constructor(private readonly billingService: BillingService) {}

  @Get('subscriptions')
  @ApiOperation({ summary: 'Get user subscriptions' })
  @ApiResponse({ status: 200, description: 'Subscriptions retrieved successfully' })
  async getSubscriptions(@Query() query: any) {
    return this.billingService.getSubscriptions(query);
  }

  @Post('subscriptions')
  @ApiOperation({ summary: 'Create new subscription' })
  @ApiResponse({ status: 201, description: 'Subscription created successfully' })
  async createSubscription(@Body() subscriptionData: any) {
    return this.billingService.createSubscription(subscriptionData);
  }

  @Put('subscriptions/:id')
  @ApiOperation({ summary: 'Update subscription' })
  @ApiResponse({ status: 200, description: 'Subscription updated successfully' })
  async updateSubscription(@Param('id') id: string, @Body() updateData: any) {
    return this.billingService.updateSubscription(id, updateData);
  }

  @Delete('subscriptions/:id')
  @ApiOperation({ summary: 'Cancel subscription' })
  @ApiResponse({ status: 200, description: 'Subscription cancelled successfully' })
  async cancelSubscription(@Param('id') id: string) {
    return this.billingService.cancelSubscription(id);
  }

  @Get('plans')
  @ApiOperation({ summary: 'Get available billing plans' })
  @ApiResponse({ status: 200, description: 'Plans retrieved successfully' })
  async getPlans() {
    return this.billingService.getPlans();
  }

  @Get('invoices')
  @ApiOperation({ summary: 'Get user invoices' })
  @ApiResponse({ status: 200, description: 'Invoices retrieved successfully' })
  async getInvoices(@Query() query: any) {
    return this.billingService.getInvoices(query);
  }

  @Get('usage')
  @ApiOperation({ summary: 'Get usage statistics' })
  @ApiResponse({ status: 200, description: 'Usage statistics retrieved successfully' })
  async getUsage(@Query() query: any) {
    return this.billingService.getUsage(query);
  }

  @Post('payment-methods')
  @ApiOperation({ summary: 'Add payment method' })
  @ApiResponse({ status: 201, description: 'Payment method added successfully' })
  async addPaymentMethod(@Body() paymentMethodData: any) {
    return this.billingService.addPaymentMethod(paymentMethodData);
  }

  @Get('payment-methods')
  @ApiOperation({ summary: 'Get payment methods' })
  @ApiResponse({ status: 200, description: 'Payment methods retrieved successfully' })
  async getPaymentMethods() {
    return this.billingService.getPaymentMethods();
  }

  @Delete('payment-methods/:id')
  @ApiOperation({ summary: 'Remove payment method' })
  @ApiResponse({ status: 200, description: 'Payment method removed successfully' })
  async removePaymentMethod(@Param('id') id: string) {
    return this.billingService.removePaymentMethod(id);
  }
}
