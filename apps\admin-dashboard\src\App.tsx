/**
 * CloudForge Platform - Admin Dashboard App
 * Enterprise-grade cloud services platform
 */

import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';

import { useAuth } from './hooks/useAuth';
import { Layout } from './components/Layout';
import { LoginPage } from './pages/auth/LoginPage';
import { LoadingScreen } from './components/LoadingScreen';
import { ProtectedRoute } from './components/ProtectedRoute';

// Lazy load pages for better performance
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'));
const UsersPage = React.lazy(() => import('./pages/users/UsersPage'));
const UserDetailsPage = React.lazy(() => import('./pages/users/UserDetailsPage'));
const RolesPage = React.lazy(() => import('./pages/roles/RolesPage'));
const SubscriptionsPage = React.lazy(() => import('./pages/subscriptions/SubscriptionsPage'));
const BillingPage = React.lazy(() => import('./pages/billing/BillingPage'));
const NotificationsPage = React.lazy(() => import('./pages/notifications/NotificationsPage'));
const MonitoringPage = React.lazy(() => import('./pages/monitoring/MonitoringPage'));
const SettingsPage = React.lazy(() => import('./pages/settings/SettingsPage'));
const AuditLogsPage = React.lazy(() => import('./pages/audit/AuditLogsPage'));
const SystemHealthPage = React.lazy(() => import('./pages/system/SystemHealthPage'));
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'));

// Fallback component for Suspense
const PageLoader: React.FC = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    minHeight="400px"
  >
    <CircularProgress size={40} />
  </Box>
);

const App: React.FC = () => {
  const { isLoading, isAuthenticated } = useAuth();

  // Show loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route
        path="/login"
        element={
          isAuthenticated ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <LoginPage />
          )
        }
      />

      {/* Protected routes */}
      <Route
        path="/*"
        element={
          <ProtectedRoute>
            <Layout>
              <Suspense fallback={<PageLoader />}>
                <Routes>
                  {/* Dashboard */}
                  <Route path="/dashboard" element={<DashboardPage />} />
                  
                  {/* User Management */}
                  <Route path="/users" element={<UsersPage />} />
                  <Route path="/users/:id" element={<UserDetailsPage />} />
                  <Route path="/roles" element={<RolesPage />} />
                  
                  {/* Billing & Subscriptions */}
                  <Route path="/subscriptions" element={<SubscriptionsPage />} />
                  <Route path="/billing" element={<BillingPage />} />
                  
                  {/* Communications */}
                  <Route path="/notifications" element={<NotificationsPage />} />
                  
                  {/* Monitoring & Analytics */}
                  <Route path="/monitoring" element={<MonitoringPage />} />
                  <Route path="/audit-logs" element={<AuditLogsPage />} />
                  <Route path="/system-health" element={<SystemHealthPage />} />
                  
                  {/* Settings */}
                  <Route path="/settings" element={<SettingsPage />} />
                  
                  {/* Default redirect */}
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  
                  {/* 404 page */}
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </Suspense>
            </Layout>
          </ProtectedRoute>
        }
      />
    </Routes>
  );
};

export default App;
