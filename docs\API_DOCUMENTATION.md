# CloudForge Platform - API Documentation

## 🚀 Overview

The CloudForge Platform provides a comprehensive RESTful API for managing users, billing, notifications, and system monitoring. All APIs follow REST principles and return JSON responses.

**Base URL**: `https://api.cloudforge.com/api/v1`  
**Authentication**: Bearer <PERSON>ken (JWT)  
**Content-Type**: `application/json`

## 🔐 Authentication

### JWT Token Authentication

All API endpoints (except public ones) require authentication using JWT tokens.

```http
Authorization: Bearer <your-jwt-token>
```

### Authentication Endpoints

#### POST /auth/login
Authenticate user and receive JWT tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "johndo<PERSON>",
      "roles": ["user"]
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
  }
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### POST /auth/logout
Logout user and invalidate tokens.

#### GET /auth/me
Get current user profile.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "email": "<EMAIL>",
    "username": "johndoe",
    "firstName": "John",
    "lastName": "Doe",
    "roles": ["user"],
    "permissions": ["read:profile", "update:profile"]
  }
}
```

## 👥 User Management API

### GET /users
Get paginated list of users (Admin only).

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search term
- `role` (string): Filter by role
- `status` (string): Filter by status

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [...],
    "total": 150,
    "page": 1,
    "limit": 10,
    "totalPages": 15
  }
}
```

### GET /users/:id
Get user by ID.

### POST /users
Create new user (Admin only).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "newuser",
  "password": "securePassword123",
  "firstName": "New",
  "lastName": "User",
  "roles": ["user"]
}
```

### PUT /users/:id
Update user information.

### DELETE /users/:id
Soft delete user (Admin only).

### POST /users/:id/roles
Assign roles to user (Admin only).

**Request Body:**
```json
{
  "roles": ["user", "moderator"]
}
```

## 💳 Billing API

### GET /billing/subscriptions
Get user subscriptions.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "planId": "uuid",
      "status": "active",
      "currentPeriodStart": "2024-01-01T00:00:00Z",
      "currentPeriodEnd": "2024-02-01T00:00:00Z",
      "plan": {
        "name": "pro",
        "price": 29.99,
        "currency": "USD",
        "interval": "month"
      }
    }
  ]
}
```

### POST /billing/subscriptions
Create new subscription.

**Request Body:**
```json
{
  "planId": "uuid",
  "paymentMethodId": "pm_1234567890"
}
```

### PUT /billing/subscriptions/:id
Update subscription.

### DELETE /billing/subscriptions/:id
Cancel subscription.

### GET /billing/invoices
Get user invoices.

### GET /billing/plans
Get available billing plans.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "starter",
      "displayName": "Starter Plan",
      "price": 9.99,
      "currency": "USD",
      "interval": "month",
      "features": [
        "Up to 5 users",
        "10GB storage",
        "Email support"
      ],
      "limits": {
        "users": 5,
        "storage": 10737418240
      }
    }
  ]
}
```

## 🔔 Notifications API

### GET /notifications
Get user notifications.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `type` (string): Filter by notification type
- `read` (boolean): Filter by read status

**Response:**
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "type": "email",
        "subject": "Welcome to CloudForge",
        "content": "Thank you for joining...",
        "isRead": false,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 25,
    "unreadCount": 5
  }
}
```

### POST /notifications
Send notification (Admin only).

**Request Body:**
```json
{
  "userId": "uuid",
  "type": "email",
  "subject": "Important Update",
  "content": "We have an important update...",
  "template": "update_notification",
  "templateData": {
    "userName": "John Doe",
    "updateDetails": "..."
  }
}
```

### PUT /notifications/:id/read
Mark notification as read.

### PUT /notifications/read-all
Mark all notifications as read.

### DELETE /notifications/:id
Delete notification.

## 📊 Monitoring API

### GET /monitoring/metrics
Get system metrics.

**Query Parameters:**
- `metric` (string): Specific metric name
- `timeRange` (string): Time range (1h, 24h, 7d, 30d)
- `granularity` (string): Data granularity (1m, 5m, 1h, 1d)

**Response:**
```json
{
  "success": true,
  "data": {
    "metrics": [
      {
        "name": "api_requests_total",
        "value": 15420,
        "timestamp": "2024-01-01T00:00:00Z",
        "labels": {
          "method": "GET",
          "status": "200"
        }
      }
    ]
  }
}
```

### GET /monitoring/health
Get system health status.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "services": {
      "database": {
        "status": "healthy",
        "responseTime": 15,
        "lastCheck": "2024-01-01T00:00:00Z"
      },
      "redis": {
        "status": "healthy",
        "responseTime": 2,
        "lastCheck": "2024-01-01T00:00:00Z"
      }
    },
    "uptime": 86400,
    "version": "1.0.0"
  }
}
```

### GET /monitoring/logs
Get application logs (Admin only).

**Query Parameters:**
- `level` (string): Log level (error, warn, info, debug)
- `service` (string): Service name
- `startTime` (string): Start time (ISO 8601)
- `endTime` (string): End time (ISO 8601)
- `limit` (number): Number of logs to return

## 🛡️ Admin API

### GET /admin/users
Get all users with admin details.

### PUT /admin/users/:id/status
Update user status (activate/deactivate).

**Request Body:**
```json
{
  "status": "active",
  "reason": "Account verified"
}
```

### GET /admin/system/stats
Get system statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 1250,
      "active": 1180,
      "newThisMonth": 45
    },
    "subscriptions": {
      "total": 890,
      "active": 850,
      "revenue": 25670.50
    },
    "system": {
      "uptime": 2592000,
      "requestsToday": 45230,
      "errorRate": 0.02
    }
  }
}
```

### POST /admin/system/maintenance
Enable/disable maintenance mode.

**Request Body:**
```json
{
  "enabled": true,
  "message": "System maintenance in progress",
  "estimatedDuration": 3600
}
```

## 📝 Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "req_1234567890"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "req_1234567890"
}
```

## 🚦 HTTP Status Codes

- `200` - OK: Request successful
- `201` - Created: Resource created successfully
- `400` - Bad Request: Invalid request data
- `401` - Unauthorized: Authentication required
- `403` - Forbidden: Insufficient permissions
- `404` - Not Found: Resource not found
- `409` - Conflict: Resource already exists
- `422` - Unprocessable Entity: Validation error
- `429` - Too Many Requests: Rate limit exceeded
- `500` - Internal Server Error: Server error
- `503` - Service Unavailable: Service temporarily unavailable

## 🔄 Rate Limiting

API requests are rate limited to prevent abuse:

- **Default**: 1000 requests per 15 minutes per IP
- **Authentication**: 5 requests per minute per IP
- **Admin endpoints**: 100 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## 📚 SDKs and Libraries

### JavaScript/TypeScript SDK
```bash
npm install @cloudforge/sdk
```

```javascript
import { CloudForgeClient } from '@cloudforge/sdk';

const client = new CloudForgeClient({
  apiKey: 'your-api-key',
  baseURL: 'https://api.cloudforge.com'
});

const users = await client.users.list();
```

### Python SDK
```bash
pip install cloudforge-python
```

```python
from cloudforge import CloudForgeClient

client = CloudForgeClient(api_key='your-api-key')
users = client.users.list()
```

## 🧪 Testing

### Postman Collection
Import our Postman collection for easy API testing:
[Download Postman Collection](./postman/CloudForge-API.postman_collection.json)

### OpenAPI Specification
Full OpenAPI 3.0 specification available at:
`https://api.cloudforge.com/docs/openapi.json`

## 📞 Support

For API support and questions:
- **Documentation**: [docs.cloudforge.com](https://docs.cloudforge.com)
- **Support Email**: <EMAIL>
- **Status Page**: [status.cloudforge.com](https://status.cloudforge.com)
