/**
 * Shared types for CloudForge Platform
 * Enterprise-grade cloud services platform
 */

export type Environment = 'development' | 'staging' | 'production' | 'test';

export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'verbose';

export type UserRole = 'admin' | 'user' | 'moderator' | 'viewer' | 'billing_admin';

export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'past_due' | 'trialing';

export type NotificationType = 'email' | 'sms' | 'push' | 'webhook' | 'in_app';

export type NotificationStatus = 'pending' | 'sent' | 'failed' | 'delivered' | 'read';

export type ResourceType = 'vm' | 'storage' | 'network' | 'database' | 'container' | 'function';

export type ResourceStatus = 'running' | 'stopped' | 'pending' | 'error' | 'terminated';

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';

export type BillingInterval = 'hour' | 'day' | 'month' | 'year';

export type Currency = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CAD' | 'AUD';

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

export type SortOrder = 'ASC' | 'DESC';

export type HealthStatus = 'healthy' | 'unhealthy' | 'degraded' | 'unknown';

export type EventType = 
  | 'user.created'
  | 'user.updated'
  | 'user.deleted'
  | 'user.login'
  | 'user.logout'
  | 'subscription.created'
  | 'subscription.updated'
  | 'subscription.cancelled'
  | 'payment.completed'
  | 'payment.failed'
  | 'resource.created'
  | 'resource.updated'
  | 'resource.deleted'
  | 'notification.sent'
  | 'system.error'
  | 'security.breach';

export type PermissionAction = 'create' | 'read' | 'update' | 'delete' | 'manage' | 'execute';

export type ResourcePermission = 'users' | 'billing' | 'notifications' | 'monitoring' | 'system';

export type OAuthProvider = 'google' | 'github' | 'microsoft' | 'facebook' | 'twitter';

export type StorageProvider = 'local' | 'aws_s3' | 'gcp_storage' | 'azure_blob';

export type DatabaseProvider = 'postgresql' | 'mysql' | 'mongodb' | 'redis';

export type CloudProvider = 'aws' | 'gcp' | 'azure' | 'digitalocean' | 'linode';

export type MetricType = 'counter' | 'gauge' | 'histogram' | 'summary';

export type AlertSeverity = 'critical' | 'high' | 'medium' | 'low' | 'info';

export type DeploymentStrategy = 'rolling' | 'blue_green' | 'canary' | 'recreate';

export type ServiceStatus = 'starting' | 'running' | 'stopping' | 'stopped' | 'error';

export type ApiVersion = 'v1' | 'v2' | 'beta';

export type ContentType = 
  | 'application/json'
  | 'application/xml'
  | 'text/plain'
  | 'text/html'
  | 'multipart/form-data'
  | 'application/x-www-form-urlencoded';

export type TimeZone = 
  | 'UTC'
  | 'America/New_York'
  | 'America/Los_Angeles'
  | 'Europe/London'
  | 'Europe/Paris'
  | 'Asia/Tokyo'
  | 'Asia/Shanghai'
  | 'Australia/Sydney';

export type EncryptionAlgorithm = 'AES-256-GCM' | 'AES-256-CBC' | 'ChaCha20-Poly1305';

export type HashAlgorithm = 'bcrypt' | 'argon2' | 'scrypt' | 'pbkdf2';

export type CompressionType = 'gzip' | 'deflate' | 'br' | 'none';

export type CacheStrategy = 'lru' | 'lfu' | 'fifo' | 'ttl';

export type QueueStrategy = 'fifo' | 'lifo' | 'priority' | 'round_robin';

export type LoadBalancingStrategy = 'round_robin' | 'least_connections' | 'ip_hash' | 'weighted';

export type BackupStrategy = 'full' | 'incremental' | 'differential' | 'snapshot';

export type MonitoringProvider = 'prometheus' | 'datadog' | 'newrelic' | 'grafana';

export type LoggingProvider = 'winston' | 'bunyan' | 'pino' | 'elasticsearch';

export type TracingProvider = 'jaeger' | 'zipkin' | 'opentelemetry' | 'datadog';

// Utility types
export type Nullable<T> = T | null;

export type Optional<T> = T | undefined;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type PartialFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

export type NonEmptyArray<T> = [T, ...T[]];

export type StringKeys<T> = Extract<keyof T, string>;

export type NumberKeys<T> = Extract<keyof T, number>;

export type FunctionKeys<T> = {
  [K in keyof T]: T[K] extends Function ? K : never;
}[keyof T];

export type NonFunctionKeys<T> = {
  [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];

export type PromiseType<T> = T extends Promise<infer U> ? U : T;

export type ArrayElement<T> = T extends (infer U)[] ? U : never;

export type Constructor<T = {}> = new (...args: any[]) => T;

export type AbstractConstructor<T = {}> = abstract new (...args: any[]) => T;

export type Mixin<T extends Constructor> = InstanceType<T>;

// API Response types
export type SuccessResponse<T = any> = {
  success: true;
  data: T;
  message?: string;
  timestamp: string;
  path: string;
  statusCode: number;
};

export type ErrorResponse = {
  success: false;
  errors: string[];
  message: string;
  timestamp: string;
  path: string;
  statusCode: number;
  stack?: string;
};

export type ApiResponseType<T = any> = SuccessResponse<T> | ErrorResponse;

// Event types
export type DomainEvent<T = any> = {
  id: string;
  type: EventType;
  aggregateId: string;
  aggregateType: string;
  data: T;
  metadata?: Record<string, any>;
  timestamp: Date;
  version: number;
};

export type EventHandler<T = any> = (event: DomainEvent<T>) => Promise<void> | void;

// Configuration types
export type ConfigValue = string | number | boolean | object | null | undefined;

export type ConfigSchema = Record<string, ConfigValue>;

export type EnvironmentConfig = {
  [K in Environment]: ConfigSchema;
};

// Database types
export type EntityId = string | number;

export type TimestampFields = {
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
};

export type SoftDeleteEntity<T> = T & TimestampFields;

export type AuditFields = TimestampFields & {
  createdBy?: string;
  updatedBy?: string;
  deletedBy?: string;
};

export type AuditableEntity<T> = T & AuditFields;

// Query types
export type FilterOperator = 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like' | 'ilike';

export type QueryFilter<T> = {
  [K in keyof T]?: {
    operator: FilterOperator;
    value: any;
  };
};

export type QueryOptions<T> = {
  filters?: QueryFilter<T>;
  sort?: {
    field: keyof T;
    order: SortOrder;
  }[];
  pagination?: {
    page: number;
    limit: number;
  };
  include?: string[];
};

// Validation types
export type ValidationRule = {
  field: string;
  rules: string[];
  message?: string;
};

export type ValidationSchema = ValidationRule[];

export type ValidationError = {
  field: string;
  message: string;
  value?: any;
};

export type ValidationResult = {
  isValid: boolean;
  errors: ValidationError[];
};
