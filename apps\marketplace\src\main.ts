import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('MarketplaceService');

  // Security middleware
  app.use(helmet());
  app.use(compression());

  // CORS configuration
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // API documentation
  const config = new DocumentBuilder()
    .setTitle('CloudForge Marketplace')
    .setDescription('Enterprise Extension Marketplace & Developer Ecosystem - Created by <PERSON><PERSON>uti')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('marketplace', 'Extension marketplace management')
    .addTag('extensions', 'Extension lifecycle management')
    .addTag('developers', 'Developer community management')
    .addTag('apis', 'Open API management')
    .addTag('sdks', 'SDK distribution and management')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Global prefix
  app.setGlobalPrefix('api/v1');

  const port = configService.get('PORT', 3007);
  await app.listen(port);

  logger.log(`🏪 Marketplace Service running on port ${port}`);
  logger.log(`🌐 Developer Ecosystem by Marwan El-Qaouti`);
  logger.log(`📚 API Documentation available at http://localhost:${port}/api/docs`);
}

bootstrap().catch((error) => {
  console.error('Failed to start marketplace service:', error);
  process.exit(1);
});
