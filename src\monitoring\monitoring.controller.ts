/**
 * CloudForge Platform - Monitoring Controller
 * Provides real-time system monitoring data
 * Created by <PERSON><PERSON>
 */

import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { MonitoringService } from './monitoring.service';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { User } from '@prisma/client';

@ApiTags('Monitoring')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('monitoring')
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  @Get('performance')
  @ApiOperation({ summary: 'Get real-time performance metrics' })
  @ApiResponse({ status: 200, description: 'Performance metrics retrieved successfully' })
  async getPerformanceMetrics(@GetUser() user: User) {
    return this.monitoringService.getPerformanceMetrics();
  }

  @Get('system')
  @ApiOperation({ summary: 'Get system health metrics' })
  @ApiResponse({ status: 200, description: 'System health metrics retrieved successfully' })
  async getSystemHealth(@GetUser() user: User) {
    return this.monitoringService.getSystemHealth();
  }

  @Get('quantum')
  @ApiOperation({ summary: 'Get quantum processing metrics' })
  @ApiResponse({ status: 200, description: 'Quantum metrics retrieved successfully' })
  async getQuantumMetrics(@GetUser() user: User) {
    return this.monitoringService.getQuantumMetrics();
  }

  @Get('consciousness')
  @ApiOperation({ summary: 'Get AI consciousness metrics' })
  @ApiResponse({ status: 200, description: 'Consciousness metrics retrieved successfully' })
  async getConsciousnessMetrics(@GetUser() user: User) {
    return this.monitoringService.getConsciousnessMetrics();
  }

  @Get('security')
  @ApiOperation({ summary: 'Get security monitoring data' })
  @ApiResponse({ status: 200, description: 'Security metrics retrieved successfully' })
  async getSecurityMetrics(@GetUser() user: User) {
    return this.monitoringService.getSecurityMetrics();
  }

  @Get('alerts')
  @ApiOperation({ summary: 'Get active alerts' })
  @ApiResponse({ status: 200, description: 'Active alerts retrieved successfully' })
  async getActiveAlerts(@GetUser() user: User) {
    return this.monitoringService.getActiveAlerts();
  }

  @Get('status')
  @ApiOperation({ summary: 'Get overall system status' })
  @ApiResponse({ status: 200, description: 'System status retrieved successfully' })
  async getSystemStatus(@GetUser() user: User) {
    return this.monitoringService.getSystemStatus();
  }
}
