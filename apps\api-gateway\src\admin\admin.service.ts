/**
 * CloudForge Platform - Admin Service (API Gateway)
 * Enterprise-grade cloud services platform
 */

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);
  private readonly userServiceUrl: string;
  private readonly monitoringServiceUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.userServiceUrl = this.configService.get<string>('services.userService.url');
    this.monitoringServiceUrl = this.configService.get<string>('services.monitoringService.url');
  }

  async getAllUsers(query: any) {
    try {
      this.logger.log('Proxying get all users (admin) request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/admin/users`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get all users (admin) request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async updateUserStatus(id: string, statusData: any) {
    try {
      this.logger.log(`Proxying update user status request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.userServiceUrl}/admin/users/${id}/status`, statusData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Update user status request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getSystemStats() {
    try {
      this.logger.log('Proxying get system stats request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/admin/stats`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get system stats request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getSystemHealth() {
    try {
      this.logger.log('Proxying get system health request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/admin/health`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get system health request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async setMaintenanceMode(maintenanceData: any) {
    try {
      this.logger.log('Proxying set maintenance mode request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.monitoringServiceUrl}/admin/maintenance`, maintenanceData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Set maintenance mode request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getAuditLogs(query: any) {
    try {
      this.logger.log('Proxying get audit logs request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/admin/audit-logs`, { params: query })
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get audit logs request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getRoles() {
    try {
      this.logger.log('Proxying get roles request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/admin/roles`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get roles request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async createRole(roleData: any) {
    try {
      this.logger.log('Proxying create role request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.userServiceUrl}/admin/roles`, roleData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Create role request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async updateRole(id: string, roleData: any) {
    try {
      this.logger.log(`Proxying update role request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.userServiceUrl}/admin/roles/${id}`, roleData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Update role request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async deleteRole(id: string) {
    try {
      this.logger.log(`Proxying delete role request for ID: ${id}`);
      
      const response = await firstValueFrom(
        this.httpService.delete(`${this.userServiceUrl}/admin/roles/${id}`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Delete role request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getPermissions() {
    try {
      this.logger.log('Proxying get permissions request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.userServiceUrl}/admin/permissions`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get permissions request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getSettings() {
    try {
      this.logger.log('Proxying get settings request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/admin/settings`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get settings request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async updateSettings(settingsData: any) {
    try {
      this.logger.log('Proxying update settings request');
      
      const response = await firstValueFrom(
        this.httpService.put(`${this.monitoringServiceUrl}/admin/settings`, settingsData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Update settings request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async createBackup(backupData: any) {
    try {
      this.logger.log('Proxying create backup request');
      
      const response = await firstValueFrom(
        this.httpService.post(`${this.monitoringServiceUrl}/admin/backup`, backupData)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Create backup request failed', error.response?.data || error.message);
      throw error;
    }
  }

  async getBackups() {
    try {
      this.logger.log('Proxying get backups request');
      
      const response = await firstValueFrom(
        this.httpService.get(`${this.monitoringServiceUrl}/admin/backups`)
      );
      
      return response.data;
    } catch (error) {
      this.logger.error('Get backups request failed', error.response?.data || error.message);
      throw error;
    }
  }
}
