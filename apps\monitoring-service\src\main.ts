import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('MonitoringService');

  // Security middleware
  app.use(helmet());
  app.use(compression());

  // CORS configuration
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // API documentation
  const config = new DocumentBuilder()
    .setTitle('CloudForge Monitoring Service')
    .setDescription('Enterprise monitoring, metrics, and observability service')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('metrics', 'System and business metrics')
    .addTag('health', 'Health checks and status')
    .addTag('alerts', 'Alert management')
    .addTag('logs', 'Log management and analysis')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Global prefix
  app.setGlobalPrefix('api/v1');

  const port = configService.get('PORT', 3005);
  await app.listen(port);

  logger.log(`🚀 Monitoring Service running on port ${port}`);
  logger.log(`📚 API Documentation available at http://localhost:${port}/api/docs`);
}

bootstrap().catch((error) => {
  console.error('Failed to start monitoring service:', error);
  process.exit(1);
});
