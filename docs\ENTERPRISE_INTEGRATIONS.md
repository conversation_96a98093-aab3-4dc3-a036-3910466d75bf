# CloudForge Platform - Enterprise Integrations Guide

## 🔗 Overview

This comprehensive guide covers enterprise-grade integrations for CloudForge Platform, enabling seamless connectivity with existing enterprise systems and third-party services essential for €60M value proposition.

## 🏢 Enterprise Integration Architecture

### Integration Patterns
- **API-First**: RESTful APIs with OpenAPI 3.0 specifications
- **Event-Driven**: Asynchronous messaging with reliable delivery
- **Real-Time**: WebSocket connections for live data synchronization
- **Batch Processing**: Scheduled data synchronization and bulk operations
- **Hybrid**: Combination of real-time and batch processing

### Security & Compliance
- **End-to-End Encryption**: TLS 1.3 for all communications
- **Authentication**: OAuth2, SAML, JWT, and API keys
- **Authorization**: RBAC with fine-grained permissions
- **Audit Logging**: Comprehensive integration activity tracking
- **Data Privacy**: GDPR-compliant data handling and processing

---

## 🔐 LDAP/Active Directory Integration

### Enterprise Authentication Integration

#### Configuration Overview
```yaml
# ldap-integration-config.yml
ldap:
  enabled: true
  provider: 'active_directory' # or 'openldap', 'azure_ad'
  
  connection:
    url: 'ldaps://ad.enterprise.com:636'
    bind_dn: 'CN=CloudForge Service,OU=Service Accounts,DC=enterprise,DC=com'
    bind_password: '${LDAP_BIND_PASSWORD}'
    timeout: 30000
    pool_size: 10
    
  ssl:
    enabled: true
    verify_certificate: true
    ca_certificate_path: '/etc/ssl/certs/enterprise-ca.crt'
    
  search:
    base_dn: 'DC=enterprise,DC=com'
    user_filter: '(&(objectClass=user)(sAMAccountName={username}))'
    group_filter: '(&(objectClass=group)(member={userDN}))'
    
  attributes:
    username: 'sAMAccountName'
    email: 'mail'
    first_name: 'givenName'
    last_name: 'sn'
    display_name: 'displayName'
    groups: 'memberOf'
    employee_id: 'employeeID'
    department: 'department'
    title: 'title'
    
  synchronization:
    enabled: true
    schedule: '0 2 * * *' # Daily at 2 AM
    batch_size: 1000
    incremental: true
    
  group_mapping:
    'CN=CloudForge Admins,OU=Groups,DC=enterprise,DC=com': 'admin'
    'CN=CloudForge Users,OU=Groups,DC=enterprise,DC=com': 'user'
    'CN=CloudForge Viewers,OU=Groups,DC=enterprise,DC=com': 'viewer'
```

#### LDAP Service Implementation
```typescript
// ldap-integration.service.ts
@Injectable()
export class LDAPIntegrationService {
  private ldapClient: Client;
  
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly auditService: AuditService
  ) {
    this.initializeLDAPClient();
  }

  private initializeLDAPClient(): void {
    const config = this.configService.get('ldap');
    
    this.ldapClient = createClient({
      url: config.url,
      timeout: config.timeout,
      connectTimeout: config.timeout,
      tlsOptions: {
        rejectUnauthorized: config.ssl.verify_certificate,
        ca: config.ssl.ca_certificate_path ? 
            fs.readFileSync(config.ssl.ca_certificate_path) : undefined,
      },
    });
  }

  async authenticateUser(username: string, password: string): Promise<LDAPAuthResult> {
    try {
      // Bind with service account
      await this.bindServiceAccount();
      
      // Search for user
      const userDN = await this.findUserDN(username);
      if (!userDN) {
        throw new UnauthorizedException('User not found in LDAP');
      }
      
      // Authenticate user
      await this.ldapClient.bind(userDN, password);
      
      // Get user attributes
      const userAttributes = await this.getUserAttributes(userDN);
      
      // Get user groups
      const userGroups = await this.getUserGroups(userDN);
      
      // Map LDAP groups to application roles
      const roles = this.mapGroupsToRoles(userGroups);
      
      // Log authentication
      await this.auditService.logAuthentication({
        username,
        source: 'ldap',
        success: true,
        userDN,
        groups: userGroups,
        timestamp: new Date(),
      });
      
      return {
        success: true,
        user: {
          username: userAttributes.username,
          email: userAttributes.email,
          firstName: userAttributes.firstName,
          lastName: userAttributes.lastName,
          displayName: userAttributes.displayName,
          employeeId: userAttributes.employeeId,
          department: userAttributes.department,
          title: userAttributes.title,
        },
        roles,
        groups: userGroups,
      };
      
    } catch (error) {
      await this.auditService.logAuthentication({
        username,
        source: 'ldap',
        success: false,
        error: error.message,
        timestamp: new Date(),
      });
      
      throw new UnauthorizedException('LDAP authentication failed');
    } finally {
      await this.ldapClient.unbind();
    }
  }

  async synchronizeUsers(): Promise<SynchronizationResult> {
    const config = this.configService.get('ldap');
    const results = {
      processed: 0,
      created: 0,
      updated: 0,
      errors: 0,
      startTime: new Date(),
    };

    try {
      await this.bindServiceAccount();
      
      const searchOptions = {
        filter: config.search.user_filter.replace('{username}', '*'),
        scope: 'sub',
        attributes: Object.values(config.attributes),
        paged: true,
        sizeLimit: config.synchronization.batch_size,
      };

      const users = await this.searchLDAP(config.search.base_dn, searchOptions);
      
      for (const ldapUser of users) {
        try {
          results.processed++;
          
          const userAttributes = this.extractUserAttributes(ldapUser);
          const userGroups = await this.getUserGroups(ldapUser.dn);
          const roles = this.mapGroupsToRoles(userGroups);
          
          const existingUser = await this.userService.findByUsername(userAttributes.username);
          
          if (existingUser) {
            await this.userService.updateFromLDAP(existingUser.id, {
              ...userAttributes,
              roles,
              groups: userGroups,
              lastSyncAt: new Date(),
            });
            results.updated++;
          } else {
            await this.userService.createFromLDAP({
              ...userAttributes,
              roles,
              groups: userGroups,
              source: 'ldap',
              createdAt: new Date(),
              lastSyncAt: new Date(),
            });
            results.created++;
          }
          
        } catch (error) {
          results.errors++;
          console.error(`Error processing user ${ldapUser.dn}:`, error);
        }
      }
      
    } catch (error) {
      console.error('LDAP synchronization error:', error);
      throw error;
    } finally {
      await this.ldapClient.unbind();
      results.endTime = new Date();
      results.duration = results.endTime.getTime() - results.startTime.getTime();
    }

    await this.auditService.logSynchronization(results);
    return results;
  }

  private async bindServiceAccount(): Promise<void> {
    const config = this.configService.get('ldap');
    await this.ldapClient.bind(config.connection.bind_dn, config.connection.bind_password);
  }

  private async findUserDN(username: string): Promise<string | null> {
    const config = this.configService.get('ldap');
    const filter = config.search.user_filter.replace('{username}', username);
    
    const results = await this.searchLDAP(config.search.base_dn, {
      filter,
      scope: 'sub',
      attributes: ['dn'],
    });
    
    return results.length > 0 ? results[0].dn : null;
  }

  private async getUserAttributes(userDN: string): Promise<UserAttributes> {
    const config = this.configService.get('ldap');
    
    const results = await this.searchLDAP(userDN, {
      filter: '(objectClass=*)',
      scope: 'base',
      attributes: Object.values(config.attributes),
    });
    
    if (results.length === 0) {
      throw new Error('User not found');
    }
    
    return this.extractUserAttributes(results[0]);
  }

  private async getUserGroups(userDN: string): Promise<string[]> {
    const config = this.configService.get('ldap');
    const filter = config.search.group_filter.replace('{userDN}', userDN);
    
    const results = await this.searchLDAP(config.search.base_dn, {
      filter,
      scope: 'sub',
      attributes: ['cn', 'distinguishedName'],
    });
    
    return results.map(group => group.distinguishedName || group.dn);
  }

  private mapGroupsToRoles(groups: string[]): string[] {
    const config = this.configService.get('ldap');
    const roles = new Set<string>();
    
    for (const group of groups) {
      const role = config.group_mapping[group];
      if (role) {
        roles.add(role);
      }
    }
    
    return Array.from(roles);
  }

  private extractUserAttributes(ldapEntry: any): UserAttributes {
    const config = this.configService.get('ldap');
    
    return {
      username: this.getAttributeValue(ldapEntry, config.attributes.username),
      email: this.getAttributeValue(ldapEntry, config.attributes.email),
      firstName: this.getAttributeValue(ldapEntry, config.attributes.first_name),
      lastName: this.getAttributeValue(ldapEntry, config.attributes.last_name),
      displayName: this.getAttributeValue(ldapEntry, config.attributes.display_name),
      employeeId: this.getAttributeValue(ldapEntry, config.attributes.employee_id),
      department: this.getAttributeValue(ldapEntry, config.attributes.department),
      title: this.getAttributeValue(ldapEntry, config.attributes.title),
    };
  }

  private getAttributeValue(entry: any, attributeName: string): string {
    const value = entry[attributeName];
    return Array.isArray(value) ? value[0] : value;
  }

  private async searchLDAP(baseDN: string, options: any): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      
      this.ldapClient.search(baseDN, options, (err, res) => {
        if (err) {
          reject(err);
          return;
        }
        
        res.on('searchEntry', (entry) => {
          results.push(entry.object);
        });
        
        res.on('error', (err) => {
          reject(err);
        });
        
        res.on('end', (result) => {
          if (result.status === 0) {
            resolve(results);
          } else {
            reject(new Error(`LDAP search failed with status ${result.status}`));
          }
        });
      });
    });
  }
}

// Types
interface LDAPAuthResult {
  success: boolean;
  user: UserAttributes;
  roles: string[];
  groups: string[];
}

interface UserAttributes {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  employeeId?: string;
  department?: string;
  title?: string;
}

interface SynchronizationResult {
  processed: number;
  created: number;
  updated: number;
  errors: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
}
```

#### LDAP Authentication Controller
```typescript
// ldap-auth.controller.ts
@Controller('auth/ldap')
@ApiTags('LDAP Authentication')
export class LDAPAuthController {
  constructor(
    private readonly ldapService: LDAPIntegrationService,
    private readonly authService: AuthService,
    private readonly userService: UserService
  ) {}

  @Post('login')
  @ApiOperation({ summary: 'Authenticate user via LDAP' })
  @ApiBody({ type: LDAPLoginDto })
  @ApiResponse({ status: 200, description: 'Authentication successful' })
  @ApiResponse({ status: 401, description: 'Authentication failed' })
  async login(@Body() loginDto: LDAPLoginDto): Promise<AuthResponse> {
    const ldapResult = await this.ldapService.authenticateUser(
      loginDto.username,
      loginDto.password
    );

    if (!ldapResult.success) {
      throw new UnauthorizedException('LDAP authentication failed');
    }

    // Create or update user in local database
    let user = await this.userService.findByUsername(ldapResult.user.username);
    
    if (!user) {
      user = await this.userService.createFromLDAP({
        ...ldapResult.user,
        roles: ldapResult.roles,
        groups: ldapResult.groups,
        source: 'ldap',
      });
    } else {
      user = await this.userService.updateFromLDAP(user.id, {
        ...ldapResult.user,
        roles: ldapResult.roles,
        groups: ldapResult.groups,
        lastLoginAt: new Date(),
      });
    }

    // Generate JWT tokens
    const tokens = await this.authService.generateTokens(user);

    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roles: user.roles,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: tokens.expiresIn,
    };
  }

  @Post('sync')
  @ApiOperation({ summary: 'Synchronize users from LDAP' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async syncUsers(): Promise<SynchronizationResult> {
    return this.ldapService.synchronizeUsers();
  }

  @Get('test-connection')
  @ApiOperation({ summary: 'Test LDAP connection' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      await this.ldapService.testConnection();
      return {
        success: true,
        message: 'LDAP connection successful',
      };
    } catch (error) {
      return {
        success: false,
        message: `LDAP connection failed: ${error.message}`,
      };
    }
  }
}

// DTOs
class LDAPLoginDto {
  @ApiProperty({ example: 'john.doe' })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({ example: 'password123' })
  @IsString()
  @IsNotEmpty()
  password: string;
}

interface AuthResponse {
  success: boolean;
  user: {
    id: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    roles: string[];
  };
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}
```

---

## 🛡️ SIEM Integration (Security Information and Event Management)

### Enterprise Security Monitoring Integration

#### SIEM Configuration
```yaml
# siem-integration-config.yml
siem:
  enabled: true
  providers:
    - name: 'splunk'
      type: 'splunk_enterprise'
      endpoint: 'https://splunk.enterprise.com:8088/services/collector'
      token: '${SPLUNK_HEC_TOKEN}'
      index: 'cloudforge_security'
      source: 'cloudforge_platform'

    - name: 'elastic_siem'
      type: 'elasticsearch'
      endpoint: 'https://elastic.enterprise.com:9200'
      username: 'cloudforge_siem'
      password: '${ELASTIC_SIEM_PASSWORD}'
      index: 'cloudforge-security-logs'

    - name: 'qradar'
      type: 'ibm_qradar'
      endpoint: 'https://qradar.enterprise.com/api/siem'
      token: '${QRADAR_API_TOKEN}'

  log_levels:
    - 'security'
    - 'authentication'
    - 'authorization'
    - 'data_access'
    - 'configuration_change'
    - 'system_event'

  real_time:
    enabled: true
    batch_size: 100
    flush_interval: 5000 # 5 seconds

  enrichment:
    geo_location: true
    threat_intelligence: true
    user_context: true
    asset_context: true
```

#### SIEM Service Implementation
```typescript
// siem-integration.service.ts
@Injectable()
export class SIEMIntegrationService {
  private readonly logger = new Logger(SIEMIntegrationService.name);
  private eventQueue: SecurityEvent[] = [];
  private flushTimer: NodeJS.Timeout;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly geoService: GeoLocationService,
    private readonly threatIntelService: ThreatIntelligenceService
  ) {
    this.initializeEventProcessing();
  }

  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      // Enrich event with additional context
      const enrichedEvent = await this.enrichEvent(event);

      // Add to queue for batch processing
      this.eventQueue.push(enrichedEvent);

      // Immediate send for critical events
      if (event.severity === 'critical' || event.severity === 'high') {
        await this.sendImmediateAlert(enrichedEvent);
      }

    } catch (error) {
      this.logger.error(`Failed to log security event: ${error.message}`, error.stack);
    }
  }

  private async enrichEvent(event: SecurityEvent): Promise<EnrichedSecurityEvent> {
    const config = this.configService.get('siem');
    const enriched: EnrichedSecurityEvent = {
      ...event,
      timestamp: new Date().toISOString(),
      platform: 'cloudforge',
      version: this.configService.get('app.version'),
      environment: this.configService.get('app.environment'),
    };

    // Geo-location enrichment
    if (config.enrichment.geo_location && event.sourceIP) {
      try {
        enriched.geoLocation = await this.geoService.lookup(event.sourceIP);
      } catch (error) {
        this.logger.warn(`Geo-location lookup failed for IP ${event.sourceIP}`);
      }
    }

    // Threat intelligence enrichment
    if (config.enrichment.threat_intelligence && event.sourceIP) {
      try {
        enriched.threatIntelligence = await this.threatIntelService.checkIP(event.sourceIP);
      } catch (error) {
        this.logger.warn(`Threat intelligence lookup failed for IP ${event.sourceIP}`);
      }
    }

    // User context enrichment
    if (config.enrichment.user_context && event.userId) {
      try {
        enriched.userContext = await this.getUserContext(event.userId);
      } catch (error) {
        this.logger.warn(`User context lookup failed for user ${event.userId}`);
      }
    }

    return enriched;
  }

  private initializeEventProcessing(): void {
    const config = this.configService.get('siem');

    if (config.real_time.enabled) {
      this.flushTimer = setInterval(() => {
        this.flushEventQueue();
      }, config.real_time.flush_interval);
    }
  }

  private async flushEventQueue(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const config = this.configService.get('siem');
    const events = this.eventQueue.splice(0, config.real_time.batch_size);

    const providers = config.providers;

    for (const provider of providers) {
      try {
        await this.sendToProvider(provider, events);
      } catch (error) {
        this.logger.error(`Failed to send events to ${provider.name}: ${error.message}`);
        // Re-queue events for retry
        this.eventQueue.unshift(...events);
      }
    }
  }

  private async sendToProvider(provider: any, events: EnrichedSecurityEvent[]): Promise<void> {
    switch (provider.type) {
      case 'splunk_enterprise':
        await this.sendToSplunk(provider, events);
        break;
      case 'elasticsearch':
        await this.sendToElasticsearch(provider, events);
        break;
      case 'ibm_qradar':
        await this.sendToQRadar(provider, events);
        break;
      default:
        this.logger.warn(`Unknown SIEM provider type: ${provider.type}`);
    }
  }

  private async sendToSplunk(provider: any, events: EnrichedSecurityEvent[]): Promise<void> {
    const payload = events.map(event => ({
      time: new Date(event.timestamp).getTime() / 1000,
      index: provider.index,
      source: provider.source,
      sourcetype: 'cloudforge:security',
      event: event,
    }));

    await this.httpService.post(provider.endpoint, payload, {
      headers: {
        'Authorization': `Splunk ${provider.token}`,
        'Content-Type': 'application/json',
      },
    }).toPromise();
  }

  private async sendToElasticsearch(provider: any, events: EnrichedSecurityEvent[]): Promise<void> {
    const body = events.flatMap(event => [
      { index: { _index: provider.index, _type: '_doc' } },
      event,
    ]);

    await this.httpService.post(`${provider.endpoint}/_bulk`, body, {
      headers: {
        'Content-Type': 'application/x-ndjson',
      },
      auth: {
        username: provider.username,
        password: provider.password,
      },
    }).toPromise();
  }

  private async sendToQRadar(provider: any, events: EnrichedSecurityEvent[]): Promise<void> {
    for (const event of events) {
      const qradarEvent = this.convertToQRadarFormat(event);

      await this.httpService.post(`${provider.endpoint}/events`, qradarEvent, {
        headers: {
          'SEC': provider.token,
          'Content-Type': 'application/json',
        },
      }).toPromise();
    }
  }

  private async sendImmediateAlert(event: EnrichedSecurityEvent): Promise<void> {
    // Send immediate alert for critical events
    const alertPayload = {
      alert_type: 'security_incident',
      severity: event.severity,
      title: event.eventType,
      description: event.description,
      source: 'CloudForge Platform',
      timestamp: event.timestamp,
      details: event,
    };

    // Send to all configured SIEM providers immediately
    const config = this.configService.get('siem');
    for (const provider of config.providers) {
      try {
        await this.sendAlertToProvider(provider, alertPayload);
      } catch (error) {
        this.logger.error(`Failed to send immediate alert to ${provider.name}: ${error.message}`);
      }
    }
  }

  private convertToQRadarFormat(event: EnrichedSecurityEvent): any {
    return {
      message: event.description,
      severity: this.mapSeverityToQRadar(event.severity),
      category: event.category,
      source_ip: event.sourceIP,
      destination_ip: event.destinationIP,
      username: event.username,
      event_time: new Date(event.timestamp).getTime(),
      custom_properties: {
        platform: event.platform,
        event_type: event.eventType,
        user_id: event.userId,
        session_id: event.sessionId,
        tenant_id: event.tenantId,
      },
    };
  }

  private mapSeverityToQRadar(severity: string): number {
    const mapping = {
      'low': 3,
      'medium': 5,
      'high': 7,
      'critical': 9,
    };
    return mapping[severity] || 5;
  }

  private async getUserContext(userId: string): Promise<UserContext> {
    // Implement user context lookup
    return {
      userId,
      username: 'user.username',
      roles: ['user.roles'],
      department: 'user.department',
      lastLogin: 'user.lastLoginAt',
    };
  }
}

// Security Event Types
interface SecurityEvent {
  eventType: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  sourceIP?: string;
  destinationIP?: string;
  username?: string;
  userId?: string;
  sessionId?: string;
  tenantId?: string;
  userAgent?: string;
  requestId?: string;
  resource?: string;
  action?: string;
  outcome?: 'success' | 'failure';
  additionalData?: any;
}

interface EnrichedSecurityEvent extends SecurityEvent {
  timestamp: string;
  platform: string;
  version: string;
  environment: string;
  geoLocation?: GeoLocation;
  threatIntelligence?: ThreatIntelligence;
  userContext?: UserContext;
}

interface GeoLocation {
  country: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
  isp: string;
  organization: string;
}

interface ThreatIntelligence {
  isMalicious: boolean;
  threatTypes: string[];
  reputation: number;
  sources: string[];
  lastSeen?: string;
}

interface UserContext {
  userId: string;
  username: string;
  roles: string[];
  department: string;
  lastLogin: string;
}
```

This SIEM integration provides:

1. **Multi-Provider Support**: Splunk, Elasticsearch, IBM QRadar, and other SIEM platforms
2. **Real-Time Monitoring**: Immediate alerts for critical security events
3. **Event Enrichment**: Geo-location, threat intelligence, and user context
4. **Batch Processing**: Efficient bulk event transmission
5. **Comprehensive Logging**: All security-relevant events tracked
6. **Enterprise Standards**: Compatible with major enterprise SIEM solutions

The SIEM integration is essential for enterprise environments requiring comprehensive security monitoring and compliance with regulations like SOX, PCI DSS, and GDPR.
