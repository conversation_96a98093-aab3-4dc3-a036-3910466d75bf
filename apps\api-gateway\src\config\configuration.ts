/**
 * CloudForge Platform - Configuration
 * Enterprise-grade cloud services platform
 */

export const configuration = () => ({
  // Application
  app: {
    name: process.env.APP_NAME || 'CloudForge Platform',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.API_PORT, 10) || 3000,
    prefix: process.env.API_PREFIX || 'api/v1',
    logLevel: process.env.LOG_LEVEL || 'debug',
  },

  // Database
  database: {
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
    username: process.env.DATABASE_USERNAME || 'cloudforge',
    password: process.env.DATABASE_PASSWORD || 'password',
    name: process.env.DATABASE_NAME || 'cloudforge_platform',
    ssl: process.env.DATABASE_SSL === 'true',
    poolSize: parseInt(process.env.DATABASE_POOL_SIZE, 10) || 10,
    timeout: parseInt(process.env.DATABASE_TIMEOUT, 10) || 30000,
  },

  // Redis
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'cloudforge:',
    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY, 10) || 100,
    maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES, 10) || 3,
  },

  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    algorithm: 'HS256',
    issuer: 'cloudforge-platform',
    audience: 'cloudforge-users',
  },

  // OAuth2
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackUrl: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback',
      scope: ['email', 'profile'],
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
      callbackUrl: process.env.GITHUB_CALLBACK_URL || 'http://localhost:3000/auth/github/callback',
      scope: ['user:email'],
    },
  },

  // Security
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'your-session-secret',
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT, 10) || 3600000, // 1 hour
    maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS, 10) || 5,
    lockoutDuration: parseInt(process.env.LOCKOUT_DURATION, 10) || 900000, // 15 minutes
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-32-character-encryption-key',
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW, 10) || 15 * 60 * 1000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
  },

  // CORS
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  },

  // Email
  email: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT, 10) || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
    from: process.env.EMAIL_FROM || '<EMAIL>',
    templates: {
      path: process.env.EMAIL_TEMPLATES_PATH || './templates/email',
    },
  },

  // File Upload
  upload: {
    maxSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 10 * 1024 * 1024, // 10MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,application/pdf').split(','),
    destination: process.env.UPLOAD_DESTINATION || './uploads',
    storageType: process.env.STORAGE_TYPE || 'local',
  },

  // Monitoring
  monitoring: {
    enabled: process.env.ENABLE_MONITORING !== 'false',
    prometheusPort: parseInt(process.env.PROMETHEUS_PORT, 10) || 9090,
    grafanaPort: parseInt(process.env.GRAFANA_PORT, 10) || 3000,
    healthCheckPath: process.env.HEALTH_CHECK_PATH || '/health',
    metricsPath: process.env.METRICS_PATH || '/metrics',
    jaegerEndpoint: process.env.JAEGER_ENDPOINT,
  },

  // Billing
  billing: {
    stripeSecretKey: process.env.STRIPE_SECRET_KEY,
    stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    currency: process.env.BILLING_CURRENCY || 'USD',
    defaultPlan: process.env.DEFAULT_PLAN || 'free',
  },

  // Feature Flags
  features: {
    registration: process.env.ENABLE_REGISTRATION !== 'false',
    oauth: process.env.ENABLE_OAUTH !== 'false',
    billing: process.env.ENABLE_BILLING !== 'false',
    monitoring: process.env.ENABLE_MONITORING !== 'false',
    notifications: process.env.ENABLE_NOTIFICATIONS !== 'false',
  },

  // External Services
  services: {
    authService: {
      url: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
      timeout: parseInt(process.env.AUTH_SERVICE_TIMEOUT, 10) || 30000,
    },
    userService: {
      url: process.env.USER_SERVICE_URL || 'http://localhost:3002',
      timeout: parseInt(process.env.USER_SERVICE_TIMEOUT, 10) || 30000,
    },
    billingService: {
      url: process.env.BILLING_SERVICE_URL || 'http://localhost:3003',
      timeout: parseInt(process.env.BILLING_SERVICE_TIMEOUT, 10) || 30000,
    },
    notificationService: {
      url: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:3004',
      timeout: parseInt(process.env.NOTIFICATION_SERVICE_TIMEOUT, 10) || 30000,
    },
    monitoringService: {
      url: process.env.MONITORING_SERVICE_URL || 'http://localhost:3005',
      timeout: parseInt(process.env.MONITORING_SERVICE_TIMEOUT, 10) || 30000,
    },
  },

  // Cloud Provider
  cloud: {
    provider: process.env.CLOUD_PROVIDER || 'aws',
    region: process.env.CLOUD_REGION || 'us-east-1',
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1',
    },
    gcp: {
      projectId: process.env.GCP_PROJECT_ID,
      keyFilename: process.env.GCP_KEY_FILENAME,
      region: process.env.GCP_REGION || 'us-central1',
    },
    azure: {
      subscriptionId: process.env.AZURE_SUBSCRIPTION_ID,
      tenantId: process.env.AZURE_TENANT_ID,
      clientId: process.env.AZURE_CLIENT_ID,
      clientSecret: process.env.AZURE_CLIENT_SECRET,
      region: process.env.AZURE_REGION || 'East US',
    },
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'debug',
    format: process.env.LOG_FORMAT || 'json',
    file: {
      enabled: process.env.LOG_FILE_ENABLED === 'true',
      filename: process.env.LOG_FILENAME || 'app.log',
      maxSize: process.env.LOG_MAX_SIZE || '20m',
      maxFiles: parseInt(process.env.LOG_MAX_FILES, 10) || 5,
    },
    elasticsearch: {
      enabled: process.env.ELASTICSEARCH_ENABLED === 'true',
      host: process.env.ELASTICSEARCH_HOST || 'localhost:9200',
      index: process.env.ELASTICSEARCH_INDEX || 'cloudforge-logs',
    },
  },

  // Cache
  cache: {
    ttl: parseInt(process.env.CACHE_TTL, 10) || 300, // 5 minutes
    max: parseInt(process.env.CACHE_MAX, 10) || 1000,
    updateAgeOnGet: process.env.CACHE_UPDATE_AGE_ON_GET === 'true',
  },

  // Queue
  queue: {
    redis: {
      host: process.env.QUEUE_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.QUEUE_REDIS_PORT, 10) || parseInt(process.env.REDIS_PORT, 10) || 6379,
      password: process.env.QUEUE_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
      db: parseInt(process.env.QUEUE_REDIS_DB, 10) || 3,
    },
    defaultJobOptions: {
      removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE, 10) || 10,
      removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL, 10) || 5,
      attempts: parseInt(process.env.QUEUE_ATTEMPTS, 10) || 3,
      backoff: {
        type: 'exponential',
        delay: parseInt(process.env.QUEUE_BACKOFF_DELAY, 10) || 2000,
      },
    },
  },
});
