{"name": "api-gateway", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api-gateway/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/api-gateway", "main": "apps/api-gateway/src/main.ts", "tsConfig": "apps/api-gateway/tsconfig.app.json", "assets": ["apps/api-gateway/src/assets"], "isolatedConfig": true, "webpackConfig": "apps/api-gateway/webpack.config.js"}, "configurations": {"development": {"optimization": false, "extractLicenses": false, "inspect": false, "fileReplacements": [{"replace": "apps/api-gateway/src/environments/environment.ts", "with": "apps/api-gateway/src/environments/environment.development.ts"}]}, "production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/api-gateway/src/environments/environment.ts", "with": "apps/api-gateway/src/environments/environment.production.ts"}]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "api-gateway:build"}, "configurations": {"development": {"buildTarget": "api-gateway:build:development"}, "production": {"buildTarget": "api-gateway:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/api-gateway/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/api-gateway/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}, "docker-build": {"executor": "nx:run-commands", "options": {"command": "docker build -f apps/api-gateway/Dockerfile -t cloudforge-api-gateway:latest ."}}, "docker-run": {"executor": "nx:run-commands", "options": {"command": "docker run -p 3000:3000 cloudforge-api-gateway:latest"}}}, "tags": ["scope:api-gateway", "type:app"]}