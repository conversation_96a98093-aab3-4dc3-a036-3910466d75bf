# Enterprise Cloud Platform

A comprehensive, enterprise-grade cloud services platform built with NestJS, TypeScript, and modern DevOps practices.

## 🚀 Features

- **Authentication & Authorization**: JWT + OAuth2 + RBAC
- **Database**: PostgreSQL with TypeORM + Redis caching
- **API Documentation**: OpenAPI 3.1 + Swagger
- **Testing**: Unit, Integration, E2E, and Load testing
- **Security**: Helmet, input validation, security scanning
- **Monitoring**: Prometheus + Grafana + OpenTelemetry
- **Containerization**: Docker + Kubernetes + Helm
- **CI/CD**: GitHub Actions with automated testing and deployment

## 📋 Prerequisites

- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

## 🛠️ Quick Start

### Local Development

1. **Clone and install dependencies**
```bash
git clone <repository-url>
cd enterprise-cloud-platform
npm install
```

2. **Setup environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start services with Docker**
```bash
docker-compose up -d
```

4. **Run database migrations**
```bash
npm run migration:run
```

5. **Start development server**
```bash
npm run dev
```

The API will be available at `http://localhost:3000`
Swagger documentation at `http://localhost:3000/docs`

### Production Deployment

#### Using Docker
```bash
docker build -t enterprise-platform .
docker run -p 3000:3000 enterprise-platform
```

#### Using Kubernetes
```bash
kubectl apply -f infra/k8s/
```

#### Using Helm
```bash
helm install enterprise-platform ./infra/helm/
```

## 🧪 Testing

```bash
# Unit tests
npm run test

# Integration tests
npm run test:e2e

# Load tests
npm run test:load

# Coverage report
npm run test:coverage
```

## 📊 Monitoring

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001 (admin/admin)
- **Health Check**: http://localhost:3000/health

## 🔧 Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run test` - Run all tests
- `npm run lint` - Lint code
- `npm run format` - Format code
- `./scripts/deploy.sh` - Deploy to production

## 📁 Project Structure

```
├── apps/
│   └── api/                 # Main API application
├── libs/
│   ├── common/             # Shared utilities
│   └── config/             # Configuration modules
├── infra/
│   ├── k8s/               # Kubernetes manifests
│   ├── helm/              # Helm charts
│   └── terraform/         # Infrastructure as code
├── tests/
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── e2e/              # End-to-end tests
├── docs/                  # Documentation
└── scripts/              # Deployment scripts
```

## 🔐 Security

- JWT authentication with refresh tokens
- OAuth2 integration (Google)
- Role-based access control (RBAC)
- Input validation and sanitization
- Security headers with Helmet
- Automated security scanning

## 📈 Performance

- Database connection pooling
- Redis caching
- Horizontal scaling with Kubernetes
- Load balancing
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details