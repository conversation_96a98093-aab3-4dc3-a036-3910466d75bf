# CloudForge Platform 🚀

[![Build Status](https://github.com/cloudforge/platform/workflows/CI/badge.svg)](https://github.com/cloudforge/platform/actions)
[![Coverage](https://img.shields.io/badge/coverage-95%25-brightgreen.svg)](./coverage)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](./LICENSE)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.2-blue.svg)](https://www.typescriptlang.org/)
[![NestJS](https://img.shields.io/badge/NestJS-10.2-red.svg)](https://nestjs.com/)

**Enterprise-grade cloud services platform with microservices architecture, ready for production deployment and commercial licensing.**

## 🎯 Overview

CloudForge Platform is a comprehensive, enterprise-ready cloud services platform built with modern technologies and best practices. It provides a complete infrastructure for building, deploying, and managing cloud applications with built-in security, monitoring, and scalability.

### 🏗️ Architecture Highlights

- **Microservices Architecture**: Modular, scalable services built with NestJS
- **Enterprise Security**: JWT + OAuth2 + RBAC with comprehensive audit trails
- **Cloud-Native**: Kubernetes-ready with Helm charts and Terraform IaC
- **Observability**: Prometheus, Grafana, and OpenTelemetry integration
- **High Availability**: Redis clustering, PostgreSQL replication
- **Developer Experience**: Complete CI/CD, testing, and documentation

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- Docker and Docker Compose
- Kubernetes cluster (optional, for production)
- PostgreSQL 14+ and Redis 6+

### Local Development Setup

```bash
# Clone the repository
git clone https://github.com/cloudforge/platform.git
cd platform

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Start infrastructure services
docker-compose up -d postgres redis

# Run database migrations
npm run db:migrate

# Start all services in development mode
npm run dev

# Access the platform
# API Gateway: http://localhost:3000
# Admin Dashboard: http://localhost:3001
# Swagger Documentation: http://localhost:3000/api/docs
```

### Production Deployment

```bash
# Build and deploy with Docker Compose
npm run docker:build
npm run docker:up

# Or deploy to Kubernetes
npm run k8s:deploy

# Or use Terraform for cloud deployment
npm run terraform:init
npm run terraform:apply
```

## 📦 Platform Components

### Core Services

| Service | Port | Description |
|---------|------|-------------|
| **API Gateway** | 3000 | Unified entry point with rate limiting and authentication |
| **Auth Service** | 3001 | JWT + OAuth2 authentication with RBAC |
| **User Service** | 3002 | User management and profile operations |
| **Billing Service** | 3003 | Subscription management and payment processing |
| **Notification Service** | 3004 | Email, SMS, and webhook notifications |
| **Monitoring Service** | 3005 | System metrics, logging, and health checks |

### Frontend Applications

| Application | Port | Description |
|-------------|------|-------------|
| **Admin Dashboard** | 3001 | React-based administrative interface |
| **User Portal** | 3002 | Customer-facing web application |

### Infrastructure

| Component | Port | Description |
|-----------|------|-------------|
| **PostgreSQL** | 5432 | Primary relational database |
| **Redis** | 6379 | Caching and session storage |
| **Prometheus** | 9090 | Metrics collection and alerting |
| **Grafana** | 3000 | Metrics visualization and dashboards |

## 🛠️ Technology Stack

### Backend
- **Framework**: NestJS 10.2 with TypeScript 5.2
- **Database**: PostgreSQL 14+ with TypeORM
- **Cache**: Redis 6+ with clustering support
- **Authentication**: JWT + Passport + OAuth2 (Google, GitHub)
- **API Documentation**: OpenAPI 3.1 + Swagger UI
- **Validation**: Class Validator + Class Transformer

### Frontend
- **Framework**: React 18 with TypeScript
- **State Management**: Redux Toolkit + RTK Query
- **UI Components**: Material-UI + Custom Design System
- **Build Tool**: Webpack 5 with Hot Module Replacement

### DevOps & Infrastructure
- **Containerization**: Docker + Docker Compose
- **Orchestration**: Kubernetes + Helm Charts
- **Infrastructure as Code**: Terraform
- **CI/CD**: GitHub Actions
- **Monitoring**: Prometheus + Grafana + OpenTelemetry
- **Security**: Snyk + Semgrep + TruffleHog

### Testing
- **Unit Testing**: Jest with 95%+ coverage
- **Integration Testing**: Supertest
- **E2E Testing**: Cypress
- **Load Testing**: k6
- **Security Testing**: OWASP ZAP

## 🔐 Security Features

- **Multi-Factor Authentication**: TOTP and SMS support
- **Role-Based Access Control**: Granular permissions system
- **API Security**: Rate limiting, CORS, helmet protection
- **Data Encryption**: AES-256 encryption for sensitive data
- **Audit Logging**: Comprehensive activity tracking
- **Security Scanning**: Automated vulnerability detection
- **Compliance**: SOC 2, GDPR, and HIPAA ready

## 📊 Monitoring & Observability

- **Application Metrics**: Custom business metrics with Prometheus
- **Infrastructure Monitoring**: System resources and health checks
- **Distributed Tracing**: Request flow tracking with OpenTelemetry
- **Log Aggregation**: Structured logging with Winston
- **Alerting**: Configurable alerts for critical events
- **Dashboards**: Pre-built Grafana dashboards for all services

## 🧪 Testing Strategy

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e

# Run load tests
npm run test:load

# Security scanning
npm run security:scan
```

## 📚 Documentation

- [**API Reference**](./docs/api/README.md) - Complete API documentation
- [**Architecture Guide**](./docs/architecture/README.md) - System design and patterns
- [**Deployment Guide**](./docs/deployment/README.md) - Production deployment instructions
- [**Developer Guide**](./docs/development/README.md) - Development setup and guidelines
- [**Security Guide**](./docs/security/README.md) - Security best practices
- [**Operations Manual**](./docs/operations/README.md) - Day-to-day operations guide

## 🚀 Deployment Options

### Cloud Providers
- **AWS**: EKS + RDS + ElastiCache
- **Google Cloud**: GKE + Cloud SQL + Memorystore
- **Azure**: AKS + Azure Database + Redis Cache
- **DigitalOcean**: Kubernetes + Managed Databases

### On-Premises
- **Kubernetes**: Self-managed clusters
- **Docker Swarm**: Simplified container orchestration
- **Bare Metal**: Direct server deployment

## 🔧 Configuration

The platform uses environment-based configuration with validation:

```bash
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=cloudforge

# Authentication
JWT_SECRET=your-secret-key
GOOGLE_CLIENT_ID=your-google-client-id

# Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
```

See [.env.example](./.env.example) for complete configuration options.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure all tests pass: `npm test`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🏢 Commercial Licensing

CloudForge Platform is available for commercial licensing. Contact us for enterprise licensing options, support, and custom development.

**Contact**: <EMAIL>

## 🆘 Support

- **Documentation**: [docs.cloudforge.com](https://docs.cloudforge.com)
- **Community**: [GitHub Discussions](https://github.com/cloudforge/platform/discussions)
- **Issues**: [GitHub Issues](https://github.com/cloudforge/platform/issues)
- **Enterprise Support**: <EMAIL>

## 🎖️ Acknowledgments

Built with ❤️ by the CloudForge Engineering Team

- Modern architecture patterns and best practices
- Open source community contributions
- Enterprise-grade security and reliability standards

---

**Ready to deploy your cloud platform? Get started with CloudForge today!** 🚀

- User and Identity Management (IAM)
- Virtual Services (VMs, Storage, Networking, Containers)
- Billing and Usage Metering
- Integrated Security (encryption, permissions, roles)
- Unified API Gateway
- Web Admin Panel
- Modular, scalable, and fully documented

## Project Structure

- `backend/` - Microservices (IAM, Resource, Billing, Gateway)
- `frontend/` - Admin web panel (React)
- `infra/` - Infrastructure as Code (Terraform, Kubernetes)
- `docs/` - Technical documentation, architecture, API reference
- `tests/` - Unit, integration, load, and security tests

## Quick Start

See [docs/INSTALLATION.md](docs/INSTALLATION.md) for setup instructions.

## Documentation

- [Installation Guide](docs/INSTALLATION.md)
- [Deployment Guide](docs/DEPLOYMENT.md)
- [Architecture](docs/ARCHITECTURE.md)
- [API Reference](docs/API_REFERENCE.md)
- [Developer Guide](docs/DEVELOPER_GUIDE.md)
- [Security Notes](docs/SECURITY.md)
- [Operation Manual](docs/OPERATION.md)
- [Comparison](docs/COMPARISON.md)
- [Use Case Example](docs/USE_CASE.md)

## Contribution

See [docs/DEVELOPER_GUIDE.md](docs/DEVELOPER_GUIDE.md).

## Security

See [docs/SECURITY.md](docs/SECURITY.md).

## Operation

See [docs/OPERATION.md](docs/OPERATION.md).