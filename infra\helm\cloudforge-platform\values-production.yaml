# CloudForge Platform - Production Helm Values
# Enterprise-grade configuration for production deployment

# Global Configuration
global:
  imageRegistry: "cloudforge.azurecr.io"
  imagePullSecrets:
    - name: cloudforge-registry-secret
  storageClass: "gp3"
  
# Environment Configuration
environment: production
replicaCount: 3

# Image Configuration
image:
  registry: cloudforge.azurecr.io
  pullPolicy: IfNotPresent
  tag: "1.0.0"

# API Gateway Configuration
apiGateway:
  enabled: true
  replicaCount: 6
  image:
    repository: cloudforge/api-gateway
    tag: "1.0.0"
  
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
  
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      nginx.ingress.kubernetes.io/rate-limit: "1000"
    hosts:
      - host: api.cloudforge.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: cloudforge-api-tls
        hosts:
          - api.cloudforge.com
  
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  
  autoscaling:
    enabled: true
    minReplicas: 6
    maxReplicas: 50
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    NODE_ENV: production
    LOG_LEVEL: info
    RATE_LIMIT_WINDOW_MS: 900000
    RATE_LIMIT_MAX_REQUESTS: 1000

# Authentication Service Configuration
authService:
  enabled: true
  replicaCount: 4
  image:
    repository: cloudforge/auth-service
    tag: "1.0.0"
  
  service:
    type: ClusterIP
    port: 3001
    targetPort: 3001
  
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 4
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
  
  env:
    NODE_ENV: production
    JWT_EXPIRATION: "1h"
    REFRESH_TOKEN_EXPIRATION: "7d"
    MFA_ENABLED: "true"
    SESSION_TIMEOUT: "30m"

# User Service Configuration
userService:
  enabled: true
  replicaCount: 4
  image:
    repository: cloudforge/user-service
    tag: "1.0.0"
  
  service:
    type: ClusterIP
    port: 3002
    targetPort: 3002
  
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 4
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70

# Billing Service Configuration
billingService:
  enabled: true
  replicaCount: 3
  image:
    repository: cloudforge/billing-service
    tag: "1.0.0"
  
  service:
    type: ClusterIP
    port: 3003
    targetPort: 3003
  
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 15
    targetCPUUtilizationPercentage: 70

# Notification Service Configuration
notificationService:
  enabled: true
  replicaCount: 3
  image:
    repository: cloudforge/notification-service
    tag: "1.0.0"
  
  service:
    type: ClusterIP
    port: 3004
    targetPort: 3004
  
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 512Mi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 12
    targetCPUUtilizationPercentage: 70

# Monitoring Service Configuration
monitoringService:
  enabled: true
  replicaCount: 2
  image:
    repository: cloudforge/monitoring-service
    tag: "1.0.0"
  
  service:
    type: ClusterIP
    port: 3005
    targetPort: 3005
  
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi

# Admin Dashboard Configuration
adminDashboard:
  enabled: true
  replicaCount: 3
  image:
    repository: cloudforge/admin-dashboard
    tag: "1.0.0"
  
  service:
    type: ClusterIP
    port: 80
    targetPort: 80
  
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/auth-url: "https://auth.cloudforge.com/auth"
    hosts:
      - host: admin.cloudforge.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: cloudforge-admin-tls
        hosts:
          - admin.cloudforge.com
  
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 512Mi

# Database Configuration
postgresql:
  enabled: false  # Using external RDS
  external:
    enabled: true
    host: "cloudforge-postgres.cluster-xyz.us-east-1.rds.amazonaws.com"
    port: 5432
    database: "cloudforge"
    username: "cloudforge_admin"
    existingSecret: "cloudforge-db-secret"
    existingSecretPasswordKey: "password"

# Redis Configuration
redis:
  enabled: false  # Using external ElastiCache
  external:
    enabled: true
    host: "cloudforge-redis.abc123.cache.amazonaws.com"
    port: 6379
    existingSecret: "cloudforge-redis-secret"
    existingSecretPasswordKey: "auth-token"

# Security Configuration
security:
  podSecurityPolicy:
    enabled: true
  networkPolicy:
    enabled: true
  serviceAccount:
    create: true
    annotations:
      eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/cloudforge-service-role"
  
  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    fsGroup: 1001
    seccompProfile:
      type: RuntimeDefault
  
  containerSecurityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    runAsNonRoot: true
    runAsUser: 1001
    capabilities:
      drop:
        - ALL

# Monitoring Configuration
monitoring:
  enabled: true
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
      interval: 30s
      scrapeTimeout: 10s
  
  grafana:
    enabled: true
    adminPassword: "admin123!"
    dashboards:
      enabled: true
      configMapName: "cloudforge-dashboards"
  
  alerts:
    enabled: true
    rules:
      - name: "high-cpu-usage"
        threshold: 80
      - name: "high-memory-usage"
        threshold: 85
      - name: "high-error-rate"
        threshold: 5

# Backup Configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: "30d"
  storage:
    type: "s3"
    bucket: "cloudforge-backups"
    region: "us-east-1"

# Logging Configuration
logging:
  enabled: true
  level: "info"
  format: "json"
  destination: "stdout"
  
  fluentd:
    enabled: true
    elasticsearch:
      host: "elasticsearch.logging.svc.cluster.local"
      port: 9200
      index: "cloudforge-logs"

# Performance Configuration
performance:
  caching:
    enabled: true
    ttl: 3600
  
  compression:
    enabled: true
    level: 6
  
  keepAlive:
    enabled: true
    timeout: 65

# High Availability Configuration
highAvailability:
  enabled: true
  podDisruptionBudget:
    enabled: true
    minAvailable: 2
  
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                    - cloudforge-platform
            topologyKey: kubernetes.io/hostname

# Resource Quotas
resourceQuota:
  enabled: true
  hard:
    requests.cpu: "50"
    requests.memory: "100Gi"
    limits.cpu: "100"
    limits.memory: "200Gi"
    persistentvolumeclaims: "50"

# Network Policies
networkPolicy:
  enabled: true
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 3000
  egress:
    - to: []
      ports:
        - protocol: TCP
          port: 5432  # PostgreSQL
        - protocol: TCP
          port: 6379  # Redis
        - protocol: TCP
          port: 443   # HTTPS
        - protocol: TCP
          port: 53    # DNS
        - protocol: UDP
          port: 53    # DNS
